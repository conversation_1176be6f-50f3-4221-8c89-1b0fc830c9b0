#!/bin/bash

# Voice Assistant Nginx Setup Script
# This script sets up nginx configuration for the voice assistant application

set -e

echo "🚀 Setting up Nginx for Voice Assistant..."

# Check if nginx is installed
if ! command -v nginx &> /dev/null; then
    echo "❌ Nginx is not installed. Installing..."
    sudo apt-get update
    sudo apt-get install -y nginx
fi

# Backup existing nginx configuration
BACKUP_DIR="/etc/nginx/backup-$(date +%Y%m%d-%H%M%S)"
echo "📦 Creating backup of existing nginx configuration at $BACKUP_DIR"
sudo mkdir -p "$BACKUP_DIR"
sudo cp -r /etc/nginx/* "$BACKUP_DIR/" 2>/dev/null || true

# Copy our configuration
echo "📝 Installing voice assistant nginx configuration..."
sudo cp nginx.conf /etc/nginx/sites-available/voice-assistant

# Disable default site if it exists
if [ -f "/etc/nginx/sites-enabled/default" ]; then
    echo "🔄 Disabling default nginx site..."
    sudo rm -f /etc/nginx/sites-enabled/default
fi

# Enable our site
echo "✅ Enabling voice assistant site..."
sudo ln -sf /etc/nginx/sites-available/voice-assistant /etc/nginx/sites-enabled/

# Test nginx configuration
echo "🧪 Testing nginx configuration..."
if sudo nginx -t; then
    echo "✅ Nginx configuration is valid"
else
    echo "❌ Nginx configuration test failed!"
    echo "📦 Restoring backup configuration..."
    sudo rm -f /etc/nginx/sites-enabled/voice-assistant
    sudo rm -f /etc/nginx/sites-available/voice-assistant
    exit 1
fi

# Reload nginx
echo "🔄 Reloading nginx..."
sudo systemctl reload nginx

# Enable nginx to start on boot
echo "🔧 Enabling nginx to start on boot..."
sudo systemctl enable nginx

# Show status
echo "📊 Nginx status:"
sudo systemctl status nginx --no-pager

echo ""
echo "🎉 Nginx setup completed successfully!"
echo ""
echo "📋 Configuration Summary:"
echo "  Main Application: http://your-server/ (proxy to :10000)"
echo "  WebSocket Endpoint: ws://your-server/media-stream"
echo "  API Endpoints: http://your-server/api/"
echo "  Reports: http://your-server/reports/"
echo "  Service 8080: http://your-server/service-8080/"
echo "  Service 8081: http://your-server/external-reports/"
echo "  Service 8009: http://your-server/service-8009/"
echo "  Service 8090: http://your-server/service-8090/"
echo "  Health Check: http://your-server/health"
echo ""
echo "🔧 Next Steps:"
echo "  1. Make sure all your services are running on their respective ports"
echo "  2. Test the admin interface: http://your-server/admin/"
echo "  3. Verify WebSocket connection for Twilio: ws://your-server/media-stream"
echo "  4. Update your Twilio webhook URLs if needed"
echo ""
echo "🐛 Troubleshooting:"
echo "  - Check nginx error logs: sudo tail -f /var/log/nginx/error.log"
echo "  - Check access logs: sudo tail -f /var/log/nginx/access.log"
echo "  - Verify services are running: netstat -tlnp | grep -E ':(8009|8080|8081|8090|10000)'"
echo "  - Test configuration: sudo nginx -t"
echo ""