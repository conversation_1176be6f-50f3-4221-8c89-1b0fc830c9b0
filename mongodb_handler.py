import os
import time
import json
import logging as log
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

# MongoDB Configuration
MONGO_URI = os.getenv("MONGO_URI", "****************************************")
DEFAULT_CLIENT_DB = os.getenv("DEFAULT_CLIENT_DB", "Client_Activities")
MONGO_COLLECTION = os.getenv(
    "MONGO_COLLECTION", "voice-api"
)  # Legacy - now using email-based collections

# Feature flag to enable/disable MongoDB
MONGODB_ENABLED = os.getenv("MONGODB_ENABLED", "true").lower() in ("true", "1", "yes", "on")


class MongoDBHandler:
    """Handles all MongoDB operations for the voice assistant application."""
    
    def __init__(self):
        self.mongo_uri = MONGO_URI
        self.default_db = DEFAULT_CLIENT_DB
        self.collection_base = MONGO_COLLECTION
        self.enabled = MONGODB_ENABLED
        self._client = None
        
    def is_enabled(self) -> bool:
        """Check if MongoDB is enabled via feature flag."""
        return self.enabled
        
    def connect(self) -> bool:
        """Establish connection to MongoDB."""
        if not self.enabled:
            log.info("MongoDB is disabled via feature flag")
            return False
            
        try:
            self._client = MongoClient(self.mongo_uri, serverSelectionTimeoutMS=5000)
            # Test connection
            server_info = self._client.server_info()
            log.info(f"Connected to MongoDB server version: {server_info.get('version')}")
            log.info(f"Using database: {self.default_db}, collection base: {self.collection_base}")
            return True
        except Exception as e:
            log.error(f"MongoDB connection failed: {e}")
            self._client = None
            return False
    
    def disconnect(self):
        """Close MongoDB connection."""
        if self._client:
            self._client.close()
            self._client = None
            log.info("MongoDB connection closed")
    
    def test_connection(self) -> Dict[str, Any]:
        """Test MongoDB connection and return status."""
        result = {"status": "unknown", "enabled": self.enabled}
        
        if not self.enabled:
            result.update({
                "status": "disabled",
                "message": "MongoDB is disabled via MONGODB_ENABLED environment variable"
            })
            return result
            
        try:
            mongo_client = MongoClient(self.mongo_uri, serverSelectionTimeoutMS=2000)
            server_info = mongo_client.server_info()
            mongo_client.close()
            
            result.update({
                "status": "connected",
                "uri": self.mongo_uri[:20] + "...",
                "db": self.default_db,
                "version": server_info.get('version')
            })
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            result.update({
                "status": "error",
                "message": f"Connection error: {str(e)}"
            })
        except Exception as e:
            result.update({
                "status": "error",
                "message": str(e)
            })
            
        return result
    
    def save_transcript_legacy(
        self, 
        transcript_document: Dict[str, Any],
        client_email: Optional[str] = None
    ) -> bool:
        """Save transcript using legacy approach (monthly collections)."""
        if not self.enabled:
            log.info("MongoDB disabled - skipping transcript save")
            return False
            
        try:
            current_time = datetime.now()
            collection_name = f"{self.collection_base}_{current_time.strftime('%Y%m')}"
            
            mongo_client = MongoClient(self.mongo_uri)
            db = mongo_client[self.default_db if client_email else "voice_assistant"]
            collection = db[collection_name]
            
            result = collection.insert_one(transcript_document)
            mongo_client.close()
            
            log.info(f"Transcript saved to legacy collection. Document ID: {result.inserted_id}")
            log.info(f"Collection: {collection_name}")
            return True
            
        except Exception as e:
            log.error(f"Error saving to legacy MongoDB: {e}")
            return False
    
    def update_transcript_with_report_info(
        self,
        call_identifier: str,
        report_path: str,
        report_gridfs_id: Optional[str] = None,
        client_name: Optional[str] = None,
        vehicle_info: Optional[str] = None,
        client_email: Optional[str] = None
    ) -> bool:
        """Update existing transcript document with report information."""
        if not self.enabled:
            return False
            
        try:
            current_time = datetime.now()
            collection_name = f"{self.collection_base}_{current_time.strftime('%Y%m')}"
            
            mongo_client = MongoClient(self.mongo_uri)
            db = mongo_client[self.default_db if client_email else "voice_assistant"]
            collection = db[collection_name]
            
            update_data = {"report_path": report_path}
            if report_gridfs_id:
                update_data["report_gridfs_id"] = report_gridfs_id
            if client_name:
                update_data["customer_name"] = client_name
            if vehicle_info:
                update_data["vehicle_info"] = vehicle_info
            
            collection.update_one(
                {"call_identifier": call_identifier},
                {"$set": update_data}
            )
            
            mongo_client.close()
            log.info("Updated legacy MongoDB document with report information")
            return True
            
        except Exception as e:
            log.error(f"Error updating legacy MongoDB document: {e}")
            return False
    
    def save_transcript_to_file_fallback(
        self, 
        transcript_document: Dict[str, Any], 
        call_identifier: str
    ) -> bool:
        """Save transcript to local file as fallback when MongoDB is unavailable."""
        try:
            with open(f"transcripts/transcript_{call_identifier}.json", "w") as f:
                json.dump(transcript_document, f, indent=2, default=str)
            log.info(f"Fallback: Saved transcript locally as transcript_{call_identifier}.json")
            return True
        except Exception as e:
            log.error(f"Error saving transcript locally: {e}")
            return False


# Module-level functions for easy importing
def get_mongodb_handler() -> MongoDBHandler:
    """Get a MongoDB handler instance."""
    return MongoDBHandler()


def test_mongodb_connection() -> Dict[str, Any]:
    """Test MongoDB connection and return status."""
    handler = MongoDBHandler()
    return handler.test_connection()


def is_mongodb_enabled() -> bool:
    """Check if MongoDB is enabled via feature flag."""
    return MONGODB_ENABLED


def save_transcript(
    transcript_document: Dict[str, Any],
    call_identifier: str,
    client_email: Optional[str] = None,
    use_client_manager: bool = True
) -> bool:
    """
    Save transcript using the best available method.
    
    Args:
        transcript_document: The transcript data to save
        call_identifier: Unique identifier for the call
        client_email: Client email for client-centric storage
        use_client_manager: Whether to try client data manager first
        
    Returns:
        True if saved successfully, False otherwise
    """
    handler = MongoDBHandler()
    
    if not handler.is_enabled():
        return handler.save_transcript_to_file_fallback(transcript_document, call_identifier)
    
    # Try client data manager first if available and requested
    if use_client_manager and client_email:
        try:
            from client_data_manager import ClientDataManager
            client_data_manager = ClientDataManager(MONGO_URI, DEFAULT_CLIENT_DB)
            
            if not client_data_manager.client:
                client_data_manager.connect()
            
            success = client_data_manager.add_transcript(client_email, transcript_document)
            if success:
                log.info(f"Transcript saved to client collection for {client_email}")
                return True
        except Exception as e:
            log.error(f"Error using client data manager: {e}")
    
    # Fallback to legacy approach
    success = handler.save_transcript_legacy(transcript_document, client_email)
    if success:
        return True
    
    # Final fallback to local file
    return handler.save_transcript_to_file_fallback(transcript_document, call_identifier)


def update_transcript_with_report(
    call_identifier: str,
    report_path: str,
    report_gridfs_id: Optional[str] = None,
    client_name: Optional[str] = None,
    vehicle_info: Optional[str] = None,
    client_email: Optional[str] = None
) -> bool:
    """Update transcript with report information."""
    handler = MongoDBHandler()
    
    if not handler.is_enabled():
        log.info("MongoDB disabled - skipping transcript update")
        return False
    
    return handler.update_transcript_with_report_info(
        call_identifier, report_path, report_gridfs_id, 
        client_name, vehicle_info, client_email
    )