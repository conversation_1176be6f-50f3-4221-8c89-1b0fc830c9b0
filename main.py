import asyncio
import base64
import functools
import inspect
import json
import logging as log
import os
import sys
import time
import traceback
from asyncio import Lock
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

import httpx
import websockets
from dotenv import load_dotenv
from fastapi import FastAPI, HTTPException, Request, Response, WebSocket, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.websockets import WebSocketDisconnect

# Only import the TwiML components needed for responding to incoming calls
from twilio.twiml.voice_response import Connect, Hangup, Say, Stream, VoiceResponse

from client_api import router as client_api_router
from client_data_manager import ClientDataManager
from mongodb_handler import (
    get_mongodb_handler,
    is_mongodb_enabled,
    save_transcript,
    update_transcript_with_report,
)
from mongodb_handler import (
    test_mongodb_connection as test_mongo,
)
from scripts.report_ui import router as reports_router

load_dotenv()

# Configure logging to stdout with proper formatting
log.basicConfig(
    level=log.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        log.StreamHandler(sys.stdout)  # Force all logs to stdout
    ],
    force=True,  # Override any existing logging configuration
)

# Create a logger instance
logger = log.getLogger(__name__)

# Test logging configuration
log.info("🚀 Voice Assistant logging initialized - all logs will be sent to stdout")
log.info(f"📊 Log level set to: {log.getLevelName(logger.getEffectiveLevel())}")

# Ensure transcript directory exists
transcripts_dir = os.path.join(
    os.path.dirname(os.path.abspath(__file__)), "transcripts"
)
os.makedirs(transcripts_dir, exist_ok=True)

# Configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
PORT = int(os.getenv("PORT", 10000))
host = os.getenv("HOST", "localhost")

# MongoDB Configuration (moved to mongodb_handler.py)
# Import MongoDB configuration from handler
from mongodb_handler import DEFAULT_CLIENT_DB, MONGO_COLLECTION, MONGO_URI

# Report API Configuration (external API endpoint - set to "disabled" to use only local database)
REPORT_APP_URI = os.getenv(
    "REPORT_APP_URI", "disabled"  # Changed default to disabled to prioritize local reports database
)

# Reports database configuration (for local dashboard)
REPORTS_MONGO_URI = os.getenv(
    "REPORTS_MONGO_URI", "*******************************************************************************"
)
REPORTS_DB_NAME = os.getenv("REPORTS_DB_NAME", "inspection_reports")
REPORTS_COLLECTION_NAME = os.getenv("REPORTS_COLLECTION_NAME", "inspections")

# Initialize Client Data Manager with flexible database name
client_data_manager = ClientDataManager(MONGO_URI, DEFAULT_CLIENT_DB)


# Debug Decorator for Deep Function Visibility
def deep_debug(
    log_args=True,
    log_return=True,
    log_duration=True,
    log_exceptions=True,
    max_arg_length=500,
    max_return_length=500,
    sensitive_params=None,
):
    """
    Comprehensive debug decorator that provides deep visibility into function execution.

    Args:
        log_args: Whether to log function arguments
        log_return: Whether to log function return values
        log_duration: Whether to log execution duration
        log_exceptions: Whether to log exceptions with full traceback
        max_arg_length: Maximum length for argument values in logs
        max_return_length: Maximum length for return values in logs
        sensitive_params: List of parameter names to mask (e.g., ['password', 'token'])

    Usage:
        @deep_debug()
        def my_function():
            pass

        @deep_debug(log_args=False, sensitive_params=['api_key'])
        async def sensitive_function(api_key, data):
            pass
    """
    if sensitive_params is None:
        sensitive_params = ["password", "token", "key", "secret", "auth"]

    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            func_name = f"{func.__module__}.{func.__qualname__}"
            start_time = time.time()

            # Generate unique call ID for tracking
            call_id = f"{func_name}_{int(start_time * 1000000) % 1000000}"

            try:
                # Log function entry
                log.info(f"🚀 [ENTRY] {func_name} (ID: {call_id})")

                # Log arguments if enabled
                if log_args:
                    # Get function signature
                    sig = inspect.signature(func)
                    bound_args = sig.bind(*args, **kwargs)
                    bound_args.apply_defaults()

                    # Process arguments for logging
                    arg_info = {}
                    for param_name, value in bound_args.arguments.items():
                        if any(
                            sensitive in param_name.lower()
                            for sensitive in sensitive_params
                        ):
                            arg_info[param_name] = "***MASKED***"
                        else:
                            # Truncate long values
                            str_value = str(value)
                            if len(str_value) > max_arg_length:
                                arg_info[param_name] = (
                                    str_value[:max_arg_length] + "...[TRUNCATED]"
                                )
                            else:
                                arg_info[param_name] = str_value

                    log.info(
                        f"📥 [ARGS] {func_name} (ID: {call_id}) - Arguments: {json.dumps(arg_info, indent=2, default=str)}"
                    )

                # Execute the function
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)

                # Calculate execution time
                end_time = time.time()
                duration = end_time - start_time

                # Log return value if enabled
                if log_return:
                    if result is not None:
                        str_result = str(result)
                        if len(str_result) > max_return_length:
                            truncated_result = (
                                str_result[:max_return_length] + "...[TRUNCATED]"
                            )
                        else:
                            truncated_result = str_result
                        log.info(
                            f"📤 [RETURN] {func_name} (ID: {call_id}) - Result: {truncated_result}"
                        )
                    else:
                        log.info(
                            f"📤 [RETURN] {func_name} (ID: {call_id}) - Result: None"
                        )

                # Log duration if enabled
                if log_duration:
                    log.info(
                        f"⏱️ [DURATION] {func_name} (ID: {call_id}) - Execution time: {duration:.4f}s"
                    )

                # Log successful exit
                log.info(f"✅ [EXIT] {func_name} (ID: {call_id}) - Success")

                return result

            except Exception as e:
                end_time = time.time()
                duration = end_time - start_time

                # Log exception details if enabled
                if log_exceptions:
                    log.error(
                        f"❌ [EXCEPTION] {func_name} (ID: {call_id}) - {type(e).__name__}: {str(e)}"
                    )
                    log.error(
                        f"🔍 [TRACEBACK] {func_name} (ID: {call_id}) - Full traceback:"
                    )
                    log.error(traceback.format_exc())

                # Log duration even for failed calls
                if log_duration:
                    log.error(
                        f"⏱️ [DURATION] {func_name} (ID: {call_id}) - Failed after: {duration:.4f}s"
                    )

                # Log failed exit
                log.error(f"💥 [EXIT] {func_name} (ID: {call_id}) - Failed")

                # Re-raise the exception
                raise

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            func_name = f"{func.__module__}.{func.__qualname__}"
            start_time = time.time()

            # Generate unique call ID for tracking
            call_id = f"{func_name}_{int(start_time * 1000000) % 1000000}"

            try:
                # Log function entry
                log.info(f"🚀 [ENTRY] {func_name} (ID: {call_id})")

                # Log arguments if enabled
                if log_args:
                    # Get function signature
                    sig = inspect.signature(func)
                    bound_args = sig.bind(*args, **kwargs)
                    bound_args.apply_defaults()

                    # Process arguments for logging
                    arg_info = {}
                    for param_name, value in bound_args.arguments.items():
                        if any(
                            sensitive in param_name.lower()
                            for sensitive in sensitive_params
                        ):
                            arg_info[param_name] = "***MASKED***"
                        else:
                            # Truncate long values
                            str_value = str(value)
                            if len(str_value) > max_arg_length:
                                arg_info[param_name] = (
                                    str_value[:max_arg_length] + "...[TRUNCATED]"
                                )
                            else:
                                arg_info[param_name] = str_value

                    log.info(
                        f"📥 [ARGS] {func_name} (ID: {call_id}) - Arguments: {json.dumps(arg_info, indent=2, default=str)}"
                    )

                # Execute the function
                result = func(*args, **kwargs)

                # Calculate execution time
                end_time = time.time()
                duration = end_time - start_time

                # Log return value if enabled
                if log_return:
                    if result is not None:
                        str_result = str(result)
                        if len(str_result) > max_return_length:
                            truncated_result = (
                                str_result[:max_return_length] + "...[TRUNCATED]"
                            )
                        else:
                            truncated_result = str_result
                        log.info(
                            f"📤 [RETURN] {func_name} (ID: {call_id}) - Result: {truncated_result}"
                        )
                    else:
                        log.info(
                            f"📤 [RETURN] {func_name} (ID: {call_id}) - Result: None"
                        )

                # Log duration if enabled
                if log_duration:
                    log.info(
                        f"⏱️ [DURATION] {func_name} (ID: {call_id}) - Execution time: {duration:.4f}s"
                    )

                # Log successful exit
                log.info(f"✅ [EXIT] {func_name} (ID: {call_id}) - Success")

                return result

            except Exception as e:
                end_time = time.time()
                duration = end_time - start_time

                # Log exception details if enabled
                if log_exceptions:
                    log.error(
                        f"❌ [EXCEPTION] {func_name} (ID: {call_id}) - {type(e).__name__}: {str(e)}"
                    )
                    log.error(
                        f"🔍 [TRACEBACK] {func_name} (ID: {call_id}) - Full traceback:"
                    )
                    log.error(traceback.format_exc())

                # Log duration even for failed calls
                if log_duration:
                    log.error(
                        f"⏱️ [DURATION] {func_name} (ID: {call_id}) - Failed after: {duration:.4f}s"
                    )

                # Log failed exit
                log.error(f"💥 [EXIT] {func_name} (ID: {call_id}) - Failed")

                # Re-raise the exception
                raise

        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


# Prompts
SYSTEM_MESSAGE = """
[You are the Workforce AI Assistant for a Repair Shop that helps document vehicle inspections. Always introduce yourself as "Workforce AI Assistant" when greeting callers. Speak in a brief, methodical, and formal tone. Ask only one question at a time. Do not say thank you, do not acknowledge responses. Be direct, efficient, and emotionless in delivery—like a protocol officer executing an audit. Do not use conversational filler or pleasantries.]

[SESSION START]:
[1. State your identity briefly: "Workforce AI Assistant."]
[2. Ask for preferred language: "English or Spanish?" Adapt accordingly.]
3. FIRST, you MUST collect the following client and vehicle information:
   - "What is the unit number?"
   - "What is the maker and model?"
   - "What is the client's name?"
   - "What is the client's email address?"
   - "What does the odometer reading say?"

[INSPECTION TYPE DETERMINATION]:
[After collecting the maker and model information, you MUST automatically determine the inspection type based on the vehicle specifications and announce it. Use this logic:]
- Light-Duty Vehicle: Personal cars, pickup trucks, SUVs, vans under 10,000 lbs
- Power-Unit/Tractor: Semi-trucks, tractors, commercial trucks over 26,000 lbs
- Trailer: Semi-trailers, cargo trailers, flatbeds
- Liftgate/Forklift: Forklifts, scissor lifts, boom lifts, aerial equipment
- Hy-Rail/Heavy-Duty: Rail equipment, heavy construction vehicles
- General DOT Commercial Vehicle: Commercial vehicles 10,001-26,000 lbs

[Once you determine the type, announce: "We will be doing a [TYPE] inspection" and immediately proceed to the appropriate checklist.]

[INSPECTION TYPE BRANCHING]:
[Based on your determination, you MUST follow the appropriate regulatory inspection branch:]

**FOR LIGHT-DUTY VEHICLE**
[Follow State Safety/Emissions Inspection standards. Ask each item individually.]
- Cab & Body:
  • Door locks
  • Door latches
  • Door hinges
  • Windshield condition
  • Mirrors
  • Wipers
  • Ignition switch
  • Accessory switches
  • Dashboard gauges
  • Dashboard indicators
  • Horn
  • Interior lights
  • Heater operation
  • A-C operation
  • Defroster
  • Seat belt condition
  • Seat belt latches
  • Body panels damage
  • Body corrosion
  • Inspection stickers expiry
- Electrical & Lighting:
  • Headlights
  • Stop lights
  • Turn signals
  • Marker lights
  • Wiring harness integrity
  • Battery condition
  • Charging system test
- Engine & Powertrain:
  • Fluid levels
  • Fluid leaks
  • Engine oil condition
  • Filter change status
  • Engine operation
  • Engine idle quality
  • Engine noise
  • Transmission shift quality
  • Transmission leaks
- Chassis:
  • Service brakes
  • Parking brakes
  • Tire tread depth
  • Tire pressure
  • Wheel damage
  • Springs condition
  • Shocks condition
  • Mounts condition
  • Steering free-play
  • Linkage wear
- Fuel & Exhaust:
  • Fuel tank condition
  • Fuel lines
  • Evap system leaks
  • Exhaust leaks
  • Exhaust hangers

**FOR POWER-UNIT/TRACTOR/HOSTLER:**
[Follow FMCSA Periodic (Annual) Inspection per 49 CFR 396.17/396.21. Ask each item individually.]
- Cab & Sleeper:
  • Seat belt condition
  • Seat belt latch
  • Seat belt retractor
  • Switches operation
  • Gauges function
  • Warning lamps
  • Sleeper compartment egress
  • Fire extinguisher presence
  • Sleeper lighting
  • Cab climate controls
  • Bunk climate controls
- Electrical & Lighting:
  • Headlights
  • Stop lights
  • Tail lights
  • Turn signals
  • Marker lights
  • ID lights
  • ABS warning lamp cycles
  • ABS warning lamp clears
  • Battery cables
  • Battery terminals
  • Battery hold-downs
- Engine & Drive:
  • Oil levels
  • Coolant levels
  • Oil leaks
  • Coolant leaks
  • Air filter restriction
  • Piping secure
  • Engine mounts cracks
  • Loose fasteners
  • Clutch free play
  • Clutch engagement
  • Transmission condition
  • Driveline U-joints
  • Carrier bearings
  • Transmission leaks
- Chassis:
  • Steering box mounts secure
  • Steering box cracks
  • Drag link wear
  • Tie-rod ends wear
  • Pitman arm wear
  • Power steering pump condition
  • Power steering hoses
  • Service brakes lining thickness
  • Drums condition
  • Rotors condition
  • Air system compressor
  • Air system governor
  • Air leak test single
  • Air leak test combo
  • Low air warning function
  • Suspension springs
  • Cracked leaves
  • Air bags condition
  • Air bag mounts
  • Torque rods
  • Bushings condition
  • Tire tread depth steer
  • Tire tread depth others
  • Air pressure OEM spec
  • Wheel rims cracks
  • Missing lugs
- Fifth Wheel & Rear:
  • Slider lock pins
  • Kingpin wear
  • Mud flaps present
  • Mud flaps secure
- Fuel & Exhaust:
  • Fuel tanks condition
  • Tank mounts
  • Tank straps
  • Fuel leaks
  • DPF system condition
  • DEF system leaks
  • DEF level
  • Exhaust leaks
  • Soot at joints

**FOR TRAILER:**
[Follow FMCSA Periodic (Annual) Inspection per 49 CFR 396.17/396.21 for trailers. Ask each item individually.]
- Chassis & Frame:
  • Main rails cracks
  • Main rails buckles
  • Crossmembers rust-out
  • Missing crossmembers
  • Landing gear raises
  • Landing gear lowers
  • Landing gear legs straight
  • Kingpin condition
  • Upper coupler plate secure
- Brakes:
  • Service brake shoes thickness
  • Brake pads thickness
  • Oil saturation check
  • Air lines service
  • Emergency glad-hands
  • Seals condition
  • ABS warning lamp cycles
- Suspension:
  • Springs condition
  • Air leaf cracks
  • Air-bag leaks
  • Torque arms bushings
  • Suspension play
- Tires & Wheels:
  • Tread depth measurement
  • Sidewalls bulges
  • Exposed ply
  • Wheel rims cracks
  • Lug nuts present
- Lights & Electrical:
  • Marker lights amber
  • Marker lights red
  • ID lights
  • Clearance lights
  • Stop lamps
  • Tail lamps
  • Turn signals
  • Brake lamps
  • 7-pin harness condition
  • Cable abrasion
  • Proper voltage
- Cargo & Body:
  • Doors operation
  • Hinges condition
  • Latches operation
  • Seals condition
  • Floor rot
  • Floor holes
  • Roof panels intact
  • Roof leaks

**FOR LIFTGATE/FORKLIFT/DOLLY:**
[Follow OSHA 29 CFR 1910.178 Pre-Operation Checklist. Ask each item individually.] 
- Structural:
  • Platform surface bends
  • Platform rust-through
  • Critical welds intact
  • Hinges play
  • Pins play
- Hydraulics:
  • Cylinders leaks
  • Rod chrome condition
  • Hoses abrasion
  • Hose bubbles
  • Pump operation
  • Motor amp draw
- Electrical:
  • Up switch function
  • Down switch function
  • Interlock functions
  • Wiring insulation
- Safety Devices:
  • Warning decals legible
  • Auto stops function
  • Platform returns flush
  • Safety chains intact
  • Chain latches

**FOR HY-RAIL/HEAVY-DUTY TRUCK:**
[Follow FRA §214.523 Annual Inspection + FMCSA Periodic. Ask each item individually.]
- Hy-rail Gear:
  • Front gear deploy raises
  • Front gear deploy lowers
  • Rear gear deploy raises
  • Rear gear deploy lowers
  • Lock pins engage
  • Pivot lubrication
  • Grease points serviced
  • Guide wheels flange wear
  • Rail brake engages
  • Rail brake releases
  • Hydraulic fluid leaks
- Crane (if equipped):
  • Boom structure cracks
  • Hydraulic cylinders
  • Hydraulic hoses
  • Wire rope strands
  • Outriggers deploy
  • Outriggers hold load
- Chassis:
  • Brake linings thickness
  • Brake wear even
  • Air system compressor
  • Air leak test
  • Steering linkage play
  • Power fluid level
  • Power fluid leaks
  • Suspension springs
  • Air-bags condition
  • Suspension mounts
  • Tire tread depth
  • Wheel condition
  • Lug nuts tight
- Electrical & Lighting:
  • Headlights
  • Tail lights
  • Turn signals
  • Marker lights
  • Work lights
  • Rail work-lights
  • ABS indicator cycles
  • ABS indicator clears

**FOR GENERAL DOT COMMERCIAL VEHICLE:**
[Follow FMCSA Periodic (Annual) Inspection Report per 49 CFR 396.17/396.21. Ask each item individually.]
- Brake System:
  • Service brake components present
  • Service brakes operative
  • Parking brake holds grade
  • Air leak rate test
  • Low-air warning function
  • Air warning activation pressure
- Steering Mechanism:
  • Steering wheel free-play
  • Kingpins condition
  • Drag link wear
  • Tie-rod ends condition
  • Power steering fluid leaks
- Lighting Devices:
  • Headlamps high beams
  • Headlamps low beams
  • Stop lamps illuminate
  • Brake lamps function
  • Turn signals left
  • Turn signals right
  • Signal flash rate
  • Marker lights amber
  • Marker lights red
  • Clearance lights
  • ID lights
- Tires:
  • Steer axle tread depth
  • Other axles tread depth
  • Sidewalls bulges
  • Exposed cords
  • Dual tire spacing
- Emergency Equipment:
  • Fire extinguisher rating
  • Fire extinguisher charged
  • Fire extinguisher secured
  • Warning triangles present
  • Spare fuses available
  • Circuit-breakers available

[INSPECTION EXECUTION GUIDANCE: Once you've identified the vehicle type, state: "Beginning inspection protocol." Then work through each category systematically. For each bullet point (•), ask about that ONE SPECIFIC ITEM using minimal words, WAIT FOR COMPLETE RESPONSE, then move to the next item. CRITICAL: You must receive an answer before asking the next question. Never ask about multiple items in the same question.]

[POST-VEHICLE-TYPE INSTRUCTION]:
1. Once you determine the vehicle type from the maker and model, announce: "We will be doing a [TYPE] inspection" and explain which regulatory standard applies.
2. You MUST then follow the specific inspection checklist for that vehicle type as outlined above.
3. Work through each section systematically, asking about ONE ITEM AT A TIME.
4. MANDATORY: Wait for the technician's complete response before asking the next question. Do not acknowledge. Move directly to next question only AFTER receiving response.
5. If a response is unclear or incomplete, repeat the question.
6. Some checks might be conditional (e.g., "if equipped..."). If the technician indicates it's not applicable, acknowledge and move on.
7. Do not switch to a different inspection type mid-conversation unless the technician explicitly requests to start over.

[CONVERSATION APPROACH]:
- Ask about ONE specific bullet point (•) at a time, never multiple items together.
- Use direct questions like: "[Item]?" or "[Item] status?" 
- CRITICAL: Wait for complete response before proceeding. Do not acknowledge. Only move to next item AFTER receiving answer.
- Ask only what is necessary. No explanations or context.
- Maintain steady pace. No conversational transitions.
- Continue until ALL applicable information from the chosen inspection type has been collected.
- No empathy, patience displays, or encouragement needed.

[VOICE CHARACTERISTICS]: Maintain a neutral, professional tone. Speak at normal pace. No emotional inflection. No humor, laughs, or affirmative sounds. Deliver each question with uniform cadence and volume.

[STRICT CONVERSATION BOUNDARIES]: 
- ONLY discuss vehicle inspection topics
- NEVER share personal information, experiences, or stories unrelated to inspections
- NEVER mention promotions, personal life, work events, or off-topic subjects
- NEVER start additional inspection categories after completing the determined vehicle type
- If user asks "what else" or similar after completion, do not respond - hang up immediately
- If the conversation drifts off-topic, politely redirect: "Let's focus on the vehicle inspection. What's the next item we need to check?"
- Stay strictly within your role as a vehicle inspection assistant

[CLOSING]: Once you've completed ALL inspection items for the chosen vehicle type, immediately state: "Thank you, we are done here." Then hang up the call. Do not wait for response. Do not ask additional questions. Do not start other categories.

[REGULATORY COMPLIANCE NOTE]: Each vehicle type follows specific federal or state regulations:
- Light-Duty: State Safety/Emissions standards (varies by state)
- Power-Unit/Tractor: FMCSA 49 CFR 396.17/396.21 (DOT)
- Trailer: FMCSA 49 CFR 396.17/396.21 (DOT)
- Liftgate/Forklift: OSHA 29 CFR 1910.178 (Dept. of Labor)
- Hy-Rail: FRA 49 CFR 214.523 + FMCSA periodic (DOT)
- General DOT Commercial Vehicle: FMCSA 49 CFR 396.17/396.21 (DOT)

Ensure all questions align with the appropriate regulatory requirements for the identified vehicle type.
"""


VOICE = "sage"
LOG_EVENT_TYPES = [
    "error",
    "response.content.done",
    "rate_limits.updated",
    "response.done",
    "input_audio_buffer.committed",
    "input_audio_buffer.speech_stopped",
    "input_audio_buffer.speech_started",
    "session.created",
]
SHOW_TIMING_MATH = False

# Transcript-related event types
SPEECH_STOPPED_EVENT = "input_audio_buffer.speech_stopped"
AUDIO_TRANSCRIPT_DELTA_EVENT = "response.audio_transcript.delta"
AUDIO_TRANSCRIPT_DONE_EVENT = "response.audio_transcript.done"
RESPONSE_DONE_EVENT = "response.done"

# Farewell phrases that indicate the user wants to end the call
FAREWELL_PHRASES = [
    "goodbye",
    "bye",
    "see you",
    "talk to you later",
    "have a good day",
    "have a nice day",
    "end the call",
    "hang up",
    "that's all",
    "we're done",
    "that will be all",
    "thank you for your help",
    "thanks for your help",
]
SHOW_TIMING_MATH = False

app = FastAPI()

# Global lock to prevent concurrent responses
response_lock = Lock()

# Mount static files and templates
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For production, specify exact domains
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Webhook logs
def loghook(str) -> None:
    import requests

    requests.post(
        "*********************************************************************************",
        json={"text": "LOG API: " + str},
    )


# Include the reports router
app.include_router(reports_router)

# Include the client API router
app.include_router(client_api_router)


# Global exception handler for HTTP requests
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    error_id = f"err-{int(time.time())}"
    print(f"[ERROR {error_id}] Unhandled exception in {request.url.path}: {str(exc)}")
    print(traceback.format_exc())

    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error_id": error_id,
            "message": "An unexpected error occurred. Please try again later.",
            "path": request.url.path,
        },
    )


# Only check for API key in production mode, not during testing
if not OPENAI_API_KEY and "pytest" not in sys.modules:
    raise ValueError("Missing the OpenAI API key. Please set it in the .env file.")


@app.get("/health", response_class=JSONResponse)
async def health_check():
    """
    Health check endpoint for monitoring.
    """
    return {"status": "ok", "message": "Voice Assistant API is running"}


@app.get("/media-format", response_class=JSONResponse)
async def media_format():
    """
    Returns the current audio format configuration for diagnostic purposes.
    """
    session_settings = {
        "input_audio_format": "g711_ulaw",  # Fixed to ensure compatibility with Twilio
        "output_audio_format": "g711_ulaw",
        "voice": VOICE,
        "hostname": host,
        "websocket_url": f"wss://{host}/media-stream",
    }
    return session_settings


@app.get("/", response_class=JSONResponse)
async def index_page():
    """
    Returns a JSON response with server status and client statistics.
    """
    try:
        # Try to get client statistics
        if not client_data_manager.client:
            client_data_manager.connect()

        stats = client_data_manager.get_database_stats()
        client_count = len(client_data_manager.list_all_clients())

        return {
            "message": "Twilio Media Stream Server is running!",
            "version": "2.0 - Client-Centric Architecture",
            "database": {
                "name": DEFAULT_CLIENT_DB,
                "total_clients": client_count,
                "stats": stats,
            },
            "endpoints": {
                "health": "/health",
                "client_api": "/api/clients",
                "reports": "/reports",
                "media_stream": "/media-stream",
                "incoming_call": "/incoming-call",
            },
        }
    except Exception as e:
        return {
            "message": "Twilio Media Stream Server is running!",
            "version": "2.0 - Client-Centric Architecture",
            "database_status": f"Error: {str(e)}",
            "endpoints": {
                "health": "/health",
                "client_api": "/api/clients",
                "reports": "/reports",
                "media_stream": "/media-stream",
                "incoming_call": "/incoming-call",
            },
        }


@app.get("/connection-test", response_class=JSONResponse)
async def connection_test():
    """
    Test the connection to both OpenAI and Twilio to ensure everything is configured correctly.
    """
    results = {
        "status": "success",
        "timestamp": time.time(),
        "config": {
            "host": host,
            "port": PORT,
            "input_audio_format": "g711_ulaw",
            "output_audio_format": "g711_ulaw",
            "voice": VOICE,
        },
        "openai": {"status": "unknown"},
        "twilio": {"status": "unknown"},
        "mongodb": {"status": "unknown"},
    }

    # Test OpenAI API key validity
    try:
        # Check if API key exists and has valid format
        if not OPENAI_API_KEY:
            results["openai"] = {"status": "error", "message": "API key not configured"}
        elif len(OPENAI_API_KEY) < 20:  # Simple validation
            results["openai"] = {
                "status": "error",
                "message": "API key appears invalid (too short)",
            }
        else:
            # We can't easily test the API without making a real request,
            # but we can confirm it's at least formatted correctly
            results["openai"] = {
                "status": "configured",
                "message": "API key is configured",
            }
    except Exception as e:
        results["openai"] = {"status": "error", "message": str(e)}

    # Test Twilio setup
    try:
        # Check if we have credentials set in environment variables
        twilio_sid = os.getenv("TWILIO_ACCOUNT_SID")
        twilio_token = os.getenv("TWILIO_AUTH_TOKEN")

        if not twilio_sid or not twilio_token:
            results["twilio"] = {
                "status": "configured",
                "message": "Twilio SID/token not fully configured",
            }
        else:
            results["twilio"] = {
                "status": "configured",
                "message": "Twilio credentials are configured",
            }
    except Exception as e:
        results["twilio"] = {"status": "error", "message": str(e)}

    # Test MongoDB connection using handler
    try:
        results["mongodb"] = test_mongo()
    except Exception as e:
        results["mongodb"] = {"status": "error", "message": str(e)}

    return results


@app.api_route("/incoming-call", methods=["GET", "POST"])
async def handle_incoming_call(request: Request):
    """Handle incoming call and return TwiML response to connect to Media Stream."""
    loghook("Incoming call received in Twilio")

    # Get caller information if available
    form_data = await request.form() if hasattr(request, "form") else {}
    caller_id = form_data.get("From", "Unknown")
    call_sid = form_data.get("CallSid", "Unknown")

    # Log all available form data for debugging
    log.info(f"=== INCOMING CALL DEBUG ===")
    log.info(f"Caller ID: {caller_id}")
    log.info(f"Call SID: {call_sid}")
    log.info(f"Form data keys: {list(form_data.keys())}")
    log.info(f"Full form data: {dict(form_data)}")
    log.info(f"Request headers: {dict(request.headers)}")
    log.info(f"Request URL: {request.url}")
    log.info(f"Request method: {request.method}")

    loghook(f"CALL INITIATED: From {caller_id}, SID: {call_sid}")

    response = VoiceResponse()

    # Send a simple "Thank you" to get the ai talking immediately
    response.say("Hello")

    # Get the actual hostname
    hostname = request.url.hostname or host
    log.info(f"WebSocket hostname: {hostname}")

    # Create the WebSocket connection with improved parameters
    connect = Connect()
    stream = connect.stream(url=f"wss://{hostname}/media-stream")

    # Add stream parameters for better audio quality
    stream.parameter(name="track", value="both_tracks")
    stream.parameter(name="statusCallback", value=f"https://{hostname}/stream-status")

    response.append(connect)

    twiml_response = str(response)
    log.info(f"Generated TwiML response: {twiml_response}")
    log.info(f"=== END INCOMING CALL DEBUG ===")

    return HTMLResponse(content=twiml_response, media_type="application/xml")


@app.api_route("/stream-status", methods=["POST"])
async def handle_stream_status(request: Request):
    """Handle stream status callbacks from Twilio for monitoring."""
    try:
        form_data = await request.form()
        stream_sid = form_data.get("StreamSid", "Unknown")
        status = form_data.get("Status", "Unknown")

        # Enhanced logging for stream status
        log.info(f"=== STREAM STATUS DEBUG ===")
        log.info(f"Stream SID: {stream_sid}")
        log.info(f"Status: {status}")
        log.info(f"All form data: {dict(form_data)}")
        log.info(f"Request headers: {dict(request.headers)}")
        log.info(f"Timestamp: {time.time()}")

        # Check for specific status values that indicate problems
        if status in ["stopped", "failed", "ended", "interrupted"]:
            log.error(f"🚨 STREAM ISSUE: Stream {stream_sid} status is {status}")
            loghook(f"🚨 STREAM PROBLEM: {stream_sid} - {status}")
        else:
            log.info(f"✅ Stream {stream_sid} status: {status}")
            loghook(f"Stream status update: {stream_sid} - {status}")

        log.info(f"=== END STREAM STATUS DEBUG ===")

        return JSONResponse({"status": "received"})
    except Exception as e:
        log.error(f"Error handling stream status: {e}")
        loghook(f"ERROR in stream status handler: {e}")
        return JSONResponse({"error": "Error processing status"}, status_code=500)


async def safe_cleanup_and_process_transcript(
    websocket,
    response_lock,
    user_transcriptions,
    assistant_transcriptions,
    full_conversation_transcript,
    structured_conversation,
    stream_sid,
    call_start_time,
    silence_warning_count,
    goodbye_confirmation_sent,
    report_processing_state,
):
    """
    Safely cleanup and process any available transcript data when exiting early.
    This ensures we never lose conversation data regardless of how the session ends.
    """
    try:
        log.info("🔄 Performing safe cleanup and transcript processing...")

        # Check if report processing is already in progress or completed
        if report_processing_state["processing_in_progress"]:
            log.info(
                "⏳ Report processing already in progress - skipping duplicate processing"
            )
            return

        if (
            report_processing_state["transcript_saved"]
            and report_processing_state["inspection_data_extracted"]
        ):
            log.info(
                "✅ Report already processed completely - skipping duplicate processing"
            )
            return

        # Mark processing as in progress
        report_processing_state["processing_in_progress"] = True

        # Aggregate any remaining transcriptions
        if user_transcriptions:
            current_user_segment = " ".join(user_transcriptions)
            structured_conversation.append(
                {"role": "user", "content": current_user_segment}
            )
            if full_conversation_transcript:
                full_conversation_transcript += "\nUser: " + current_user_segment
            else:
                full_conversation_transcript = "User: " + current_user_segment

        if assistant_transcriptions:
            current_assistant_segment = " ".join(assistant_transcriptions)
            structured_conversation.append(
                {"role": "assistant", "content": current_assistant_segment}
            )
            full_conversation_transcript += "\nAssistant: " + current_assistant_segment

        # Process transcript if we have any content
        if full_conversation_transcript.strip() or structured_conversation:
            log.info("📋 Processing partial transcript from early exit...")

            current_time = datetime.now()
            timestamp_str = current_time.strftime("%Y%m%d_%H%M%S")
            call_identifier = f"{stream_sid or 'unknown'}_{timestamp_str}"

            # Extract basic info from conversation
            client_email = None
            client_name = "Unknown Customer"
            vehicle_info = "Unknown Vehicle"
            work_order_number = None

            for item in structured_conversation:
                content = item.get("content", "").lower()

                # Look for email in the conversation
                if "email" in content and "@" in content:
                    import re

                    email_pattern = (
                        r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"
                    )
                    emails = re.findall(email_pattern, content, re.IGNORECASE)
                    if emails:
                        client_email = emails[0].lower()

                # Extract other basic info
                if "customer" in content and "name" in content and ":" in content:
                    parts = content.split(":")
                    if len(parts) > 1:
                        client_name = parts[1].strip().title()

                if "work order" in content and ":" in content:
                    parts = content.split(":")
                    if len(parts) > 1:
                        work_order_number = parts[1].strip()

                if (
                    "vehicle" in content
                    and ("make" in content or "model" in content)
                    and ":" in content
                ):
                    parts = content.split(":")
                    if len(parts) > 1:
                        vehicle_info = parts[1].strip().title()

            # Create transcript document
            transcript_document = {
                "transcript": full_conversation_transcript,
                "structured_conversation": structured_conversation,
                "call_id": stream_sid or "unknown",
                "timestamp": time.time(),
                "date_time": current_time,
                "formatted_time": timestamp_str,
                "call_identifier": call_identifier,
                "client_name": client_name,
                "work_order_number": work_order_number,
                "vehicle_info": vehicle_info,
                "metadata": {
                    "duration": time.time() - (call_start_time or time.time()),
                    "silence_warnings_sent": silence_warning_count or 0,
                    "goodbye_detected": goodbye_confirmation_sent or False,
                    "transcript_segments": len(structured_conversation),
                    "early_exit": True,
                    "incomplete_session": True,
                },
            }

            # Save transcript
            mongodb_saved = save_transcript(
                transcript_document,
                call_identifier,
                client_email,
                use_client_manager=True,
            )
            report_processing_state["transcript_saved"] = True
            report_processing_state["call_identifier"] = call_identifier

            # Try to extract inspection data if we have enough content and haven't already
            print(f"🔍 PARTIAL EXTRACTION CHECK: inspection_data_extracted = {report_processing_state['inspection_data_extracted']}")
            print(f"📋 Partial transcript length: {len(full_conversation_transcript)} chars")
            print(f"💬 Partial conversation items: {len(structured_conversation)}")
            
            if not report_processing_state["inspection_data_extracted"]:
                try:
                    print("🚀 STARTING PARTIAL: Attempting to extract inspection data from partial transcript...")
                    log.info(
                        "🔍 Attempting to extract inspection data from partial transcript..."
                    )
                    await extract_and_send_inspection_data(
                        full_conversation_transcript, structured_conversation
                    )
                    report_processing_state["inspection_data_extracted"] = True
                except Exception as extraction_error:
                    log.warning(
                        f"⚠️ Could not extract inspection data from partial transcript: {extraction_error}"
                    )
                    # This is okay for partial transcripts - we still saved the raw conversation

        else:
            log.info("📭 No transcript content available for processing in early exit")

    except Exception as cleanup_error:
        log.error(
            f"❌ Error in safe cleanup and transcript processing: {cleanup_error}"
        )
        loghook(f"ERROR in safe cleanup: {cleanup_error}")

    finally:
        # Always mark processing as complete, even if there was an error
        report_processing_state["processing_in_progress"] = False
        # Always cleanup resources
        try:
            if response_lock and response_lock.locked():
                response_lock.release()
            if websocket:
                await websocket.close()
        except Exception as final_cleanup_error:
            log.error(f"❌ Error in final cleanup: {final_cleanup_error}")


@app.websocket("/media-stream")
async def handle_media_stream(websocket: WebSocket):
    """Handle WebSocket connections between Twilio and OpenAI."""
    loghook("Media stream connection received")
    global response_lock
    import re

    log.info("=== WEBSOCKET CONNECTION DEBUG ===")
    log.info(f"WebSocket client info: {websocket.client}")
    log.info(f"WebSocket headers: {dict(websocket.headers)}")
    log.info(f"WebSocket scope: {websocket.scope.get('path', 'N/A')}")
    log.info("Client connected to media stream")

    await websocket.accept()
    log.info("✅ WebSocket connection accepted")

    # Create a list to track all tasks we create
    tasks = []
    openai_ws = None

    # Initialize transcript variables early for safe cleanup
    user_transcriptions = []
    assistant_transcriptions = []
    full_conversation_transcript = ""
    structured_conversation = []
    stream_sid = None
    call_start_time = time.time()
    silence_warning_count = 0
    goodbye_confirmation_sent = False

    # Report processing state tracking to prevent duplicates
    report_processing_state = {
        "transcript_saved": False,
        "inspection_data_extracted": False,
        "processing_in_progress": False,
        "call_identifier": None,
    }

    # Try to acquire the lock with a timeout
    try:
        log.info("🔒 Attempting to acquire response lock...")
        if not await asyncio.wait_for(response_lock.acquire(), timeout=5.0):
            log.error("❌ Could not acquire response lock in time")
            loghook("ERROR: Response lock timeout")
            await safe_cleanup_and_process_transcript(
                websocket,
                response_lock,
                user_transcriptions,
                assistant_transcriptions,
                full_conversation_transcript,
                structured_conversation,
                stream_sid,
                call_start_time,
                silence_warning_count,
                goodbye_confirmation_sent,
                report_processing_state,
            )
            return
        log.info("✅ Response lock acquired successfully")
    except asyncio.TimeoutError:
        log.error("❌ Timeout acquiring response lock")
        loghook("ERROR: Response lock timeout")
        await safe_cleanup_and_process_transcript(
            websocket,
            response_lock,
            user_transcriptions,
            assistant_transcriptions,
            full_conversation_transcript,
            structured_conversation,
            stream_sid,
            call_start_time,
            silence_warning_count,
            goodbye_confirmation_sent,
            report_processing_state,
        )
        return
    except Exception as e:
        log.error(f"❌ Error acquiring response lock: {e}")
        loghook(f"ERROR acquiring lock: {e}")
        await safe_cleanup_and_process_transcript(
            websocket,
            response_lock,
            user_transcriptions,
            assistant_transcriptions,
            full_conversation_transcript,
            structured_conversation,
            stream_sid,
            call_start_time,
            silence_warning_count,
            goodbye_confirmation_sent,
            report_processing_state,
        )
        return

    try:
        # Connect to OpenAI with improved connection parameters
        log.info("🔌 Connecting to OpenAI WebSocket...")
        log.info(
            f"OpenAI URL: wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview"
        )
        log.info(f"API Key present: {bool(OPENAI_API_KEY)}")
        log.info(f"API Key length: {len(OPENAI_API_KEY) if OPENAI_API_KEY else 0}")

        # Connection parameters optimized for voice streaming stability
        openai_ws = await websockets.connect(
            "wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview",
            extra_headers={
                "Authorization": f"Bearer {OPENAI_API_KEY}",
                "OpenAI-Beta": "realtime=v1",
            },
            # Optimized connection parameters for voice streaming
            ping_interval=20,  # More frequent pings for voice streaming
            ping_timeout=5,  # Faster timeout detection
            close_timeout=5,  # Faster close handshake
            max_size=32 * 1024 * 1024,  # 32MB max message size for audio
            max_queue=16,  # Reduced queue to prevent audio delay buildup
        )
        log.info("✅ Connected to OpenAI WebSocket successfully")
        log.info(f"OpenAI WebSocket state: {openai_ws.state}")
        loghook("CONNECTED TO OPENAI API!")

        # Initialize the session
        log.info("🎯 Initializing session with OpenAI...")
        loghook("Initializing session with OpenAI...")
        await initialize_session(openai_ws)
        log.info("✅ Session initialized successfully")
        loghook("Session initialized and conversation started")

    except websockets.exceptions.ConnectionClosedError as e:
        log.error(f"❌ OpenAI WebSocket connection closed during setup: {e}")
        loghook(f"WEBSOCKET CLOSED: {e}")
        await safe_cleanup_and_process_transcript(
            websocket,
            response_lock,
            user_transcriptions,
            assistant_transcriptions,
            full_conversation_transcript,
            structured_conversation,
            stream_sid,
            call_start_time,
            silence_warning_count,
            goodbye_confirmation_sent,
            report_processing_state,
        )
        return
    except websockets.exceptions.InvalidStatusCode as e:
        log.error(f"❌ OpenAI WebSocket invalid status code: {e}")
        loghook(f"INVALID STATUS: {e}")
        await safe_cleanup_and_process_transcript(
            websocket,
            response_lock,
            user_transcriptions,
            assistant_transcriptions,
            full_conversation_transcript,
            structured_conversation,
            stream_sid,
            call_start_time,
            silence_warning_count,
            goodbye_confirmation_sent,
            report_processing_state,
        )
        return
    except websockets.exceptions.ConnectionClosedOK as e:
        log.warning(f"⚠️ OpenAI WebSocket closed normally during setup: {e}")
        log.warning("This might be due to timing - trying to continue anyway...")
        loghook(f"WEBSOCKET CLOSED NORMALLY DURING SETUP: {e}")
        # Don't return here - let the connection continue
    except Exception as e:
        log.error(f"❌ Error during WebSocket setup: {e}")
        log.error(f"Exception type: {type(e).__name__}")
        log.error(f"Exception details: {str(e)}")
        loghook(f"ERROR FROM OPENAI API: {e}")
        if openai_ws and not openai_ws.closed:
            await openai_ws.close()
        await safe_cleanup_and_process_transcript(
            websocket,
            response_lock,
            user_transcriptions,
            assistant_transcriptions,
            full_conversation_transcript,
            structured_conversation,
            stream_sid,
            call_start_time,
            silence_warning_count,
            goodbye_confirmation_sent,
            report_processing_state,
        )
        return

    # Check if OpenAI WebSocket is still connected after initialization
    if not openai_ws or openai_ws.closed:
        log.error("❌ OpenAI WebSocket not available after initialization")
        loghook("ERROR: OpenAI WebSocket not available after setup")
        await safe_cleanup_and_process_transcript(
            websocket,
            response_lock,
            user_transcriptions,
            assistant_transcriptions,
            full_conversation_transcript,
            structured_conversation,
            stream_sid,
            call_start_time,
            silence_warning_count,
            goodbye_confirmation_sent,
            report_processing_state,
        )
        return

    # Connection specific state
    latest_media_timestamp = 0
    last_assistant_item = None
    mark_queue = []
    response_start_timestamp_twilio = None

    # Silence detection variables
    last_speech_timestamp = time.time()
    silence_warning_sent = False
    silence_check_task = None

    async def receive_from_twilio():
        """Receive audio data from Twilio and send it to the OpenAI Realtime API."""
        nonlocal stream_sid, latest_media_timestamp
        message_count = 0
        try:
            log.info("🎤 Starting to receive messages from Twilio...")
            async for message in websocket.iter_text():
                message_count += 1
                try:
                    data = json.loads(message)
                    event_type = data.get("event", "unknown")

                    # Log every 100th message to avoid spam, but always log important events
                    if message_count % 100 == 0 or event_type in [
                        "start",
                        "stop",
                        "mark",
                    ]:
                        log.info(f"📨 Twilio message #{message_count}: {event_type}")

                    if data["event"] == "media" and openai_ws.open:
                        latest_media_timestamp = int(data["media"]["timestamp"])
                        audio_append = {
                            "type": "input_audio_buffer.append",
                            "audio": data["media"]["payload"],
                        }
                        await openai_ws.send(json.dumps(audio_append))

                        # Log first few audio packets
                        if message_count <= 5:
                            log.info(
                                f"🔊 Audio packet sent to OpenAI: timestamp={latest_media_timestamp}"
                            )

                    elif data["event"] == "start":
                        stream_sid = data["start"]["streamSid"]
                        log.info(f"🚀 Incoming stream has started: {stream_sid}")
                        log.info(f"Start event data: {data}")
                        loghook(f"STREAM STARTED: {stream_sid}")
                        response_start_timestamp_twilio = None
                        latest_media_timestamp = 0
                        last_assistant_item = None

                    elif data["event"] == "stop":
                        log.info(f"🛑 Stream stopped: {stream_sid}")
                        log.info(f"Stop event data: {data}")
                        loghook(f"STREAM STOPPED: {stream_sid}")

                    elif data["event"] == "mark":
                        log.info(f"📍 Mark event received: {data}")
                        if mark_queue:
                            mark_queue.pop(0)

                    else:
                        log.info(f"❓ Unknown event type: {event_type}, data: {data}")

                except json.JSONDecodeError as e:
                    log.error(f"❌ Failed to parse Twilio message: {e}")
                    log.error(f"Raw message: {message}")

        except WebSocketDisconnect:
            log.info("📞 Client disconnected from Twilio WebSocket")
            loghook("TWILIO CLIENT DISCONNECTED")
            # Process transcript immediately when connection breaks
            try:
                await aggregate_transcriptions()
                if full_conversation_transcript.strip() or structured_conversation:
                    log.info("🚨 Connection broke - processing transcript immediately")
                    await upload_transcript()
            except Exception as disconnect_error:
                log.error(
                    f"Error processing transcript after disconnect: {disconnect_error}"
                )
        except Exception as e:
            log.error(f"❌ Error in receive_from_twilio: {e}")
            log.error(f"Message count: {message_count}")
            loghook(f"ERROR receiving from Twilio: {e}")
        finally:
            log.info(
                f"🔄 Cleaning up receive_from_twilio (processed {message_count} messages)"
            )
            # Signal that we should clean up
            if openai_ws and openai_ws.open:
                try:
                    await openai_ws.close()
                    log.info("✅ OpenAI WebSocket closed from receive_from_twilio")
                except Exception as e:
                    log.error(f"❌ Error closing OpenAI WebSocket: {e}")

    async def send_to_twilio():
        """Receive events from the OpenAI Realtime API, send audio back to Twilio."""
        nonlocal \
            stream_sid, \
            last_assistant_item, \
            response_start_timestamp_twilio, \
            last_speech_timestamp, \
            silence_warning_sent, \
            silence_warning_count, \
            silence_check_task, \
            goodbye_confirmation_sent, \
            user_transcriptions, \
            assistant_transcriptions, \
            full_conversation_transcript, \
            structured_conversation, \
            call_start_time

        openai_message_count = 0
        try:
            log.info("🤖 Starting to receive messages from OpenAI...")
            async for openai_message in openai_ws:
                openai_message_count += 1

                # Ensure we properly parse the message as JSON
                try:
                    response = json.loads(openai_message)
                except json.JSONDecodeError as e:
                    log.error(f"❌ Error decoding JSON from OpenAI: {e}")
                    log.error(f"Raw OpenAI message: {openai_message}")
                    loghook(f"JSON DECODE ERROR from OpenAI: {e}")
                    continue

                # Make sure response is a dictionary before proceeding
                if not isinstance(response, dict):
                    log.error(
                        f"❌ Unexpected response format (not a dict): {type(response)}"
                    )
                    log.error(f"Response content: {response}")
                    loghook(f"INVALID RESPONSE FORMAT from OpenAI: {type(response)}")
                    continue

                event_type = response.get("type", "unknown")

                # Enhanced logging for all OpenAI events
                if event_type in LOG_EVENT_TYPES:
                    log.info(f"🔔 OpenAI Event #{openai_message_count}: {event_type}")
                    if event_type == "error":
                        log.error(f"🚨 OpenAI Error Event: {response}")
                        loghook(f"OPENAI ERROR: {response}")
                elif event_type in ["session.created", "session.updated"]:
                    log.info(f"⚙️ Session Event #{openai_message_count}: {event_type}")
                    log.info(f"Session details: {response}")
                elif event_type.startswith("conversation."):
                    log.info(
                        f"💬 Conversation Event #{openai_message_count}: {event_type}"
                    )
                elif event_type.startswith("response."):
                    log.info(f"📤 Response Event #{openai_message_count}: {event_type}")
                elif event_type.startswith("input_audio_buffer."):
                    log.info(
                        f"🎧 Audio Buffer Event #{openai_message_count}: {event_type}"
                    )
                else:
                    log.info(
                        f"❓ Unknown OpenAI Event #{openai_message_count}: {event_type}"
                    )

                # Log every 50th message to track activity
                if openai_message_count % 50 == 0:
                    log.info(f"📊 OpenAI message count: {openai_message_count}")
                    loghook(f"OpenAI messages processed: {openai_message_count}")

                # Handle speech stopped event
                if response.get("type") == SPEECH_STOPPED_EVENT:
                    log.info("Speech stopped detected, aggregating transcriptions")
                    await aggregate_transcriptions()
                    # Reset the last speech timestamp
                    last_speech_timestamp = time.time()

                # Handle transcript done event
                elif response.get("type") == AUDIO_TRANSCRIPT_DONE_EVENT:
                    log.info(
                        "Transcript done event received, finalizing transcript segment"
                    )
                    await aggregate_transcriptions()

                # Check for transcription in response
                if (
                    "transcript" in str(response)
                    or response.get("type") == AUDIO_TRANSCRIPT_DELTA_EVENT
                ):
                    # Extract transcript from various response types
                    transcript = None
                    is_assistant_transcript = False

                    # Handle different transcript event types
                    if (
                        response.get("type") == RESPONSE_DONE_EVENT
                        and isinstance(response.get("response"), dict)
                        and response.get("response", {}).get("output")
                    ):
                        for item in response["response"]["output"]:
                            if isinstance(item, dict) and "content" in item:
                                for content in item.get("content", []):
                                    if (
                                        isinstance(content, dict)
                                        and content.get("type") == "audio"
                                        and content.get("transcript")
                                    ):
                                        transcript = content["transcript"]
                                        is_assistant_transcript = True
                    elif response.get(
                        "type"
                    ) == "input_audio_buffer.committed" and response.get("transcript"):
                        transcript = response["transcript"]
                        is_assistant_transcript = False
                    elif (
                        response.get("type") == AUDIO_TRANSCRIPT_DELTA_EVENT
                        and isinstance(response.get("delta"), dict)
                        and response.get("delta", {}).get("text")
                    ):
                        transcript = response["delta"]["text"]
                        is_assistant_transcript = False
                    # Detect assistant message from response type
                    elif (
                        response.get("type") == "response.audio.delta"
                        and response.get("item_id")
                        and isinstance(response.get("data"), dict)
                        and response.get("data", {}).get("transcript")
                    ):
                        transcript = response["data"]["transcript"]
                        is_assistant_transcript = True

                    # Process transcript if found
                    if transcript:
                        log.info(
                            f"Transcript detected: {transcript} (Assistant: {is_assistant_transcript})"
                        )

                        if is_assistant_transcript:
                            assistant_transcriptions.append(transcript)
                            # Check if assistant indicates inspection completion
                            await check_for_completion_phrases(transcript)
                        else:
                            user_transcriptions.append(transcript)
                            # Check for goodbye phrases if not already in confirmation mode
                            if not goodbye_confirmation_sent:
                                await check_for_goodbye_phrases(transcript)

                # Handle speech stopped event
                if response.get("type") == "input_audio_buffer.speech_stopped":
                    log.info("Speech stopped detected.")
                    # Start silence check task
                    if silence_check_task:
                        silence_check_task.cancel()
                    silence_check_task = asyncio.create_task(check_silence())

                # Handle speech started event to reset silence detection
                if response.get("type") == "input_audio_buffer.speech_started":
                    log.info("Speech started detected.")
                    last_speech_timestamp = time.time()
                    silence_warning_sent = False

                    # Cancel the silence check task if it's running
                    if silence_check_task:
                        silence_check_task.cancel()
                        silence_check_task = None

                    # Immediately truncate the current response when user starts speaking
                    if last_assistant_item:
                        log.info(
                            f"Interrupting response with id: {last_assistant_item}"
                        )
                        await handle_speech_started_event()

                # Handle audio from OpenAI
                if (
                    response.get("type") == "response.audio.delta"
                    and "delta" in response
                ):
                    audio_payload = base64.b64encode(
                        base64.b64decode(response["delta"])
                    ).decode("utf-8")
                    audio_delta = {
                        "event": "media",
                        "streamSid": stream_sid,
                        "media": {"payload": audio_payload},
                    }
                    await websocket.send_json(audio_delta)

                    if isinstance(response.get("data"), dict) and response.get(
                        "data", {}
                    ).get("transcript"):
                        assistant_transcript = response["data"]["transcript"]
                        assistant_transcriptions.append(assistant_transcript)
                        log.info(
                            f"Assistant transcript from audio delta: {assistant_transcript}"
                        )

                    if response_start_timestamp_twilio is None:
                        response_start_timestamp_twilio = latest_media_timestamp
                        if SHOW_TIMING_MATH:
                            log.info(
                                f"Setting start timestamp for new response: {response_start_timestamp_twilio}ms"
                            )

                    if response.get("item_id"):
                        last_assistant_item = response["item_id"]

                    await send_mark(websocket, stream_sid)

        except websockets.exceptions.ConnectionClosedError as e:
            log.error(f"📞 OpenAI WebSocket connection closed in send_to_twilio: {e}")
            loghook(f"OPENAI CONNECTION CLOSED: {e}")
        except websockets.exceptions.ConnectionClosedOK as e:
            log.info(f"✅ OpenAI WebSocket closed normally: {e}")
            loghook("OPENAI CONNECTION CLOSED NORMALLY")
        except Exception as e:
            log.error(f"❌ Error in send_to_twilio: {e}")
            log.error(f"Exception type: {type(e).__name__}")
            log.error(f"OpenAI message count: {openai_message_count}")
            loghook(f"ERROR in send_to_twilio: {e}")
        finally:
            log.info(
                f"🔄 Cleaning up send_to_twilio (processed {openai_message_count} OpenAI messages)"
            )

    async def check_silence():
        """Check for prolonged silence and take appropriate action using enhanced VAD detection."""
        nonlocal silence_warning_sent, silence_warning_count
        try:
            await asyncio.sleep(20)  # Increased tolerance - less frequent prompts

            current_time = time.time()
            silence_duration = current_time - last_speech_timestamp

            log.info(
                f"Silence check - Duration: {silence_duration:.2f}s, Last speech: {datetime.fromtimestamp(last_speech_timestamp).strftime('%H:%M:%S')}"
            )

            if (
                not silence_warning_sent and silence_duration >= 20
            ):  # Increased threshold - more tolerant of silence
                log.info(
                    f"Prolonged silence detected ({silence_duration:.2f}s). Sending polite prompt to user."
                )
                silence_warning_sent = True
                silence_warning_count += 1

                await send_silence_warning()

                await asyncio.sleep(10)  # Wait for response after silence warning

                current_time = time.time()
                updated_silence = current_time - last_speech_timestamp

                if updated_silence >= 30:  # 20s initial + 10s after warning
                    log.info(
                        f"No response after silence warning. Total silence: {updated_silence:.2f}s. Hanging up the call."
                    )
                    await hang_up_call(
                        "I'll end our call for now since I haven't heard from you. Please feel free to call back when you're ready to continue the inspection. Have a great day!"
                    )
                else:
                    log.info("User responded after silence warning. Continuing call.")
                    silence_warning_sent = False

            elif (
                silence_warning_count > 0 and silence_duration >= 25
            ):  # Higher threshold for repeat warnings
                log.info(
                    f"Repeated prolonged silence ({silence_duration:.2f}s) after previous warnings. Hanging up."
                )
                await hang_up_call(
                    "I'll end our call now to free up the line. You can call back anytime when you're ready to continue. Thank you!"
                )

        except asyncio.CancelledError:
            log.info("Silence check cancelled - speech detected.")
        except Exception as e:
            log.error(f"Error in check_silence: {e}")

    async def check_for_goodbye_phrases(transcript):
        """Check if the transcript contains farewell phrases."""
        nonlocal goodbye_confirmation_sent
        try:
            transcript_lower = transcript.lower()

            for phrase in FAREWELL_PHRASES:
                if re.search(r"\b" + re.escape(phrase) + r"\b", transcript_lower):
                    log.info(f"Farewell phrase detected: '{phrase}'")

                    if not goodbye_confirmation_sent:
                        goodbye_confirmation_sent = True
                        await send_goodbye_confirmation()
                    return
        except Exception as e:
            log.error(f"Error in check_for_goodbye_phrases: {e}")

    async def check_for_completion_phrases(transcript):
        """Check if the assistant indicates the inspection is complete."""
        try:
            transcript_lower = transcript.lower()

            # Phrases that indicate inspection completion - more aggressive detection
            completion_phrases = [
                "thank you, we are done here",
                "thank you we are done here",
                "we are done here",
                "we're done here",
                "we've covered everything",
                "believe we've covered everything",
                "that completes our inspection",
                "we've finished the inspection",
                "inspection is complete",
                "that's everything for",
                "anything else you'd like to add",
                "thank you for being so thorough",
                "have everything we need",
                "completed the inspection",
                "finished with the inspection",
                "that's all we need",
                "inspection completed",
                "all done with",
                "everything looks good",
                "ready to wrap up",
                "think we're done",
            ]

            for phrase in completion_phrases:
                if phrase in transcript_lower:
                    log.info(f"Completion phrase detected: '{phrase}'")
                    # Wait briefly for any final user response to prevent premature hangup
                    await asyncio.sleep(1)
                    await hang_up_call(
                        "Thank you, we are done here."
                    )
                    return

        except Exception as e:
            log.error(f"Error in check_for_completion_phrases: {e}")

    async def send_goodbye_confirmation():
        """Send a confirmation message when a farewell phrase is detected."""
        try:
            confirm_text = "It sounds like you want to end our call. Is that correct? Please say yes if you'd like to hang up now."

            assistant_transcriptions.append(confirm_text)

            confirmation_message = {
                "type": "conversation.item.create",
                "item": {
                    "type": "message",
                    "role": "assistant",
                    "content": [{"type": "text", "text": confirm_text}],
                },
            }
            await openai_ws.send(json.dumps(confirmation_message))
            await openai_ws.send(json.dumps({"type": "response.create"}))

            await aggregate_transcriptions()

            asyncio.create_task(listen_for_goodbye_confirmation())
        except Exception as e:
            log.error(f"Error sending goodbye confirmation: {e}")

    async def listen_for_goodbye_confirmation():
        """Listen for confirmation to end the call after detecting a farewell phrase."""
        nonlocal goodbye_confirmation_sent
        try:
            # await asyncio.sleep(10)

            if user_transcriptions:
                recent_transcript = user_transcriptions[-1].lower()

                confirmation_phrases = [
                    "yes",
                    "correct",
                    "that's right",
                    "yeah",
                    "yep",
                    "sure",
                    "please do",
                ]
                for phrase in confirmation_phrases:
                    if re.search(r"\b" + re.escape(phrase) + r"\b", recent_transcript):
                        log.info(f"User confirmed goodbye with: '{phrase}'")
                        await hang_up_call(
                            "Thank you for your time today. It was a pleasure assisting you. Goodbye!"
                        )
                        return

                log.info("User did not confirm goodbye. Continuing the call.")
                goodbye_confirmation_sent = False
        except Exception as e:
            log.error(f"Error in listen_for_goodbye_confirmation: {e}")

    async def send_silence_warning():
        """Send a polite prompt to the user during prolonged silence."""
        nonlocal silence_warning_count
        try:
            if silence_warning_count == 0:
                silence_text = "I'm still here and ready to continue. Are you still with me? Just let me know when you're ready to proceed with the inspection."
            else:
                silence_text = "I want to make sure we don't lose our connection. Are you still there? I'm ready to continue whenever you are."

            assistant_transcriptions.append(silence_text)

            silence_message = {
                "type": "conversation.item.create",
                "item": {
                    "type": "message",
                    "role": "assistant",
                    "content": [{"type": "text", "text": silence_text}],
                },
            }
            await openai_ws.send(json.dumps(silence_message))
            await openai_ws.send(json.dumps({"type": "response.create"}))

            await aggregate_transcriptions()

            silence_warning_count += 1
            log.info(f"Sent polite silence prompt #{silence_warning_count}")
        except Exception as e:
            log.error(f"Error sending silence warning: {e}")

    async def aggregate_transcriptions():
        """Aggregate all transcriptions into a structured conversation format."""
        nonlocal \
            user_transcriptions, \
            assistant_transcriptions, \
            full_conversation_transcript, \
            structured_conversation

        if user_transcriptions:
            current_user_segment = " ".join(user_transcriptions)

            structured_conversation.append(
                {"role": "user", "content": current_user_segment}
            )

            if full_conversation_transcript:
                full_conversation_transcript += "\nUser: " + current_user_segment
            else:
                full_conversation_transcript = "User: " + current_user_segment

            user_transcriptions = []

            log.info(f"Aggregated user transcription segment: {current_user_segment}")

        if assistant_transcriptions:
            current_assistant_segment = " ".join(assistant_transcriptions)

            structured_conversation.append(
                {"role": "assistant", "content": current_assistant_segment}
            )

            full_conversation_transcript += "\nAssistant: " + current_assistant_segment

            assistant_transcriptions = []

            log.info(
                f"Aggregated assistant transcription segment: {current_assistant_segment}"
            )

    async def upload_transcript():
        """Save the full conversation transcript to MongoDB using client-centric approach."""
        nonlocal full_conversation_transcript, structured_conversation

        # Simple duplicate prevention
        if hasattr(upload_transcript, "_processing") and upload_transcript._processing:
            log.info(
                "⏳ Upload transcript already in progress - skipping duplicate call"
            )
            return

        upload_transcript._processing = True

        await aggregate_transcriptions()

        if not full_conversation_transcript:
            log.info("No transcript to save.")
            return

        try:
            log.info(
                f"Saving transcript to MongoDB (length: {len(full_conversation_transcript)})"
            )

            current_time = datetime.now()
            timestamp_str = current_time.strftime("%Y%m%d_%H%M%S")
            call_identifier = f"{stream_sid}_{timestamp_str}"

            # Extract client email from structured conversation
            client_email = None
            client_name = "Unknown Customer"
            vehicle_info = "Unknown Vehicle"
            work_order_number = None

            for item in structured_conversation:
                content = item.get("content", "").lower()

                # Look for email in the conversation
                if "email" in content and "@" in content:
                    import re

                    email_pattern = (
                        r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"
                    )
                    emails = re.findall(email_pattern, content, re.IGNORECASE)
                    if emails:
                        client_email = emails[0].lower()

                # Extract other info
                if "customer" in content and "name" in content and ":" in content:
                    parts = content.split(":")
                    if len(parts) > 1:
                        client_name = parts[1].strip().title()

                if "work order" in content and ":" in content:
                    parts = content.split(":")
                    if len(parts) > 1:
                        work_order_number = parts[1].strip()

                if (
                    "vehicle" in content
                    and ("make" in content or "model" in content)
                    and ":" in content
                ):
                    parts = content.split(":")
                    if len(parts) > 1:
                        vehicle_info = parts[1].strip().title()

            # Prepare transcript document
            transcript_document = {
                "transcript": full_conversation_transcript,
                "structured_conversation": structured_conversation,
                "call_id": stream_sid,
                "timestamp": time.time(),
                "date_time": current_time,
                "formatted_time": timestamp_str,
                "call_identifier": call_identifier,
                "client_name": client_name,
                "work_order_number": work_order_number,
                "vehicle_info": vehicle_info,
                "metadata": {
                    "duration": time.time() - call_start_time,
                    "silence_warnings_sent": silence_warning_count,
                    "goodbye_detected": goodbye_confirmation_sent,
                    "transcript_segments": len(structured_conversation),
                },
            }

            # Use the new MongoDB handler to save transcript
            mongodb_saved = save_transcript(
                transcript_document,
                call_identifier,
                client_email,
                use_client_manager=True,
            )
            report_processing_state["transcript_saved"] = True
            report_processing_state["call_identifier"] = call_identifier

            # Only extract inspection data if not already done
            print(f"🔍 EXTRACTION CHECK: inspection_data_extracted = {report_processing_state['inspection_data_extracted']}")
            print(f"📋 Transcript length: {len(full_conversation_transcript)} chars")
            print(f"💬 Conversation items: {len(structured_conversation)}")
            
            if not report_processing_state["inspection_data_extracted"]:
                try:
                    print("🚀 STARTING: Extracting structured inspection data using OpenAI function calling...")
                    log.info(
                        "Extracting structured inspection data using OpenAI function calling..."
                    )
                    await extract_and_send_inspection_data(
                        full_conversation_transcript, structured_conversation
                    )
                    report_processing_state["inspection_data_extracted"] = True
                except Exception as extraction_error:
                    log.error(
                        f"Error during inspection data extraction: {extraction_error}"
                    )
                    # Continue with transcript processing even if inspection data extraction fails

            # No PDF generation - this app only extracts and saves JSON data
            log.info("✅ Transcript processing completed - JSON data extraction handled separately")

        except Exception as e:
            log.error(f"Error in upload_transcript: {e}")

        finally:
            # Always mark processing as complete
            upload_transcript._processing = False

    async def hang_up_call(message="Thank you for your time today. Goodbye!"):
        """Hang up the call with a custom message."""
        try:
            assistant_transcriptions.append(message)

            # Send final message to OpenAI to allow it to speak the farewell
            hangup_message = {
                "type": "conversation.item.create",
                "item": {
                    "type": "message",
                    "role": "assistant",
                    "content": [{"type": "text", "text": message}],
                },
            }
            if openai_ws and not openai_ws.closed:
                await openai_ws.send(json.dumps(hangup_message))
                await openai_ws.send(json.dumps({"type": "response.create"}))
                log.info("Final hangup message sent to OpenAI")

            await aggregate_transcriptions()

            # Give time for the final message to be spoken
            await asyncio.sleep(1)

            # Upload transcript first while connections are still active
            await upload_transcript()

            # Send a clear event to stop any ongoing audio before closing
            if websocket and stream_sid and (not hasattr(websocket, '_closed') or not websocket._closed):
                try:
                    await websocket.send_json(
                        {"event": "clear", "streamSid": stream_sid}
                    )
                    await asyncio.sleep(0.5)  # Brief pause for clear to process
                except Exception as e:
                    log.warning(f"Error sending clear event: {e}")

            # Close the OpenAI WebSocket first to prevent response conflicts
            if openai_ws and not openai_ws.closed:
                try:
                    await openai_ws.close()
                    log.info("OpenAI WebSocket closed for hangup")
                except Exception as e:
                    log.warning(f"Error closing OpenAI WebSocket: {e}")

            # Close the Twilio WebSocket to end the call
            if websocket and (not hasattr(websocket, '_closed') or not websocket._closed):
                try:
                    await websocket.close()
                    log.info(f"WebSocket closed to end call for stream {stream_sid}")
                except Exception as e:
                    log.warning(f"Error closing Twilio WebSocket: {e}")

        except Exception as e:
            log.error(f"Error hanging up call: {e}")

    async def handle_speech_started_event():
        """Handle interruption when the caller's speech starts."""
        nonlocal response_start_timestamp_twilio, last_assistant_item
        log.info("Handling speech started event.")

        # Always clear the audio stream first to stop playback immediately
        try:
            await websocket.send_json({"event": "clear", "streamSid": stream_sid})
            log.info("✅ Sent clear event to stop audio playback")
        except Exception as clear_error:
            log.warning(f"⚠️ Error sending clear event: {clear_error}")

        # Only attempt truncation if we have valid timing data
        if (
            mark_queue
            and response_start_timestamp_twilio is not None
            and last_assistant_item
        ):
            try:
                elapsed_time = latest_media_timestamp - response_start_timestamp_twilio

                # Validate elapsed time - must be positive and reasonable
                if elapsed_time <= 0:
                    log.warning(
                        f"⚠️ Invalid elapsed time for truncation: {elapsed_time}ms - skipping truncation"
                    )
                elif elapsed_time > 300000:  # More than 5 minutes seems unreasonable
                    log.warning(
                        f"⚠️ Elapsed time too large for truncation: {elapsed_time}ms - skipping truncation"
                    )
                else:
                    if SHOW_TIMING_MATH:
                        log.info(
                            f"Calculating elapsed time for truncation: {latest_media_timestamp} - {response_start_timestamp_twilio} = {elapsed_time}ms"
                        )

                    if SHOW_TIMING_MATH:
                        log.info(
                            f"Truncating item with ID: {last_assistant_item}, Truncated at: {elapsed_time}ms"
                        )

                    truncate_event = {
                        "type": "conversation.item.truncate",
                        "item_id": last_assistant_item,
                        "content_index": 0,
                        "audio_end_ms": elapsed_time,
                    }

                    # Send truncation event with error handling
                    await openai_ws.send(json.dumps(truncate_event))
                    log.info(
                        f"✅ Sent truncation event for item {last_assistant_item} at {elapsed_time}ms"
                    )

            except Exception as truncate_error:
                log.warning(f"⚠️ Error sending truncation event: {truncate_error}")
                # Continue without truncation - the clear event should be sufficient
                loghook(f"TRUNCATION ERROR: {truncate_error}")
        else:
            log.info("ℹ️ Skipping truncation - missing timing data or assistant item")

        # Always clean up state
        mark_queue.clear()
        last_assistant_item = None
        response_start_timestamp_twilio = None
        log.info("🧹 Cleaned up speech interruption state")

    async def send_mark(connection, stream_sid):
        if stream_sid:
            mark_event = {
                "event": "mark",
                "streamSid": stream_sid,
                "mark": {"name": "responsePart"},
            }
            await connection.send_json(mark_event)
            mark_queue.append("responsePart")

    try:
        await asyncio.gather(receive_from_twilio(), send_to_twilio())
    except Exception as e:
        log.error(f"❌ Error in WebSocket tasks: {e}")
        loghook(f"ERROR in WebSocket tasks: {e}")

    log.info("🧹 Cleaning up WebSocket connections...")

    # GUARANTEED TRANSCRIPT PROCESSING - Ensure we always process whatever transcript we have
    try:
        log.info("🔄 Performing guaranteed transcript processing before cleanup...")
        await aggregate_transcriptions()

        # Only process if we have some conversation content
        if full_conversation_transcript.strip() or structured_conversation:
            log.info(
                "📋 Processing incomplete/partial transcript before session end..."
            )
            await upload_transcript()
        else:
            log.info("📭 No transcript content to process during cleanup")
    except Exception as cleanup_error:
        log.error(f"❌ Error in guaranteed transcript processing: {cleanup_error}")
        # Continue with cleanup even if transcript processing fails
        loghook(f"ERROR in guaranteed transcript processing: {cleanup_error}")

    try:
        if openai_ws and not openai_ws.closed:
            await openai_ws.close()
            log.info("✅ OpenAI WebSocket closed successfully")
            loghook("OpenAI WebSocket connection closed")
    except Exception as e:
        log.error(f"❌ Error closing OpenAI WebSocket: {e}")
        loghook(f"ERROR closing OpenAI WebSocket: {e}")

    try:
        await websocket.close()
        log.info("✅ Client WebSocket closed successfully")
        loghook("Twilio WebSocket connection closed")
    except Exception as e:
        log.error(f"❌ Error closing client WebSocket: {e}")
        loghook(f"ERROR closing Twilio WebSocket: {e}")

    if response_lock.locked():
        response_lock.release()
        log.info("🔓 Response lock released")
        loghook("Response lock released")

    log.info("=== END WEBSOCKET SESSION DEBUG ===")


async def send_initial_conversation_item(openai_ws):
    """Send initial conversation item to start the inspection conversation."""
    try:
        log.info("Sending initial conversation item to start inspection")
        initial_conversation_item = {
            "type": "conversation.item.create",
            "item": {
                "type": "message",
                "role": "user",
                "content": [
                    {
                        "type": "input_text",
                        "text": "Please start the vehicle inspection conversation.",
                    }
                ],
            },
        }
        await openai_ws.send(json.dumps(initial_conversation_item))
        await openai_ws.send(json.dumps({"type": "response.create"}))
        log.info("Initial conversation item sent")
    except Exception as e:
        log.error(f"Error sending initial conversation item: {e}")
        raise


@deep_debug(log_args=False, log_return=False, log_duration=True)
async def initialize_session(openai_ws):
    """Initialize session with OpenAI (simplified like official Twilio sample)."""
    try:
        log.info("Starting session initialization with OpenAI")

        # Send session update with configuration (simplified like official sample)
        session_update = {
            "type": "session.update",
            "session": {
                "input_audio_format": "g711_ulaw",
                "output_audio_format": "g711_ulaw",
                "voice": VOICE,
                "instructions": SYSTEM_MESSAGE,
                "modalities": ["text", "audio"],
                "temperature": 0.7,
                "turn_detection": {
                    "type": "server_vad",
                    "threshold": 0.4,  # Higher threshold = less sensitive to background noise, reduced overlaps
                    "prefix_padding_ms": 300,  # Reduced padding for faster response
                    "silence_duration_ms": 800,  # Increased to 0.8 seconds to prevent interruptions
                },
            },
        }
        await openai_ws.send(json.dumps(session_update))
        log.info("✅ Session update sent")

        # Send initial conversation item to start the conversation (like official sample)
        await send_initial_conversation_item(openai_ws)

        log.info("✅ Session initialized successfully")
        loghook("Session initialized and conversation started")

    except Exception as e:
        log.error(f"❌ Error in initialize_session: {e}")
        raise


@deep_debug(sensitive_params=["openai_api_key"])
async def extract_and_send_inspection_data(
    transcript: str, structured_conversation: List[Dict]
) -> bool:
    """Extract structured inspection data using OpenAI function calling and send to API."""
    try:
        print("="*80)
        print("🔍 EXTRACTION DEBUG: Starting inspection data extraction...")
        print("="*80)
        print(f"📝 Transcript length: {len(transcript)} characters")
        print(f"💬 Structured conversation items: {len(structured_conversation)}")
        print(f"📋 Sample conversation: {structured_conversation[:2] if structured_conversation else 'No conversation data'}")
        
        # Debug print the actual transcript content
        if transcript:
            print(f"📄 Transcript preview (first 500 chars): {transcript[:500]}...")
        else:
            print("⚠️ WARNING: Empty transcript provided!")
            
        # Debug print structured conversation
        if structured_conversation:
            print("💬 Structured conversation preview:")
            for i, item in enumerate(structured_conversation[:5]):
                role = item.get('role', 'unknown')
                content = item.get('content', '')[:100]
                print(f"  {i+1}. {role}: {content}...")
        else:
            print("⚠️ WARNING: No structured conversation data!")
        
        log.info("Calling OpenAI API to extract structured inspection data...")

        # Detect if this is a partial/incomplete conversation
        is_incomplete = (
            len(structured_conversation) < 6  # Very short conversation
            or not any(
                "inspection" in item.get("content", "").lower()
                for item in structured_conversation
            )
            or "early_exit"
            in str(structured_conversation)  # Flag from our cleanup function
        )

        # Prepare the conversation for function calling with enhanced instructions
        incomplete_guidance = (
            """
        
SPECIAL HANDLING FOR INCOMPLETE CONVERSATIONS:
- If the conversation seems incomplete or cut short, still extract whatever information is available
- For missing required fields, use sensible defaults (e.g., "Unknown" for missing names, current date for inspection date)
- Create checklist_items even from partial exchanges - extract whatever inspection items were discussed
- Mark the extraction with incomplete_session: true in metadata if the conversation seems unfinished
- Be more lenient with partial or unclear responses when determining status values
        """
            if is_incomplete
            else ""
        )

        messages = [
            {
                "role": "system",
                "content": f"""You are an expert at extracting vehicle inspection data from conversations. Your main tasks:

1. IDENTIFY VEHICLE TYPE: Look for phrases like "We will be doing a [TYPE] inspection" and extract as car_type
2. EXTRACT CHECKLIST ITEMS: For every question-answer pair about vehicle parts, create a checklist item
3. EXTRACT BASIC INFO: Get unit number, client name, vehicle make/model if mentioned

FOCUS ON THESE PATTERNS:
- Assistant asks: "Door locks?" → User responds → Create checklist item
- Assistant asks: "Brakes?" → User responds → Create checklist item  
- Assistant asks: "What is the unit number?" → Extract as unit_number
- Assistant says: "We will be doing a Light-Duty Vehicle inspection" → Extract as car_type

SIMPLE RULES:
- One checklist item per question asked
- Use the exact item name from the question
- Record the user's response as status and notes
- If information is missing, leave fields empty (don't guess)
- Only extract what was actually discussed{incomplete_guidance}
                
EXAMPLE EXTRACTION:
If conversation has:
Assistant: "Let's check the brakes. How do they look?"
User: "Brakes are in good condition, no issues found."
Assistant: "What about the engine oil?"
User: "Oil level is good, clean oil."

Then create:
checklist_items: [
  {{"item": "Brakes", "status": "OK", "notes": "Brakes are in good condition, no issues found."}},
  {{"item": "Engine Oil", "status": "OK", "notes": "Oil level is good, clean oil."}}
]""",
            },
            {
                "role": "user",
                "content": f"""Extract structured inspection data from this transcript. CRITICAL: Create one checklist_items entry for EVERY question-answer pair about inspection items.
                
                Full Transcript:
                {transcript}
                
                Structured Conversation:
                {json.dumps(structured_conversation, indent=2)}
                
                STEP-BY-STEP CHECKLIST EXTRACTION:
                1. Go through EACH assistant message that asks about an inspection item
                2. Find the corresponding user response 
                3. Create a checklist_items entry with:
                   - item: The specific item being inspected
                   - status: User's response interpreted as status
                   - notes: User's exact response
                4. Do this for EVERY inspection question, no matter how small
                
                Generate the complete InspectionData JSON with ALL inspection items captured.""",
            },
        ]

        # Log the full request for debugging
        request_payload = {
            "model": "gpt-4o",
            "messages": messages,
            "tools": [{"type": "function", "function": INSPECTION_EXTRACTION_SCHEMA}],
            "tool_choice": {"type": "function", "function": {"name": "extract_inspection_data"}},
            "temperature": 0.3,  # Increased for more flexible interpretation
        }
        
        print("📤 OpenAI API Request:")
        print(f"   Model: {request_payload['model']}")
        print(f"   Messages count: {len(request_payload['messages'])}")
        print(f"   Temperature: {request_payload['temperature']}")
        print(f"   Message preview: {messages[0]['content'][:200]}...")
        
        # Make the API call with function calling
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                "https://api.openai.com/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {OPENAI_API_KEY}",
                    "Content-Type": "application/json",
                },
                json=request_payload,
            )

            if response.status_code != 200:
                print(f"❌ OpenAI API error: {response.status_code} - {response.text}")
                log.error(f"OpenAI API error: {response.status_code} - {response.text}")
                return False

            api_response = response.json()
            print(f"📦 OpenAI API Response received")
            print(f"🔑 Response keys: {list(api_response.keys())}")
            
            # Debug the full response structure
            if "choices" in api_response:
                choices = api_response["choices"]
                print(f"📊 Choices count: {len(choices)}")
                if len(choices) > 0:
                    choice = choices[0]
                    print(f"🔍 First choice keys: {list(choice.keys())}")
                    if "message" in choice:
                        message = choice["message"]
                        print(f"💬 Message keys: {list(message.keys())}")
                        if "tool_calls" in message:
                            tool_calls = message["tool_calls"]
                            print(f"🔧 Tool calls count: {len(tool_calls)}")
                            if len(tool_calls) > 0:
                                tool_call = tool_calls[0]
                                print(f"🛠️ Tool call function: {tool_call.get('function', {}).get('name', 'Unknown')}")
                                print(f"📝 Arguments preview: {tool_call.get('function', {}).get('arguments', '')[:200]}...")
                        else:
                            print("⚠️ No tool_calls in message")
                            if "content" in message:
                                print(f"📄 Message content: {message['content'][:200]}...")
                else:
                    print("❌ No choices in response")
            else:
                print("❌ No choices key in response")

            # Extract the tool call result
            if "choices" in api_response and len(api_response["choices"]) > 0:
                choice = api_response["choices"][0]
                if "message" in choice and "tool_calls" in choice["message"]:
                    tool_calls = choice["message"]["tool_calls"]
                    if tool_calls and len(tool_calls) > 0:
                        tool_call = tool_calls[0]
                        if tool_call["function"]["name"] == "extract_inspection_data":
                            try:
                                extracted_data = json.loads(tool_call["function"]["arguments"])
                            
                                print("✅ EXTRACTION SUCCESS: Data extracted from OpenAI")
                                print(f"🔑 Keys in extracted data: {list(extracted_data.keys())}")
                                print(f"👤 Vehicle owner: {extracted_data.get('vehicle_owner', 'N/A')}")
                                print(f"📧 Client email: {extracted_data.get('client_email', 'N/A')}")
                                print(f"🚗 Vehicle info: {extracted_data.get('make_model_year', 'N/A')}")
                                print(f"📋 Checklist items count: {len(extracted_data.get('checklist_items', []))}")
                                
                                # Debug checklist items in detail
                                checklist_items = extracted_data.get('checklist_items', [])
                                if checklist_items:
                                    print("📝 Checklist items found:")
                                    for i, item in enumerate(checklist_items):
                                        print(f"  {i+1}. {item.get('item', 'N/A')} = {item.get('status', 'N/A')}")
                                        print(f"     Notes: {item.get('notes', 'No notes')[:100]}...")
                                else:
                                    print("⚠️ WARNING: No checklist items extracted!")

                                # Add metadata about the extraction
                                if is_incomplete:
                                    extracted_data["metadata"] = extracted_data.get(
                                        "metadata", {}
                                    )
                                    extracted_data["metadata"]["incomplete_session"] = True
                                    extracted_data["metadata"]["extraction_note"] = (
                                        "Extracted from incomplete or partial conversation"
                                    )

                                # Log extraction details for verification
                                checklist_count = len(
                                    extracted_data.get("checklist_items", [])
                                )
                                completion_status = (
                                    "PARTIAL" if is_incomplete else "COMPLETE"
                                )
                                log.info(
                                    f"Successfully extracted inspection data with {checklist_count} checklist items ({completion_status})"
                                )

                                # Log each checklist item for verification
                                for i, item in enumerate(
                                    extracted_data.get("checklist_items", [])
                                ):
                                    log.info(
                                        f"✅ Checklist item {i + 1}: '{item.get('item', 'N/A')}' = {item.get('status', 'N/A')} ({item.get('notes', 'No notes')[:50]}...)"
                                    )

                                # Log summary of extraction
                                log.info(
                                    f"📊 Extraction summary: {checklist_count} checklist items, vehicle_owner: {extracted_data.get('vehicle_owner', 'N/A')}, inspector: {extracted_data.get('inspector_name', 'N/A')}"
                                )
                                log.info(
                                    f"🔧 Vehicle info: {extracted_data.get('make_model_year', 'N/A')} (VIN: {extracted_data.get('vin', 'N/A')})"
                                )

                                # Update with current UTC date and time to override OpenAI's old training data
                                from datetime import datetime, timezone
                                current_utc = datetime.now(timezone.utc)
                                extracted_data['inspection_date_vehicle'] = current_utc.strftime('%Y-%m-%d')
                                extracted_data['report_generation_datetime'] = current_utc.strftime('%Y-%m-%d %H:%M:%S')
                                extracted_data['signoff_date'] = current_utc.strftime('%Y-%m-%d')
                                
                                print(f"🕐 Updated with current UTC time: {current_utc.strftime('%Y-%m-%d %H:%M:%S')}")
                                print(f"📅 Inspection date: {extracted_data['inspection_date_vehicle']}")
                                print(f"📅 Report generation: {extracted_data['report_generation_datetime']}")

                                # Send to the API endpoint
                                return await send_inspection_data_to_api(extracted_data)
                            except json.JSONDecodeError as e:
                                log.error(f"Error parsing tool call arguments: {e}")
                                return False
                else:
                    print("❌ No tool calls found in OpenAI response")
                    print(f"📋 Choice message keys: {list(choice.get('message', {}).keys())}")
                    log.error("No tool calls found in OpenAI response")
                    return False
            else:
                print("❌ Invalid response structure from OpenAI API")
                print(f"📋 API response structure: {api_response}")
                log.error("Invalid response structure from OpenAI API")
                return False

    except Exception as e:
        log.error(f"Error extracting inspection data: {e}")
        return False


@deep_debug(max_arg_length=1000, max_return_length=200)
async def send_inspection_data_to_api(inspection_data: Dict) -> bool:
    """Send extracted inspection data to the configured report API endpoint and save to local reports database."""
    try:
        from datetime import timezone
        checklist_count = len(inspection_data.get("checklist_items", []))
        print(f"📊 Processing inspection data with {checklist_count} checklist items...")

        # Log summary of what's being processed
        print(f"🔍 Processing data for: {inspection_data.get('vehicle_owner', 'Unknown')} - {inspection_data.get('car_type', 'Unknown')} - {inspection_data.get('unit_number', 'Unknown')}")
        
        # Ensure all required fields for the template are present with sensible defaults
        template_defaults = {
            "report_title": "Vehicle Inspection Report",
            "vehicle_info_title": "Vehicle Information", 
            "checklist_title": "Inspection Checklist",
            "signoff_title": "Inspector Sign-off",
            "vehicle_owner": "Not provided",
            "client_email": "Not provided", 
            "unit_number": "Not provided",
            "car_type": "General Vehicle",
            "odometer_reading": "Not recorded",
            "vin": "Not provided",
            "engine_hours": "Not provided",
            "make_model_year": "Not provided",
            "vehicle_make": "Not provided",
            "vehicle_model": "Not provided", 
            "vehicle_year": "Not provided",
            "inspection_date_vehicle": datetime.now(timezone.utc).strftime("%Y-%m-%d"),
            "report_generation_datetime": datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S"),
            "license_plate": "Not provided",
            "service_level": "Standard",
            "checklist_items": [],
            "inspector_name": "AI Assistant",
            "signature_url": "https://via.placeholder.com/105x30?text=Signature",
            "signoff_date": datetime.now(timezone.utc).strftime("%Y-%m-%d"), 
            "signoff_location": "Remote Inspection",
            "download_button_text": "Download PDF",
            "footer_line1": "Report generated by WFS Inspection System.",
            "footer_support_email": "<EMAIL>",
            "metadata": {}
        }
        
        # Merge defaults with extracted data (extracted data takes precedence)
        # Skip date fields if they already exist to preserve UTC timestamps from OpenAI extraction
        date_fields = ['inspection_date_vehicle', 'report_generation_datetime', 'signoff_date']
        for key, default_value in template_defaults.items():
            if key not in inspection_data or not inspection_data[key]:
                inspection_data[key] = default_value
                print(f"🔧 Set default for {key}: {default_value}")
            elif key in date_fields:
                print(f"📅 Preserving existing {key}: {inspection_data[key]}")
        
        # Add CSS classes for checklist items based on status
        checklist_items = inspection_data.get('checklist_items', [])
        for item in checklist_items:
            status = item.get('status', '').upper()
            if status in ['OK', 'PASS', 'GOOD']:
                item['status_css_class'] = 'status-ok'
            elif status in ['FAIR', 'WARNING']:
                item['status_css_class'] = 'status-fair'
            elif status in ['POOR', 'FAIL', 'REPAIR', 'REPLACE', 'FIX']:
                item['status_css_class'] = 'status-poor'
            elif status in ['N/A', 'NA', 'NOT APPLICABLE']:
                item['status_css_class'] = 'status-na'
            else:
                item['status_css_class'] = 'status-good'  # Default
        
        print(f"✅ Final inspection data ready with {len(inspection_data.get('checklist_items', []))} checklist items")
        
        # Check if we have meaningful data before saving
        meaningful_data = False
        checklist_items = inspection_data.get('checklist_items', [])
        
        # Check if any checklist items have actual content
        for item in checklist_items:
            item_name = item.get('item', '').strip()
            item_status = item.get('status', '').strip()  
            item_notes = item.get('notes', '').strip()
            if item_name and item_status and item_notes:
                meaningful_data = True
                break
        
        # Also check if basic vehicle info was extracted
        if not meaningful_data:
            vehicle_info_fields = ['vehicle_owner', 'client_email', 'unit_number', 'make_model_year', 'vehicle_make', 'car_type']
            for field in vehicle_info_fields:
                value = inspection_data.get(field, '').strip()
                if value and value not in ['Not provided', 'Unknown', 'Not recorded', 'General Vehicle']:
                    meaningful_data = True
                    print(f"✅ Found meaningful vehicle data in field '{field}': {value}")
                    break
                    
        # Also check if we have any checklist items at all (even if some are empty)
        if not meaningful_data:
            checklist_items = inspection_data.get('checklist_items', [])
            if len(checklist_items) > 0:
                meaningful_data = True
                print(f"✅ Found {len(checklist_items)} checklist items")
        
        if not meaningful_data:
            print("⚠️ WARNING: No meaningful inspection data extracted - skipping database save")
            print("📋 This likely means the conversation was too short or didn't contain inspection questions")
            return False
        
        print("✅ Meaningful inspection data found - proceeding with database save")
        
        # Always update with current UTC time before saving to override any old dates
        current_utc = datetime.now(timezone.utc)
        inspection_data['inspection_date_vehicle'] = current_utc.strftime('%Y-%m-%d')
        inspection_data['report_generation_datetime'] = current_utc.strftime('%Y-%m-%d %H:%M:%S')
        inspection_data['signoff_date'] = current_utc.strftime('%Y-%m-%d')
        
        print(f"🕐 Final UTC timestamp override: {current_utc.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📅 Final inspection date: {inspection_data['inspection_date_vehicle']}")
        print(f"📅 Final report generation: {inspection_data['report_generation_datetime']}")

        # First, save to local reports database for the dashboard
        local_saved = await save_inspection_to_reports_database(inspection_data)
        
        # Then send to external API if configured
        api_sent = False
        if REPORT_APP_URI and REPORT_APP_URI != "disabled":
            log.info(f"Sending inspection data to external API: {REPORT_APP_URI}")
            try:
                async with httpx.AsyncClient(timeout=30.0) as client:
                    response = await client.post(
                        REPORT_APP_URI,
                        headers={"Content-Type": "application/json"},
                        json=inspection_data,
                    )

                    if response.status_code in [200, 201]:
                        log.info(
                            f"Successfully sent inspection data to external API. Response: {response.status_code}"
                        )
                        api_sent = True
                    else:
                        log.error(
                            f"External API endpoint error: {response.status_code} - {response.text}"
                        )
            except Exception as api_error:
                log.error(f"Error sending inspection data to external API: {api_error}")
        else:
            log.info("External API not configured or disabled - skipping external send")

        # Return True if either local save or API send succeeded
        if local_saved:
            log.info("✅ Inspection data successfully saved to local reports database")
        if api_sent:
            log.info("✅ Inspection data successfully sent to external API")
            
        return local_saved or api_sent

    except Exception as e:
        log.error(f"Error processing inspection data: {e}")
        return False


async def save_inspection_to_reports_database(inspection_data: Dict) -> bool:
    """Save inspection data to the local reports database for the dashboard."""
    try:
        from pymongo import MongoClient
        
        print(f"🔄 Starting save to reports database...")
        print(f"📊 Database: {REPORTS_DB_NAME}, Collection: {REPORTS_COLLECTION_NAME}")
        print(f"🔗 MongoDB URI: {REPORTS_MONGO_URI}")
        print(f"📋 Inspection data keys: {list(inspection_data.keys())}")
        
        client = MongoClient(REPORTS_MONGO_URI, serverSelectionTimeoutMS=5000)
        
        # Test connection first
        client.admin.command('ping')
        print("✅ MongoDB connection successful")
        
        db = client[REPORTS_DB_NAME]
        collection = db[REPORTS_COLLECTION_NAME]
        
        # Add timestamp if not present
        if 'report_generation_datetime' not in inspection_data:
            from datetime import datetime
            inspection_data['report_generation_datetime'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Insert the inspection data
        result = collection.insert_one(inspection_data)
        
        client.close()
        
        print(f"✅ Successfully saved inspection to reports database with ID: {result.inserted_id}")
        print(f"📍 Saved to: {REPORTS_DB_NAME}.{REPORTS_COLLECTION_NAME}")
        return True
        
    except Exception as e:
        print(f"❌ Error saving inspection to reports database: {e}")
        print(f"📍 Attempted save to: {REPORTS_DB_NAME}.{REPORTS_COLLECTION_NAME}")
        print(f"🔗 Using URI: {REPORTS_MONGO_URI}")
        return False


@deep_debug(log_args=False, log_return=True, log_duration=True)
async def test_mongodb_connection():
    """Test MongoDB connection on startup."""
    try:
        result = test_mongo()
        if result["status"] == "connected":
            print(
                f"Connected to MongoDB server version: {result.get('version', 'unknown')}"
            )
            print(
                f"Using database: {DEFAULT_CLIENT_DB}, collection base: {MONGO_COLLECTION}"
            )
            
            # Test reports database connection
            try:
                from pymongo import MongoClient
                reports_client = MongoClient(REPORTS_MONGO_URI, serverSelectionTimeoutMS=5000)
                reports_db = reports_client[REPORTS_DB_NAME]
                reports_db.command('ping')
                reports_client.close()
                print(f"✅ Reports database connected: {REPORTS_DB_NAME}.{REPORTS_COLLECTION_NAME}")
                print(f"📊 Reports dashboard will be available with local data")
            except Exception as reports_error:
                print(f"⚠️  Reports database connection failed: {reports_error}")
                print("📊 Reports dashboard may not work properly")
            
            # Log external API configuration
            if REPORT_APP_URI and REPORT_APP_URI != "disabled":
                print(f"🌐 External API configured: {REPORT_APP_URI}")
            else:
                print("🌐 External API disabled - using local reports database only")
            
            return True
        elif result["status"] == "disabled":
            print("MongoDB is disabled via MONGODB_ENABLED environment variable")
            print("Transcripts will fallback to local file storage only")
            return False
        else:
            print(
                f"Warning: MongoDB connection failed: {result.get('message', 'Unknown error')}"
            )
            print("Transcripts will fallback to local file storage only")
            return False
    except Exception as e:
        print(f"Warning: MongoDB connection test failed: {e}")
        print("Transcripts will fallback to local file storage only")
        return False


# Function calling schema for structuring inspection data
INSPECTION_EXTRACTION_SCHEMA = {
    "type": "function",
    "name": "extract_inspection_data",
    "description": "Extract ALL inspection items from transcript into structured InspectionData format matching the API specification",
    "parameters": {
        "type": "object",
        "properties": {
            "report_title": {
                "type": "string",
                "description": "Title of the inspection report",
                "default": "Vehicle Inspection Report",
            },
            "vehicle_info_title": {
                "type": "string",
                "description": "Section title for vehicle information",
                "default": "Vehicle Information",
            },
            "checklist_title": {
                "type": "string",
                "description": "Section title for inspection checklist",
                "default": "Inspection Checklist",
            },
            "signoff_title": {
                "type": "string",
                "description": "Section title for inspector sign-off",
                "default": "_______________________f",
            },
            "vehicle_owner": {
                "type": "string",
                "description": "Extract from 'client's name' question in conversation",
            },
            "client_email": {
                "type": "string",
                "description": "Extract from 'client's email address' question in conversation",
            },
            "unit_number": {
                "type": "string",
                "description": "Extract from 'unit number' question in conversation",
            },
            "car_type": {
                "type": "string",
                "description": "Extract from vehicle type question - map the response to vehicle category (Light-Duty Vehicle, Power-Unit/Tractor, Trailer, Liftgate/Forklift, Hy-Rail/Heavy-Duty, General DOT Commercial)",
            },
            "odometer_reading": {
                "type": "string",
                "description": "Current odometer reading with units (e.g., '45,678 mi')",
            },
            "vin": {
                "type": "string",
                "description": "Extract unit number from conversation as VIN equivalent, or 'Not provided' if not given",
            },
            "engine_hours": {
                "type": "string",
                "description": "Estimate based on odometer reading, or 'Not provided' if cannot estimate",
                "default": "Not provided",
            },
            "make_model_year": {
                "type": "string",
                "description": "Extract from 'maker and model' question in conversation (e.g., 'Ford F-550 / 2019')",
            },
            "vehicle_make": {
                "type": "string", 
                "description": "Extract vehicle maker from 'maker and model' question (e.g., 'Ford')",
            },
            "vehicle_model": {
                "type": "string",
                "description": "Extract vehicle model from 'maker and model' question (e.g., 'F-550')",
            },
            "vehicle_year": {
                "type": "string",
                "description": "Extract vehicle year from 'maker and model' question (e.g., '2019')",
            },
            "inspection_date_vehicle": {
                "type": "string",
                "description": "Date of inspection in YYYY-MM-DD format (e.g., '2025-04-16'), use now always in that format.",
            },
            "report_generation_datetime": {
                "type": "string",
                "description": "Date and time when report was generated in 'YYYY-MM-DD HH:MM:SS' format (e.g., '2025-06-18 14:30:25'), use current timestamp.",
            },
            "license_plate": {
                "type": "string",
                "description": "Set to 'Not provided' since license plate is not asked in current conversation flow",
            },
            "service_level": {
                "type": "string",
                "description": "Service level based on vehicle type or 'Standard' as default",
                "default": "Standard",
            },
            "checklist_items": {
                "type": "array",
                "description": "Array containing EVERY individual inspection item that was asked about and answered in the conversation. Each question-answer pair becomes one array entry.",
                "items": {
                    "type": "object",
                    "properties": {
                        "item": {
                            "type": "string",
                            "description": "Specific inspection item name (e.g., 'Brakes', 'Engine Oil', 'Tires')",
                        },
                        "status": {
                            "type": "string",
                            "description": "Technician's response interpreted as status (e.g., 'OK', 'GOOD', 'FAIR', 'POOR', 'FAIL', 'N/A')",
                        },
                        "notes": {
                            "type": "string",
                            "description": "Complete technician response and any additional comments (e.g., 'Functional', 'Needs replacement', 'Good condition')",
                        },
                    },
                    "required": ["item", "status", "notes"],
                },
            },
            "inspector_name": {
                "type": "string",
                "description": "Name of the inspector from conversation (e.g., 'John Doe') or 'Workforce AI Assistant' if not provided",
            },
            "signature_url": {
                "type": "string",
                "description": "URL for signature image",
                "default": "https://via.placeholder.com/105x30?text=Signature",
            },
            "signoff_date": {
                "type": "string",
                "description": "Date of sign-off in YYYY-MM-DD format (e.g., '2025-04-16')",
            },
            "signoff_location": {
                "type": "string",
                "description": "Location where inspection was performed, or 'Remote Inspection' as default",
                "default": "Remote Inspection",
            },
            "download_button_text": {
                "type": "string",
                "description": "Text for download button",
                "default": "Download PDF",
            },
            "footer_line1": {
                "type": "string",
                "description": "Footer text line 1",
                "default": "Report generated by WFS Inspection System.",
            },
            "footer_support_email": {
                "type": "string",
                "description": "Support email for footer",
                "default": "<EMAIL>",
            },
        },
        "required": [
            "checklist_items",
            "car_type"
        ],
    },
}

if __name__ == "__main__":
    import uvicorn

    # Configure uvicorn logging to use stdout
    logging_config = uvicorn.config.LOGGING_CONFIG.copy()
    logging_config["handlers"]["default"]["stream"] = sys.stdout
    logging_config["handlers"]["access"]["stream"] = sys.stdout

    # Start the FastAPI application with explicit logging configuration
    uvicorn.run(
        app, host="0.0.0.0", port=PORT, log_config=logging_config, log_level="info"
    )
