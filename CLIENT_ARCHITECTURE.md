# Client-Centric Architecture Documentation

## 🎯 Overview

The Voice Assistant system has been restructured to use a **client-centric architecture** where each client email becomes a unique collection in MongoDB. This approach provides better performance, organization, and scalability.

## 🏗️ Architecture

### Database Structure
```
Database: Client_Activities
├── john_dot_doe_at_company_dot_com (collection)
│   ├── profile (document)
│   ├── transcript_1 (document)
│   ├── transcript_2 (document)
│   ├── inspection_1 (document)
│   └── repair_1 (document)
├── jane_dot_smith_at_fleet_dot_com (collection)
└── mike_dot_wilson_at_logistics_dot_com (collection)
```

### Document Types

#### 1. Profile Document
```json
{
  "activity_type": "profile",
  "email": "<EMAIL>",
  "client_info": {
    "name": "<PERSON>",
    "company": "ABC Logistics",
    "phone": "+1234567890"
  },
  "fleet_info": {
    "total_vehicles": 15,
    "units": {
      "UNIT001": {
        "vehicle_type": "Light-Duty",
        "make_model_year": "2020 Ford Transit",
        "last_inspection": "2024-01-15"
      }
    }
  },
  "activity_summary": {
    "total_calls": 25,
    "total_transcripts": 25,
    "total_reports": 20,
    "last_activity": "2024-01-15T10:30:00Z"
  }
}
```

#### 2. Transcript Document
```json
{
  "activity_type": "transcript",
  "email": "<EMAIL>",
  "transcript": "Full conversation text...",
  "structured_conversation": [...],
  "call_identifier": "stream_123_20240115_103000",
  "metadata": {
    "duration": 450,
    "silence_warnings_sent": 0
  }
}
```

#### 3. Inspection Document
```json
{
  "activity_type": "inspection",
  "email": "<EMAIL>",
  "work_order_number": "WO-2024-001",
  "vehicle_type": "Light-Duty",
  "inspector_name": "AI Assistant",
  "report_path": "/reports/inspection_123.pdf",
  "checklist_items": [...]
}
```

## 🚀 Benefits

### ✅ Performance
- **Faster Queries**: Small collections = faster searches
- **Efficient Indexing**: Targeted indexes per client
- **Reduced Lock Contention**: Parallel operations across clients

### ✅ Scalability
- **Horizontal Growth**: Each client scales independently
- **Resource Isolation**: Heavy clients don't affect others
- **Sharding Ready**: Easy to distribute across servers

### ✅ Organization
- **Logical Grouping**: All client data in one place
- **Easy Backup**: Per-client backup strategies
- **Clear Ownership**: Data belongs to specific clients

### ✅ Analytics
- **Client Insights**: Complete activity history per client
- **Trend Analysis**: Easy to track client patterns
- **Reporting**: Generate per-client reports efficiently

## 🔧 API Endpoints

### Client Management
```bash
# List all clients
GET /api/clients/

# Get client profile
GET /api/clients/{email}/profile

# Update client profile
PUT /api/clients/{email}/profile

# Get client activities
GET /api/clients/{email}/activities?activity_type=transcript&limit=50

# Get calendar data
GET /api/clients/{email}/calendar?start_date=2024-01-01&end_date=2024-01-31
```

### Activity Management
```bash
# Add transcript
POST /api/clients/{email}/transcript

# Add inspection
POST /api/clients/{email}/inspection

# Add repair
POST /api/clients/{email}/repair
```

### Search & Analytics
```bash
# Search clients
GET /api/clients/search?query=john

# Get fleet info
GET /api/clients/{email}/fleet

# Get inspection summary
GET /api/clients/{email}/inspections/summary

# Database statistics
GET /api/clients/stats
```

## 🔄 Migration

### From Legacy Structure
Use the migration script to convert existing data:

```bash
# Run migration
python migrate_to_client_centric.py

# Verify migration
python migrate_to_client_centric.py --verify
```

### Migration Process
1. **Extract Email**: Parse transcripts for client emails
2. **Create Collections**: Generate email-based collection names
3. **Migrate Data**: Move transcripts and inspections
4. **Update Profiles**: Aggregate client information
5. **Verify**: Check data integrity

## 💡 Usage Examples

### Python Client
```python
from client_data_manager import ClientDataManager

# Initialize
manager = ClientDataManager(mongo_uri, "Client_Activities")
manager.connect()

# Get client profile
profile = manager.get_client_profile("<EMAIL>")

# Add transcript
transcript_data = {
    "transcript": "Full conversation...",
    "call_identifier": "call_123",
    "metadata": {"duration": 300}
}
manager.add_transcript("<EMAIL>", transcript_data)

# Get activities
activities = manager.get_client_activities(
    "<EMAIL>", 
    activity_type="inspection", 
    limit=10
)
```

### REST API
```bash
# Get client profile
curl -X GET "http://localhost:10000/api/clients/<EMAIL>/profile"

# Add inspection
curl -X POST "http://localhost:10000/api/clients/<EMAIL>/inspection" \
  -H "Content-Type: application/json" \
  -d '{
    "work_order_number": "WO-2024-001",
    "vehicle_type": "Light-Duty",
    "inspector_name": "John Smith"
  }'
```

## 🔒 Security Considerations

### Email Validation
- All emails are validated using `email-validator`
- Normalized to lowercase for consistency
- Special characters escaped for MongoDB collection names

### Data Isolation
- Each client's data is isolated in separate collections
- No cross-client data leakage
- Easy to implement per-client access controls

### Backup Strategy
- Per-client backup capabilities
- Selective restore options
- Compliance-friendly data management

## 📊 Monitoring

### Key Metrics
- **Client Count**: Total number of active clients
- **Collection Size**: Average documents per client
- **Activity Rate**: Calls/inspections per client per day
- **Storage Growth**: Database size trends

### Performance Monitoring
```python
# Get database statistics
stats = manager.get_database_stats()
print(f"Total clients: {stats['total_clients']}")
print(f"Database size: {stats['total_size_mb']} MB")
```

## 🛠️ Maintenance

### Regular Tasks
1. **Index Optimization**: Monitor and optimize indexes
2. **Collection Cleanup**: Archive old inactive clients
3. **Performance Review**: Analyze query patterns
4. **Backup Verification**: Test restore procedures

### Scaling Considerations
- **Sharding**: Distribute collections across shards
- **Read Replicas**: Use read replicas for analytics
- **Archiving**: Move old data to cold storage
- **Caching**: Implement Redis for frequent queries

## 🎯 Best Practices

### Data Entry
- Always validate email addresses
- Use consistent naming conventions
- Include metadata for all activities
- Timestamp all documents

### Querying
- Use indexes effectively
- Limit result sets appropriately
- Cache frequently accessed data
- Monitor query performance

### Development
- Use the ClientDataManager class
- Handle connection errors gracefully
- Implement proper logging
- Test with realistic data volumes

## 🔮 Future Enhancements

### Planned Features
- **Real-time Notifications**: WebSocket updates for client activities
- **Advanced Analytics**: Machine learning insights per client
- **Multi-tenant Support**: Organization-level grouping
- **Data Export**: Client-specific data export tools

### Scalability Roadmap
- **Microservices**: Split into client-specific services
- **Event Sourcing**: Track all changes as events
- **CQRS**: Separate read/write models
- **GraphQL**: Flexible query interface