"""
Client API Endpoints - RESTful API for client data management
"""

from fastapi import APIRouter, HTTPException, Query, Path
from fastapi.responses import JSONResponse
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from client_data_manager import ClientDataManager
import os
import logging as log

# Initialize router
router = APIRouter(prefix="/api/clients", tags=["clients"])

# Get MongoDB configuration
MONGO_URI = os.getenv('MONGO_URI', '****************************************')
DEFAULT_CLIENT_DB = os.getenv('DEFAULT_CLIENT_DB', 'Client_Activities')

# Initialize client data manager
client_manager = ClientDataManager(MONGO_URI, DEFAULT_CLIENT_DB)

@router.on_event("startup")
async def startup_event():
    """Connect to MongoDB on startup."""
    try:
        if client_manager.connect():
            log.info("Client API connected to MongoDB successfully")
        else:
            log.error("Failed to connect Client API to MongoDB")
    except Exception as e:
        log.error(f"Error connecting Client API to MongoDB: {e}")

@router.on_event("shutdown")
async def shutdown_event():
    """Disconnect from MongoDB on shutdown."""
    try:
        client_manager.disconnect()
        log.info("Client API disconnected from MongoDB")
    except Exception as e:
        log.error(f"Error disconnecting Client API from MongoDB: {e}")

@router.get("/", response_model=List[str])
async def list_all_clients():
    """Get list of all client emails."""
    try:
        clients = client_manager.list_all_clients()
        return clients
    except Exception as e:
        log.error(f"Error listing clients: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve client list")

@router.get("/stats")
async def get_database_stats():
    """Get database statistics."""
    try:
        stats = client_manager.get_database_stats()
        return stats
    except Exception as e:
        log.error(f"Error getting database stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve database statistics")

@router.get("/{email}/profile")
async def get_client_profile(
    email: str = Path(..., description="Client email address")
):
    """Get complete client profile."""
    try:
        profile = client_manager.get_client_profile(email)
        return profile
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        log.error(f"Error getting client profile for {email}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve client profile")

@router.put("/{email}/profile")
async def update_client_profile(
    email: str = Path(..., description="Client email address"),
    updates: Dict[str, Any] = None
):
    """Update client profile."""
    try:
        if not updates:
            raise HTTPException(status_code=400, detail="No updates provided")
        
        success = client_manager.update_client_profile(email, updates)
        if success:
            return {"message": "Profile updated successfully", "email": email}
        else:
            raise HTTPException(status_code=500, detail="Failed to update profile")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        log.error(f"Error updating client profile for {email}: {e}")
        raise HTTPException(status_code=500, detail="Failed to update client profile")

@router.get("/{email}/activities")
async def get_client_activities(
    email: str = Path(..., description="Client email address"),
    activity_type: Optional[str] = Query(None, description="Filter by activity type"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of activities to return")
):
    """Get client activities with optional filtering."""
    try:
        activities = client_manager.get_client_activities(email, activity_type, limit)
        return {
            "email": email,
            "activity_type": activity_type,
            "total_activities": len(activities),
            "activities": activities
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        log.error(f"Error getting activities for {email}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve client activities")

@router.get("/{email}/calendar")
async def get_client_calendar(
    email: str = Path(..., description="Client email address"),
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)")
):
    """Get client activities for calendar view."""
    try:
        # Default to last 30 days if no dates provided
        if not start_date:
            start_dt = datetime.now() - timedelta(days=30)
        else:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        
        if not end_date:
            end_dt = datetime.now()
        else:
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        
        calendar_data = client_manager.get_client_calendar_data(email, start_dt, end_dt)
        
        return {
            "email": email,
            "start_date": start_dt.strftime("%Y-%m-%d"),
            "end_date": end_dt.strftime("%Y-%m-%d"),
            "total_events": len(calendar_data),
            "events": calendar_data
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        log.error(f"Error getting calendar data for {email}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve calendar data")

@router.post("/{email}/transcript")
async def add_transcript(
    email: str = Path(..., description="Client email address"),
    transcript_data: Dict[str, Any] = None
):
    """Add a transcript to client's collection."""
    try:
        if not transcript_data:
            raise HTTPException(status_code=400, detail="No transcript data provided")
        
        success = client_manager.add_transcript(email, transcript_data)
        if success:
            return {"message": "Transcript added successfully", "email": email}
        else:
            raise HTTPException(status_code=500, detail="Failed to add transcript")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        log.error(f"Error adding transcript for {email}: {e}")
        raise HTTPException(status_code=500, detail="Failed to add transcript")

@router.post("/{email}/inspection")
async def add_inspection(
    email: str = Path(..., description="Client email address"),
    inspection_data: Dict[str, Any] = None
):
    """Add an inspection to client's collection."""
    try:
        if not inspection_data:
            raise HTTPException(status_code=400, detail="No inspection data provided")
        
        success = client_manager.add_inspection(email, inspection_data)
        if success:
            return {"message": "Inspection added successfully", "email": email}
        else:
            raise HTTPException(status_code=500, detail="Failed to add inspection")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        log.error(f"Error adding inspection for {email}: {e}")
        raise HTTPException(status_code=500, detail="Failed to add inspection")

@router.post("/{email}/repair")
async def add_repair(
    email: str = Path(..., description="Client email address"),
    repair_data: Dict[str, Any] = None
):
    """Add a repair to client's collection."""
    try:
        if not repair_data:
            raise HTTPException(status_code=400, detail="No repair data provided")
        
        success = client_manager.add_repair(email, repair_data)
        if success:
            return {"message": "Repair added successfully", "email": email}
        else:
            raise HTTPException(status_code=500, detail="Failed to add repair")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        log.error(f"Error adding repair for {email}: {e}")
        raise HTTPException(status_code=500, detail="Failed to add repair")

@router.get("/{email}/fleet")
async def get_fleet_info(
    email: str = Path(..., description="Client email address")
):
    """Get client's fleet information."""
    try:
        profile = client_manager.get_client_profile(email)
        fleet_info = profile.get("fleet_info", {})
        
        return {
            "email": email,
            "fleet_info": fleet_info
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        log.error(f"Error getting fleet info for {email}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve fleet information")

@router.get("/{email}/inspections/summary")
async def get_inspection_summary(
    email: str = Path(..., description="Client email address")
):
    """Get client's inspection summary."""
    try:
        profile = client_manager.get_client_profile(email)
        inspection_history = profile.get("inspection_history", {})
        
        # Get recent inspections
        recent_inspections = client_manager.get_client_activities(
            email, 
            activity_type="inspection", 
            limit=10
        )
        
        return {
            "email": email,
            "inspection_history": inspection_history,
            "recent_inspections": recent_inspections
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        log.error(f"Error getting inspection summary for {email}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve inspection summary")

@router.get("/search")
async def search_clients(
    query: str = Query(..., description="Search query"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of results")
):
    """Search clients by email or name."""
    try:
        all_clients = client_manager.list_all_clients()
        
        # Simple search - in production, you'd want more sophisticated search
        matching_clients = []
        query_lower = query.lower()
        
        for client_email in all_clients:
            if query_lower in client_email.lower():
                matching_clients.append(client_email)
                if len(matching_clients) >= limit:
                    break
        
        # Also search by client names in profiles (this would be expensive for large datasets)
        if len(matching_clients) < limit:
            for client_email in all_clients[:50]:  # Limit profile searches
                if client_email not in matching_clients:
                    try:
                        profile = client_manager.get_client_profile(client_email)
                        client_name = profile.get("client_info", {}).get("name", "").lower()
                        if query_lower in client_name:
                            matching_clients.append(client_email)
                            if len(matching_clients) >= limit:
                                break
                    except Exception:
                        continue  # Skip if profile can't be loaded
        
        return {
            "query": query,
            "total_results": len(matching_clients),
            "clients": matching_clients
        }
    except Exception as e:
        log.error(f"Error searching clients: {e}")
        raise HTTPException(status_code=500, detail="Failed to search clients")