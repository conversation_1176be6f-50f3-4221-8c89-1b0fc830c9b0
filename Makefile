.PHONY: run test install dev-install docker-start docker-stop docker-restart docker-rebuild test-mongodb verify-transcripts install-report-deps generate-report

# Install production dependencies
install:
	pip install -r requirements.txt

# Install both production and development dependencies
dev-install: install
	pip install -r requirements-dev.txt

# Install report generation dependencies
install-report-deps:
	pip install -r requirements-report.txt

# Run the FastAPI application using uvicorn
run:
	uvicorn main:app --host 0.0.0.0 --port 10000

# Run tests with pytest
test:
	pytest --maxfail=1 --disable-warnings -q

# Docker commands
docker-start:
	docker-compose up -d

docker-stop:
	docker-compose down

docker-restart:
	docker-compose down
	docker-compose up -d

docker-rebuild:
	docker-compose down
	docker-compose build
	docker-compose up -d

# Test MongoDB connection
test-mongodb:
	python test_mongodb.py

verify-transcripts:
	python scripts/verify_transcripts.py

# Report generation
generate-report:
	@if [ -z "$(TRANSCRIPT_ID)" ]; then \
		python generate_inspection_report.py; \
	else \
		python generate_inspection_report.py --transcript-id $(TRANSCRIPT_ID); \
	fi

# Generate report with intermediate files
generate-report-with-files:
	@if [ -z "$(TRANSCRIPT_ID)" ]; then \
		python generate_inspection_report.py --save-intermediate; \
	else \
		python generate_inspection_report.py --transcript-id $(TRANSCRIPT_ID) --save-intermediate; \
	fi

# Generate report with custom output directory
generate-report-to:
	@if [ -z "$(OUTPUT_DIR)" ]; then \
		echo "Usage: make generate-report-to OUTPUT_DIR=path/to/output [TRANSCRIPT_ID=your_transcript_id]"; \
	elif [ -z "$(TRANSCRIPT_ID)" ]; then \
		python generate_inspection_report.py --output-dir $(OUTPUT_DIR); \
	else \
		python generate_inspection_report.py --transcript-id $(TRANSCRIPT_ID) --output-dir $(OUTPUT_DIR); \
	fi