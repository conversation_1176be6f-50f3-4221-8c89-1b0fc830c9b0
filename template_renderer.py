#!/usr/bin/env python
"""
This module renders inspection data into HTML using Jinja2 templates.
"""
import os
import json
import logging
from typing import Dict, Any, Optional
from pathlib import Path

import jinja2

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Paths
TEMPLATE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'templates')
DEFAULT_TEMPLATE = 'inspection_template.html'


def get_jinja_environment() -> jinja2.Environment:
    """
    Create and return a Jinja2 environment configured for our templates.
    """
    return jinja2.Environment(
        loader=jinja2.FileSystemLoader(TEMPLATE_DIR),
        autoescape=jinja2.select_autoescape(['html', 'xml']),
        trim_blocks=True,
        lstrip_blocks=True
    )


def render_inspection_data(
    data: Dict[str, Any],
    template_name: Optional[str] = None,
    output_file: Optional[str] = None
) -> str:
    """
    Render inspection data using a Jinja2 template.
    
    Args:
        data: Dictionary containing inspection data formatted for the template
        template_name: Name of the template file to use (defaults to DEFAULT_TEMPLATE)
        output_file: Path to save the rendered HTML (optional)
        
    Returns:
        Rendered HTML as a string
    """
    try:
        env = get_jinja_environment()
        template = env.get_template(template_name or DEFAULT_TEMPLATE)
        rendered_html = template.render(**data)
        
        if output_file:
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(rendered_html)
            logger.info(f"Rendered HTML saved to {output_file}")
        
        return rendered_html
    
    except Exception as e:
        logger.error(f"Error rendering template: {e}")
        raise


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Render inspection data to HTML")
    parser.add_argument("--data", required=True, help="JSON file containing inspection data")
    parser.add_argument("--template", help=f"Template file to use (default: {DEFAULT_TEMPLATE})")
    parser.add_argument("--output", help="Output file for the rendered HTML")
    args = parser.parse_args()
    
    try:
        with open(args.data, 'r') as f:
            inspection_data = json.load(f)
        
        rendered_html = render_inspection_data(
            inspection_data,
            template_name=args.template,
            output_file=args.output
        )
        
        if not args.output:
            print(rendered_html)
    
    except Exception as e:
        logger.error(f"Error running template renderer: {e}")
        raise