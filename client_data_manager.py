"""
Client Data Manager - Email-based MongoDB Collections
Manages client activities with email as the primary key
"""

import os
import json
import time
import logging as log
from datetime import datetime
from typing import Dict, List, Any, Optional
from pymongo import MongoClient, ASCENDING, DESCENDING
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError
from pymongo.collection import Collection
from email_validator import validate_email, EmailNotValidError

class ClientDataManager:
    """
    Manages client data using email-based collections in MongoDB.
    Each client gets their own collection for optimal performance and organization.
    """
    
    def __init__(self, mongo_uri: str, database_name: str = "Client_Activities"):
        self.mongo_uri = mongo_uri
        self.database_name = database_name
        self.client = None
        self.db = None
        
    def connect(self) -> bool:
        """Establish connection to MongoDB."""
        try:
            self.client = MongoClient(self.mongo_uri, serverSelectionTimeoutMS=5000)
            # Test connection
            self.client.server_info()
            self.db = self.client[self.database_name]
            log.info(f"Connected to MongoDB database: {self.database_name}")
            return True
        except Exception as e:
            log.error(f"Failed to connect to MongoDB: {e}")
            return False
    
    def disconnect(self):
        """Close MongoDB connection."""
        if self.client:
            self.client.close()
            log.info("MongoDB connection closed")
    
    def _normalize_email(self, email: str) -> str:
        """Normalize email for use as collection name."""
        try:
            # Validate and normalize email
            validated_email = validate_email(email)
            normalized = validated_email.email.lower()
            
            # Replace dots and special chars for MongoDB collection naming
            # MongoDB collection names can't contain certain characters
            collection_name = normalized.replace(".", "_dot_").replace("@", "_at_").replace("+", "_plus_")
            return collection_name
        except EmailNotValidError as e:
            log.error(f"Invalid email format: {email} - {e}")
            raise ValueError(f"Invalid email format: {email}")
    
    def _get_client_collection(self, email: str) -> Collection:
        """Get or create collection for a specific client email."""
        if not self.db:
            raise ConnectionError("Not connected to MongoDB")
        
        collection_name = self._normalize_email(email)
        collection = self.db[collection_name]
        
        # Create indexes for better performance
        try:
            collection.create_index([("timestamp", DESCENDING)])
            collection.create_index([("activity_type", ASCENDING)])
            collection.create_index([("work_order_number", ASCENDING)])
            collection.create_index([("call_identifier", ASCENDING)], unique=True, sparse=True)
        except Exception as e:
            log.warning(f"Index creation warning for {collection_name}: {e}")
        
        return collection
    
    def get_client_profile(self, email: str) -> Dict[str, Any]:
        """Get complete client profile with all activities."""
        try:
            collection = self._get_client_collection(email)
            
            # Get the latest profile document
            profile = collection.find_one(
                {"activity_type": "profile"},
                sort=[("timestamp", DESCENDING)]
            )
            
            if not profile:
                # Create initial profile
                profile = self._create_initial_profile(email)
                collection.insert_one(profile)
                log.info(f"Created initial profile for {email}")
            
            return profile
        except Exception as e:
            log.error(f"Error getting client profile for {email}: {e}")
            return self._create_initial_profile(email)
    
    def _create_initial_profile(self, email: str) -> Dict[str, Any]:
        """Create initial client profile structure."""
        return {
            "activity_type": "profile",
            "email": email,
            "timestamp": time.time(),
            "created_date": datetime.now(),
            "last_updated": datetime.now(),
            "client_info": {
                "name": "",
                "company": "",
                "phone": "",
                "address": "",
                "preferred_language": "en"
            },
            "fleet_info": {
                "total_vehicles": 0,
                "vehicle_types": [],
                "units": {}  # unit_number -> vehicle_details
            },
            "inspection_history": {
                "total_inspections": 0,
                "last_inspection_date": None,
                "inspection_types": {},
                "compliance_status": "unknown"
            },
            "communication_preferences": {
                "notification_email": email,
                "sms_notifications": False,
                "report_delivery": "email"
            },
            "activity_summary": {
                "total_calls": 0,
                "total_transcripts": 0,
                "total_reports": 0,
                "last_activity": None
            }
        }
    
    def update_client_profile(self, email: str, updates: Dict[str, Any]) -> bool:
        """Update client profile with new information."""
        try:
            collection = self._get_client_collection(email)
            
            # Get current profile
            current_profile = self.get_client_profile(email)
            
            # Deep merge updates
            updated_profile = self._deep_merge(current_profile, updates)
            updated_profile["last_updated"] = datetime.now()
            updated_profile["timestamp"] = time.time()
            
            # Insert new version (keeping history)
            result = collection.insert_one(updated_profile)
            log.info(f"Updated profile for {email}, document ID: {result.inserted_id}")
            return True
        except Exception as e:
            log.error(f"Error updating client profile for {email}: {e}")
            return False
    
    def add_transcript(self, email: str, transcript_data: Dict[str, Any]) -> bool:
        """Add transcript activity to client collection."""
        try:
            collection = self._get_client_collection(email)
            
            # Prepare transcript document
            transcript_doc = {
                "activity_type": "transcript",
                "email": email,
                "timestamp": time.time(),
                "date_time": datetime.now(),
                **transcript_data
            }
            
            # Insert transcript
            result = collection.insert_one(transcript_doc)
            
            # Update profile activity summary
            self._update_activity_summary(email, "transcript")
            
            log.info(f"Added transcript for {email}, document ID: {result.inserted_id}")
            return True
        except Exception as e:
            log.error(f"Error adding transcript for {email}: {e}")
            return False
    
    def add_inspection(self, email: str, inspection_data: Dict[str, Any]) -> bool:
        """Add inspection activity to client collection."""
        try:
            collection = self._get_client_collection(email)
            
            # Prepare inspection document
            inspection_doc = {
                "activity_type": "inspection",
                "email": email,
                "timestamp": time.time(),
                "date_time": datetime.now(),
                **inspection_data
            }
            
            # Insert inspection
            result = collection.insert_one(inspection_doc)
            
            # Update profile with inspection info
            self._update_inspection_history(email, inspection_data)
            self._update_activity_summary(email, "inspection")
            
            log.info(f"Added inspection for {email}, document ID: {result.inserted_id}")
            return True
        except Exception as e:
            log.error(f"Error adding inspection for {email}: {e}")
            return False
    
    def add_repair(self, email: str, repair_data: Dict[str, Any]) -> bool:
        """Add repair activity to client collection."""
        try:
            collection = self._get_client_collection(email)
            
            # Prepare repair document
            repair_doc = {
                "activity_type": "repair",
                "email": email,
                "timestamp": time.time(),
                "date_time": datetime.now(),
                **repair_data
            }
            
            # Insert repair
            result = collection.insert_one(repair_doc)
            
            # Update profile activity summary
            self._update_activity_summary(email, "repair")
            
            log.info(f"Added repair for {email}, document ID: {result.inserted_id}")
            return True
        except Exception as e:
            log.error(f"Error adding repair for {email}: {e}")
            return False
    
    def get_client_activities(self, email: str, activity_type: Optional[str] = None, 
                            limit: int = 100) -> List[Dict[str, Any]]:
        """Get client activities with optional filtering."""
        try:
            collection = self._get_client_collection(email)
            
            query = {"email": email}
            if activity_type:
                query["activity_type"] = activity_type
            
            activities = list(collection.find(
                query,
                sort=[("timestamp", DESCENDING)],
                limit=limit
            ))
            
            # Convert ObjectId to string for JSON serialization
            for activity in activities:
                if "_id" in activity:
                    activity["_id"] = str(activity["_id"])
            
            return activities
        except Exception as e:
            log.error(f"Error getting activities for {email}: {e}")
            return []
    
    def get_client_calendar_data(self, email: str, start_date: datetime, 
                               end_date: datetime) -> List[Dict[str, Any]]:
        """Get client activities for calendar view."""
        try:
            collection = self._get_client_collection(email)
            
            start_timestamp = start_date.timestamp()
            end_timestamp = end_date.timestamp()
            
            activities = list(collection.find({
                "email": email,
                "timestamp": {"$gte": start_timestamp, "$lte": end_timestamp},
                "activity_type": {"$in": ["inspection", "repair", "transcript"]}
            }, sort=[("timestamp", ASCENDING)]))
            
            # Format for calendar
            calendar_events = []
            for activity in activities:
                event = {
                    "id": str(activity["_id"]),
                    "title": self._format_calendar_title(activity),
                    "date": activity["date_time"].strftime("%Y-%m-%d"),
                    "type": activity["activity_type"],
                    "details": self._extract_calendar_details(activity)
                }
                calendar_events.append(event)
            
            return calendar_events
        except Exception as e:
            log.error(f"Error getting calendar data for {email}: {e}")
            return []
    
    def _update_activity_summary(self, email: str, activity_type: str):
        """Update activity summary in client profile."""
        try:
            profile = self.get_client_profile(email)
            
            # Update counters
            if activity_type == "transcript":
                profile["activity_summary"]["total_calls"] += 1
                profile["activity_summary"]["total_transcripts"] += 1
            elif activity_type == "inspection":
                profile["activity_summary"]["total_reports"] += 1
                profile["inspection_history"]["total_inspections"] += 1
                profile["inspection_history"]["last_inspection_date"] = datetime.now()
            
            profile["activity_summary"]["last_activity"] = datetime.now()
            
            # Save updated profile
            self.update_client_profile(email, profile)
        except Exception as e:
            log.error(f"Error updating activity summary for {email}: {e}")
    
    def _update_inspection_history(self, email: str, inspection_data: Dict[str, Any]):
        """Update inspection history in client profile."""
        try:
            profile = self.get_client_profile(email)
            
            # Update vehicle info if present
            if "work_order_number" in inspection_data:
                unit_number = inspection_data["work_order_number"]
                vehicle_info = {
                    "last_inspection": datetime.now(),
                    "vehicle_type": inspection_data.get("vehicle_type", "Unknown"),
                    "make_model_year": inspection_data.get("make_model_year", ""),
                    "odometer_reading": inspection_data.get("odometer_reading", ""),
                    "vin": inspection_data.get("vin", "")
                }
                profile["fleet_info"]["units"][unit_number] = vehicle_info
            
            # Update inspection types
            vehicle_type = inspection_data.get("vehicle_type", "Unknown")
            if vehicle_type not in profile["inspection_history"]["inspection_types"]:
                profile["inspection_history"]["inspection_types"][vehicle_type] = 0
            profile["inspection_history"]["inspection_types"][vehicle_type] += 1
            
            # Save updated profile
            self.update_client_profile(email, profile)
        except Exception as e:
            log.error(f"Error updating inspection history for {email}: {e}")
    
    def _deep_merge(self, dict1: Dict, dict2: Dict) -> Dict:
        """Deep merge two dictionaries."""
        result = dict1.copy()
        for key, value in dict2.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        return result
    
    def _format_calendar_title(self, activity: Dict[str, Any]) -> str:
        """Format activity title for calendar display."""
        activity_type = activity.get("activity_type", "Activity")
        if activity_type == "inspection":
            vehicle_type = activity.get("vehicle_type", "Vehicle")
            return f"{vehicle_type} Inspection"
        elif activity_type == "repair":
            return f"Repair - {activity.get('repair_type', 'General')}"
        elif activity_type == "transcript":
            return "Voice Call"
        return activity_type.title()
    
    def _extract_calendar_details(self, activity: Dict[str, Any]) -> Dict[str, Any]:
        """Extract relevant details for calendar view."""
        details = {
            "timestamp": activity.get("timestamp"),
            "date_time": activity.get("date_time")
        }
        
        if activity.get("activity_type") == "inspection":
            details.update({
                "work_order": activity.get("work_order_number"),
                "vehicle_type": activity.get("vehicle_type"),
                "inspector": activity.get("inspector_name")
            })
        elif activity.get("activity_type") == "transcript":
            details.update({
                "call_id": activity.get("call_id"),
                "duration": activity.get("metadata", {}).get("duration")
            })
        
        return details

    def list_all_clients(self) -> List[str]:
        """List all client emails (collection names)."""
        try:
            if not self.db:
                raise ConnectionError("Not connected to MongoDB")
            
            collection_names = self.db.list_collection_names()
            
            # Convert collection names back to emails
            emails = []
            for name in collection_names:
                if "_at_" in name:  # Only collections that represent emails
                    email = name.replace("_dot_", ".").replace("_at_", "@").replace("_plus_", "+")
                    emails.append(email)
            
            return sorted(emails)
        except Exception as e:
            log.error(f"Error listing clients: {e}")
            return []

    def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics."""
        try:
            if not self.db:
                raise ConnectionError("Not connected to MongoDB")
            
            stats = self.db.command("dbstats")
            collection_count = len(self.db.list_collection_names())
            
            return {
                "database_name": self.database_name,
                "total_clients": collection_count,
                "total_size_mb": round(stats.get("dataSize", 0) / (1024 * 1024), 2),
                "total_documents": stats.get("objects", 0),
                "average_object_size": round(stats.get("avgObjSize", 0), 2)
            }
        except Exception as e:
            log.error(f"Error getting database stats: {e}")
            return {}