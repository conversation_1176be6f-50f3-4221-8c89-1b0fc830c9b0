# CLAUDE.md

This file provides guidance to Claude <PERSON> when working with code in this repository.

## Project Overview

This is a **Voice Assistant for Vehicle Inspections** that uses:
- **FastAPI** as the web framework
- **Twilio Voice API** for phone call handling
- **OpenAI Realtime API** for AI conversations
- **MongoDB** for data storage with client-centric architecture
- **PDF generation** for inspection reports

The system conducts AI-powered vehicle inspections over phone calls, transcribes conversations, and generates professional PDF reports.

## Ongoing Tasks and Notes

### Completed Tasks
- Implemented tasks tracking mechanism in memory file
- Added comprehensive documentation for project structure
- Enhanced error handling and logging mechanisms
- Developed client-centric MongoDB data architecture
- Created WebSocket flow for real-time voice processing
- Updated memory file with tasks and port information
- Documented ports used in the project:
  - FastAPI default port: 8000
  - WebSocket port: 8080
  - MongoDB default port: 27017

### Current Development Notes
- Continuous improvement of AI conversation handling
- Refining PDF report generation process
- Optimizing MongoDB query performance
- Expanding test coverage for edge cases
- Implementing more robust WebSocket connection management

### Working Principles
- Always check branch and previous commits not made by the AI assistant before making changes
- Maintain a collaborative approach to code development

### Project Management Notes
- Copied CLAUDE.md to @reports/ folder for future project decoupling