version: '3.8'

services:
  mongodb:
    image: mongo:latest
    ports:
      - "27027:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
    restart: always
    
  voice-assistant:
    build: .
    ports:
      - "10500:10500"
    environment:
      - MONGO_URI=**************************************
      - MONG<PERSON>_DB=voice_assistant
      - M<PERSON><PERSON><PERSON>_COLLECTION=transcripts
      - PORT=10500
    depends_on:
      - mongodb
    volumes:
      - ./transcripts:/app/transcripts
      - ./reports:/app/reports
  
  reports-ui:
    build:
      context: .
      dockerfile: Dockerfile.reports
    ports:
      - "10501:10001"
    environment:
      - MONGO_URI=**************************************
      - <PERSON><PERSON><PERSON><PERSON>_<PERSON>=voice_assistant
      - REPORTS_PORT=10001
    depends_on:
      - mongodb
      - voice-assistant
    volumes:
      - ./reports:/app/reports
      - ./static:/app/static

volumes:
  mongodb_data: