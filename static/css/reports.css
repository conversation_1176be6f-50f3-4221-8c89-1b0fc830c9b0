/* AIREADY Reports Dashboard Styles */
:root {
  --primary-blue: #0f3460;
  --secondary-blue: #1a4b8c;
  --accent-blue: #1b5ba2;
  --highlight: #2a81e0;
  --text-light: #f0f4f8;
  --white: #fff;
  --dark-text: #102a43;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --shadow-sm: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  --shadow-md: 0 4px 6px rgba(0,0,0,0.1), 0 1px 3px rgba(0,0,0,0.08);
  --shadow-lg: 0 10px 25px rgba(0,0,0,0.1), 0 5px 10px rgba(0,0,0,0.05);
  --font-main: '<PERSON><PERSON><PERSON>', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Helvetica, Arial, sans-serif;
  --border-radius: 8px;
}

body {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  font-family: var(--font-main);
  margin: 0;
  padding: 0;
  color: var(--dark-text);
  min-height: 100vh;
}

.container {
  background: var(--white);
  max-width: 1100px;
  margin: 40px auto;
  box-shadow: var(--shadow-lg);
  border-radius: var(--border-radius);
  padding: 2.5rem;
}

.header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.logo {
  font-weight: 700;
  font-size: 1.75rem;
  color: var(--accent-blue);
  margin-bottom: 0.5rem;
  letter-spacing: 1px;
}

.title {
  font-weight: 900;
  font-size: 2.5rem;
  color: var(--primary-blue);
  margin-top: 0.5rem;
  margin-bottom: 0.75rem;
}

.subtitle {
  color: var(--gray-600);
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

.search-container {
  margin-bottom: 1.5rem;
}

.search-input {
  width: 100%;
  padding: 0.8rem 1rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--gray-300);
  font-family: var(--font-main);
  font-size: 1rem;
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--accent-blue);
  box-shadow: 0 0 0 3px rgba(27, 91, 162, 0.2);
}

.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.report-card {
  background: var(--gray-100);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.report-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.report-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: var(--accent-blue);
}

.report-card h3 {
  margin-top: 0.5rem;
  margin-bottom: 0.75rem;
  color: var(--primary-blue);
  font-weight: 700;
  font-size: 1.25rem;
}

.report-details {
  color: var(--gray-600);
  margin-bottom: 0.75rem;
  flex-grow: 1;
}

.report-detail {
  display: flex;
  margin-bottom: 0.5rem;
}

.detail-label {
  flex: 0 0 40%;
  font-weight: 600;
  color: var(--gray-600);
}

.detail-value {
  flex: 0 0 60%;
  color: var(--dark-text);
}

.report-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: auto;
}

.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--accent-blue);
  color: var(--white);
  padding: 0.6rem 1.25rem;
  border-radius: var(--border-radius);
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.button:hover {
  background: var(--primary-blue);
}

.button.secondary {
  background: var(--gray-200);
  color: var(--gray-600);
}

.button.secondary:hover {
  background: var(--gray-300);
  color: var(--dark-text);
}

.button svg {
  margin-right: 0.5rem;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  color: var(--gray-500);
}

.spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.spinner::before {
  content: "";
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 4px solid var(--gray-300);
  border-top-color: var(--accent-blue);
  animation: spin 1s linear infinite;
}

.footer {
  text-align: center;
  font-size: 0.9rem;
  color: var(--gray-500);
  margin-top: 2.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .container {
    padding: 1.5rem;
    margin: 1rem;
  }
  
  .reports-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }
  
  .title {
    font-size: 1.8rem;
  }
}