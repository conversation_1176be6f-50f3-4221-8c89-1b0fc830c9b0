#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to analyze existing conversation transcripts and identify patterns.
This helps understand what types of conversations are happening and how to extract data from them.
"""

import json
import os
import re
from collections import defaultdict, Counter
from typing import List, Dict, Any

def load_transcript_files() -> List[Dict[str, Any]]:
    """Load all transcript files from the transcripts directory."""
    transcripts = []
    transcripts_dir = "transcripts"
    
    if not os.path.exists(transcripts_dir):
        print(f"❌ Transcripts directory not found: {transcripts_dir}")
        return transcripts
    
    for filename in os.listdir(transcripts_dir):
        if filename.endswith('.json'):
            filepath = os.path.join(transcripts_dir, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    transcript = json.load(f)
                    transcript['filename'] = filename
                    transcripts.append(transcript)
            except Exception as e:
                print(f"⚠️ Error loading {filename}: {e}")
    
    return transcripts

def analyze_conversation_structure(transcript: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze the structure of a single conversation."""
    analysis = {
        'has_structured_conversation': False,
        'conversation_length': 0,
        'assistant_messages': 0,
        'user_messages': 0,
        'questions_asked': [],
        'vehicle_info_mentioned': [],
        'inspection_terms': [],
        'conversation_type': 'unknown'
    }
    
    # Check if structured conversation exists
    structured_conv = transcript.get('structured_conversation', [])
    if structured_conv:
        analysis['has_structured_conversation'] = True
        analysis['conversation_length'] = len(structured_conv)
        
        # Analyze each message
        for item in structured_conv:
            role = item.get('role', '')
            content = item.get('content', '').lower()
            
            if role == 'assistant':
                analysis['assistant_messages'] += 1
                
                # Look for questions
                if '?' in content:
                    analysis['questions_asked'].append(content)
                
                # Look for vehicle-related terms
                vehicle_terms = ['unit number', 'maker', 'model', 'vehicle', 'car', 'truck', 'odometer', 'vin']
                for term in vehicle_terms:
                    if term in content:
                        analysis['vehicle_info_mentioned'].append(term)
                
                # Look for inspection terms
                inspection_terms = ['inspection', 'check', 'condition', 'brake', 'tire', 'engine', 'oil', 'light']
                for term in inspection_terms:
                    if term in content:
                        analysis['inspection_terms'].append(term)
                        
            elif role == 'user':
                analysis['user_messages'] += 1
    
    # Analyze raw transcript if no structured conversation
    else:
        raw_transcript = transcript.get('transcript', '').lower()
        if raw_transcript:
            # Count questions
            analysis['questions_asked'] = re.findall(r'[^.!?]*\?', raw_transcript)
            
            # Look for vehicle terms
            vehicle_terms = ['unit', 'vehicle', 'car', 'truck', 'ford', 'chevy', 'toyota']
            for term in vehicle_terms:
                if term in raw_transcript:
                    analysis['vehicle_info_mentioned'].append(term)
            
            # Look for inspection terms
            inspection_terms = ['inspection', 'check', 'brake', 'tire', 'engine', 'oil']
            for term in inspection_terms:
                if term in raw_transcript:
                    analysis['inspection_terms'].append(term)
    
    # Classify conversation type
    if len(analysis['inspection_terms']) > 2:
        analysis['conversation_type'] = 'inspection'
    elif len(analysis['vehicle_info_mentioned']) > 1:
        analysis['conversation_type'] = 'vehicle_related'
    elif 'work order' in transcript.get('transcript', '').lower():
        analysis['conversation_type'] = 'work_order'
    else:
        analysis['conversation_type'] = 'other'
    
    return analysis

def extract_potential_data_points(transcript: Dict[str, Any]) -> Dict[str, Any]:
    """Extract potential data points that could be used for inspection reports."""
    data_points = {
        'client_name': None,
        'email': None,
        'unit_number': None,
        'vehicle_make_model': None,
        'odometer': None,
        'inspection_items': []
    }
    
    # Get full text to search
    full_text = transcript.get('transcript', '')
    structured_conv = transcript.get('structured_conversation', [])
    
    # Extract email addresses
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    emails = re.findall(email_pattern, full_text, re.IGNORECASE)
    if emails:
        data_points['email'] = emails[0]
    
    # Extract unit numbers
    unit_patterns = [
        r'unit[:\s#]*([A-Z0-9-]+)',
        r'#([A-Z0-9-]+)',
        r'unit number[:\s]*([A-Z0-9-]+)'
    ]
    for pattern in unit_patterns:
        matches = re.findall(pattern, full_text, re.IGNORECASE)
        if matches:
            data_points['unit_number'] = matches[0]
            break
    
    # Extract vehicle make/model
    vehicle_patterns = [
        r'(ford|chevy|toyota|honda|nissan|gmc|ram|dodge)\s+([a-z0-9-]+)',
        r'(\d{4})\s+(ford|chevy|toyota|honda|nissan|gmc|ram|dodge)\s+([a-z0-9-]+)'
    ]
    for pattern in vehicle_patterns:
        matches = re.findall(pattern, full_text, re.IGNORECASE)
        if matches:
            data_points['vehicle_make_model'] = ' '.join(matches[0])
            break
    
    # Extract names (look for patterns after "name" or "customer")
    name_patterns = [
        r'name[:\s]+([A-Z][a-z]+\s+[A-Z][a-z]+)',
        r'customer[:\s]+([A-Z][a-z]+\s+[A-Z][a-z]+)'
    ]
    for pattern in name_patterns:
        matches = re.findall(pattern, full_text, re.IGNORECASE)
        if matches:
            data_points['client_name'] = matches[0]
            break
    
    # Extract odometer reading
    odometer_patterns = [
        r'odometer[:\s]+([0-9,]+)\s*(miles?|mi|km)',
        r'([0-9,]+)\s*(miles?|mi)\s*on\s*odometer'
    ]
    for pattern in odometer_patterns:
        matches = re.findall(pattern, full_text, re.IGNORECASE)
        if matches:
            data_points['odometer'] = f"{matches[0][0]} {matches[0][1]}"
            break
    
    # Extract inspection items from structured conversation
    if structured_conv:
        for i, item in enumerate(structured_conv):
            if item.get('role') == 'assistant' and '?' in item.get('content', ''):
                question = item.get('content', '')
                # Look for next user response
                if i + 1 < len(structured_conv) and structured_conv[i + 1].get('role') == 'user':
                    response = structured_conv[i + 1].get('content', '')
                    
                    # Extract inspection item name from question
                    inspection_keywords = ['brake', 'tire', 'engine', 'oil', 'light', 'door', 'window', 'seat']
                    for keyword in inspection_keywords:
                        if keyword in question.lower():
                            data_points['inspection_items'].append({
                                'item': keyword.title(),
                                'question': question,
                                'response': response
                            })
                            break
    
    return data_points

def main():
    """Main analysis function."""
    print("🔍 Analyzing conversation transcripts...")
    
    # Load all transcripts
    transcripts = load_transcript_files()
    print(f"📁 Found {len(transcripts)} transcript files")
    
    if not transcripts:
        print("❌ No transcripts found to analyze")
        return
    
    # Analyze each transcript
    all_analyses = []
    conversation_types = Counter()
    total_questions = 0
    total_vehicle_terms = 0
    total_inspection_terms = 0
    
    print("\n" + "="*80)
    print("INDIVIDUAL TRANSCRIPT ANALYSIS")
    print("="*80)
    
    for transcript in transcripts:
        filename = transcript.get('filename', 'unknown')
        analysis = analyze_conversation_structure(transcript)
        data_points = extract_potential_data_points(transcript)
        
        print(f"\n📄 {filename}")
        print(f"   Type: {analysis['conversation_type']}")
        print(f"   Length: {analysis['conversation_length']} messages")
        print(f"   Questions: {len(analysis['questions_asked'])}")
        print(f"   Vehicle terms: {len(analysis['vehicle_info_mentioned'])}")
        print(f"   Inspection terms: {len(analysis['inspection_terms'])}")
        
        # Show extracted data points
        if any(data_points.values()):
            print(f"   📊 Extracted data:")
            for key, value in data_points.items():
                if value:
                    if key == 'inspection_items':
                        print(f"      {key}: {len(value)} items")
                    else:
                        print(f"      {key}: {value}")
        
        all_analyses.append(analysis)
        conversation_types[analysis['conversation_type']] += 1
        total_questions += len(analysis['questions_asked'])
        total_vehicle_terms += len(analysis['vehicle_info_mentioned'])
        total_inspection_terms += len(analysis['inspection_terms'])
    
    # Summary statistics
    print("\n" + "="*80)
    print("SUMMARY STATISTICS")
    print("="*80)
    
    print(f"📊 Conversation Types:")
    for conv_type, count in conversation_types.items():
        percentage = (count / len(transcripts)) * 100
        print(f"   {conv_type}: {count} ({percentage:.1f}%)")
    
    print(f"\n📈 Overall Stats:")
    print(f"   Total questions asked: {total_questions}")
    print(f"   Total vehicle terms found: {total_vehicle_terms}")
    print(f"   Total inspection terms found: {total_inspection_terms}")
    print(f"   Average questions per conversation: {total_questions / len(transcripts):.1f}")
    
    # Recommendations
    print("\n" + "="*80)
    print("RECOMMENDATIONS FOR EXTRACTION IMPROVEMENT")
    print("="*80)
    
    inspection_conversations = sum(1 for a in all_analyses if a['conversation_type'] == 'inspection')
    if inspection_conversations == 0:
        print("❌ No clear inspection conversations found!")
        print("   Recommendation: Make extraction more flexible to handle informal conversations")
    
    if total_vehicle_terms > 0:
        print("✅ Vehicle information is being discussed")
        print("   Recommendation: Improve vehicle info extraction patterns")
    
    if total_questions < len(transcripts) * 2:
        print("⚠️ Low number of questions per conversation")
        print("   Recommendation: Handle statement-based information sharing")

if __name__ == "__main__":
    main()
