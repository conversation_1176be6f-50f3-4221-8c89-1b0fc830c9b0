#!/usr/bin/env python
"""
This module defines the OpenAI function schema for extracting vehicle inspection data from transcripts.
"""

import os
import json
import logging
from typing import Dict, Any, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_voice_script() -> Dict[str, List[str]]:
    """
    Load the voice script from the JSON file.
    
    Returns:
        dict: The voice script structure with sections and questions
    """
    try:
        script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'voice-script.json')
        
        # If not found in current directory, try parent directory
        if not os.path.exists(script_path):
            script_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'voice-script.json')
        
        with open(script_path, 'r') as f:
            voice_script = json.load(f)
        
        logger.info(f"Loaded voice script with {len(voice_script)} sections")
        return voice_script
    except Exception as e:
        logger.error(f"Error loading voice script: {e}")
        # Fallback to a basic structure if file can't be loaded
        return {
            "vehicle_info": [
                "What is the condition of the vehicle make and model?",
                "What is the odometer reading?",
                "What is the date of inspection?"
            ],
            "exterior": [
                "What is the condition of the front bumper?",
                "What is the condition of the headlights and signal lights?",
                "What is the condition of the windshield?",
                "What is the condition of the tires?",
                "What is the condition of the body panels and paint?"
            ],
            "under_the_hood": [
                "What is the condition of the engine oil?",
                "What is the condition of the coolant level?",
                "What is the condition of the belts and hoses?",
                "What is the condition of the battery?",
                "Are there any visible leaks?"
            ],
            "brakes_and_suspension": [
                "What is the condition of the brake pads?",
                "What is the condition of the brake rotors?",
                "What is the condition of the suspension system?"
            ],
            "interior": [
                "What is the condition of the dashboard lights?",
                "What is the condition of the horn?",
                "What is the condition of the seatbelts?",
                "What is the condition of the windows and locks?"
            ],
            "road_test": [
                "What is the condition during acceleration?",
                "What is the condition during braking?",
                "Were any unusual sounds or issues noticed?"
            ],
            "final": [
                "Do you recommend any repairs?",
                "Any additional notes?"
            ]
        }

def create_field_id(question: str) -> str:
    """
    Create a field ID from a question.
    
    Args:
        question: The question to create a field ID for
    
    Returns:
        A snake_case field ID
    """
    # Remove "What is the condition of the " prefix if present
    field = question.lower()
    field = field.replace("what is the condition of the ", "")
    field = field.replace("what is the condition of ", "")
    field = field.replace("what is the ", "")
    field = field.replace("were any ", "")
    field = field.replace("are there any ", "")
    field = field.replace("do you ", "")
    
    # Replace spaces and special characters
    field = field.replace(" ", "_")
    field = field.replace("?", "")
    field = field.replace(".", "")
    field = field.replace(",", "")
    field = field.replace("'", "")
    field = field.replace("\"", "")
    field = field.replace("-", "_")
    field = field.replace("/", "_")
    
    return field

def build_inspection_properties(voice_script: Dict[str, List[str]]) -> Dict[str, Any]:
    """
    Build the function properties from the voice script.
    
    Args:
        voice_script: The voice script structure
    
    Returns:
        The properties structure for the function
    """
    properties = {}
    
    # Add vehicle info section
    properties["vehicle_info"] = {
        "type": "object",
        "description": "Basic vehicle information",
        "properties": {
            "make_model": {
                "type": "string",
                "description": "Make and model of the vehicle"
            },
            "year": {
                "type": "string",
                "description": "Year of the vehicle"
            },
            "vin": {
                "type": "string",
                "description": "Vehicle Identification Number if mentioned"
            },
            "odometer_reading": {
                "type": "string",
                "description": "Current odometer reading"
            },
            "inspection_date": {
                "type": "string",
                "description": "Date when the inspection was performed"
            },
            "owner_name": {
                "type": "string",
                "description": "Name of the vehicle owner if mentioned"
            },
            "license_plate": {
                "type": "string",
                "description": "License plate number if mentioned"
            }
        },
        "required": ["make_model", "odometer_reading", "inspection_date"]
    }
    
    # Build section properties from voice script
    for section, questions in voice_script.items():
        if section == "vehicle_info":
            continue  # Skip vehicle_info as it has a custom structure
            
        section_properties = {}
        for question in questions:
            field_id = create_field_id(question)
            section_properties[field_id] = {
                "type": "object",
                "description": f"Response to: {question}",
                "properties": {
                    "condition": {
                        "type": "string",
                        "description": "Condition assessment (Good, Fair, Poor, or detailed description)",
                        "enum": ["Good", "Fair", "Poor", "N/A"]
                    },
                    "details": {
                        "type": "string",
                        "description": "Detailed notes about the condition"
                    },
                    "recommendations": {
                        "type": "string",
                        "description": "Recommendations for repair or maintenance if applicable"
                    }
                },
                "required": ["condition"]
            }
        
        properties[section] = {
            "type": "object",
            "description": f"Inspection results for {section.replace('_', ' ')}",
            "properties": section_properties
        }
    
    # Add a summary section for overall findings and recommendations
    properties["summary"] = {
        "type": "object",
        "description": "Summary of inspection findings and recommendations",
        "properties": {
            "overall_condition": {
                "type": "string",
                "description": "Overall condition of the vehicle (Good, Fair, Poor)",
                "enum": ["Good", "Fair", "Poor"]
            },
            "recommendations": {
                "type": "array",
                "description": "List of recommended repairs or maintenance",
                "items": {
                    "type": "string"
                }
            },
            "notes": {
                "type": "string",
                "description": "Additional notes about the vehicle"
            }
        },
        "required": ["overall_condition"]
    }
    
    return properties

def get_inspection_function_calling() -> Dict[str, Any]:
    """
    Get the function calling definition for extracting vehicle inspection data.
    
    Returns:
        The function calling definition for OpenAI API
    """
    voice_script = load_voice_script()
    properties = build_inspection_properties(voice_script)
    
    function_calling = {
        "name": "extract_vehicle_inspection",
        "description": "Extract structured information from a vehicle inspection transcript",
        "parameters": {
            "type": "object",
            "properties": properties,
            "required": ["vehicle_info", "summary"]
        }
    }
    
    return function_calling

if __name__ == "__main__":
    # For testing - print the schema
    schema = get_inspection_function_calling()
    print(json.dumps(schema, indent=2))

# Function calling message to be used with OpenAI API
FUNCTION_CALLING_MESSAGES = [
    {
        "role": "system",
        "content": """You are an expert at analyzing vehicle inspection conversations and extracting structured data.
        Your task is to carefully read the transcript and extract all details about a vehicle inspection.
        Be as complete and accurate as possible, even if some information is ambiguous or unclear."""
    },
    {
        "role": "user",
        "content": "Extract vehicle inspection details from the following transcript: "
    }
]