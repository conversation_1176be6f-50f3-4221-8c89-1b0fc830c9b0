#!/usr/bin/env python
"""
End-to-end script for generating vehicle inspection reports from call transcripts.
This script handles the entire process:
1. Fetch transcript from MongoDB
2. Extract structured data using OpenAI function calling
3. Render HTML from the structured data
4. Generate PDF from the HTML
5. Store the PDF in MongoDB GridFS (optional)
"""
import os
import sys
import json
import logging
import argparse
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, Tuple

# Add parent directory to path to allow imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import components
from scripts.transcript_processor import TranscriptProcessor
from scripts.template_renderer import TemplateRenderer
from scripts.pdf_generator import PDFGenerator
from scripts.report_ui import store_pdf_in_mongodb

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("generate_inspection_report")

async def generate_inspection_report(call_id: str, save_to_mongodb: bool = True) -> Optional[str]:
    """
    Generate an inspection report from a transcript.
    
    Args:
        call_id: The ID of the call to process
        save_to_mongodb: Whether to save the PDF to MongoDB GridFS
        
    Returns:
        Path to the generated PDF or None if generation failed
    """
    try:
        # Step 1: Process the transcript with OpenAI function calling
        logger.info(f"Processing transcript for call {call_id}")
        processor = TranscriptProcessor()
        
        try:
            inspection_data = processor.process_transcript(call_id)
            if not inspection_data:
                logger.error(f"Failed to process transcript {call_id}")
                return None
                
            # Save the processed data to MongoDB
            processor.save_processed_data(call_id, inspection_data)
            
        finally:
            processor.close()
            
        # Step 2: Render HTML from the inspection data
        logger.info(f"Rendering HTML for call {call_id}")
        renderer = TemplateRenderer()
        html = renderer.render_html(inspection_data)
        
        # Save the HTML temporarily
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        html_filename = f"inspection_report_{call_id}_{timestamp}.html"
        html_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'reports',
            html_filename
        )
        
        os.makedirs(os.path.dirname(html_path), exist_ok=True)
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html)
            
        logger.info(f"Saved HTML to {html_path}")
        
        # Step 3: Generate PDF from the HTML
        logger.info(f"Generating PDF for call {call_id}")
        pdf_generator = PDFGenerator()
        pdf_filename = f"inspection_report_{call_id}_{timestamp}.pdf"
        pdf_result = pdf_generator.generate_from_html_file(html_path, pdf_filename)
        
        if not pdf_result or not pdf_result[0]:
            logger.error(f"Failed to generate PDF for call {call_id}")
            return None
            
        pdf_path, pdf_size = pdf_result
        logger.info(f"Generated PDF: {pdf_path} (Size: {pdf_size / 1024 / 1024:.2f} MB)")
        
        # Check if PDF needs compression for MongoDB storage
        if pdf_size > 10 * 1024 * 1024 and save_to_mongodb:
            logger.info(f"PDF is large ({pdf_size / 1024 / 1024:.2f} MB), attempting compression")
            compressed_path = pdf_generator.compress_pdf(pdf_path)
            if compressed_path:
                compressed_size = os.path.getsize(compressed_path)
                logger.info(f"Compressed PDF: {compressed_path} (Size: {compressed_size / 1024 / 1024:.2f} MB)")
                pdf_path = compressed_path
                pdf_size = compressed_size
        
        # Step 4: Optionally save to MongoDB GridFS
        if save_to_mongodb:
            # Skip if file is too large for MongoDB (16MB limit)
            if pdf_size > 16 * 1024 * 1024:
                logger.warning(f"PDF is too large for MongoDB storage ({pdf_size / 1024 / 1024:.2f} MB > 16 MB)")
                logger.warning("Skipping MongoDB storage, PDF will only be available on the filesystem")
            else:
                logger.info(f"Storing PDF in MongoDB GridFS")
                
                # Extract identification info for better metadata
                identification = inspection_data.get("inspection_data", {}).get("identification", {})
                customer_name = identification.get("customer_name", "Unknown Customer")
                
                # Build vehicle info string
                vehicle_make = identification.get("vehicle_make", "")
                vehicle_model = identification.get("vehicle_model", "")
                vehicle_year = identification.get("vehicle_year", "")
                vehicle_info = f"{vehicle_make} {vehicle_model}"
                if vehicle_year:
                    vehicle_info = f"{vehicle_year} {vehicle_info}"
                    
                # Store in GridFS
                try:
                    file_id = store_pdf_in_mongodb(
                        pdf_path=pdf_path,
                        call_id=call_id,
                        customer_name=customer_name,
                        vehicle_info=vehicle_info
                    )
                    
                    if file_id:
                        logger.info(f"PDF stored in MongoDB GridFS with ID: {file_id}")
                    else:
                        logger.warning(f"Failed to store PDF in MongoDB GridFS")
                except Exception as e:
                    logger.error(f"Error storing PDF in MongoDB: {e}")
        
        return pdf_path
    
    except Exception as e:
        logger.error(f"Error generating inspection report: {e}")
        return None

async def generate_inspection_report_from_transcript(transcript_content: str, call_identifier: str) -> Optional[str]:
    """
    Generate an inspection report directly from transcript content.
    Useful when the transcript is not yet in MongoDB.
    
    Args:
        transcript_content: The raw transcript text
        call_identifier: A unique identifier for the transcript
        
    Returns:
        Path to the generated PDF or None if generation failed
    """
    try:
        from openai import OpenAI
        import os
        from dotenv import load_dotenv
        from scripts.function_schema import get_inspection_function_calling
        
        load_dotenv()
        
        # Initialize OpenAI client
        client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        
        # Step 1: Process the transcript with OpenAI function calling
        logger.info(f"Processing transcript content for {call_identifier}")
        
        # Get the function schema
        function_calling = get_inspection_function_calling()
        
        # Create OpenAI chat completion with function calling
        response = client.chat.completions.create(
            model="gpt-4",  # Use appropriate model
            messages=[
                {"role": "system", "content": "You are a vehicle inspection assistant that extracts structured data from transcripts of vehicle inspections. Extract all details about the vehicle inspection accurately."},
                {"role": "user", "content": f"Extract the vehicle inspection information from this transcript: {transcript_content}"}
            ],
            tools=[{"type": "function", "function": schema} for schema in function_calling],
            tool_choice={"type": "function", "function": {"name": "extract_vehicle_inspection"}}
        )
        
        # Extract function call arguments from response
        tool_call = response.choices[0].message.tool_calls[0] if response.choices[0].message.tool_calls else None
        
        if not tool_call:
            logger.error("Function call not returned by OpenAI")
            return None
            
        # Parse arguments as JSON
        arguments = json.loads(tool_call.function.arguments)
        logger.info(f"Successfully extracted inspection data with {len(arguments)} categories")
        
        # Create inspection data structure
        inspection_data = {
            "inspection_data": arguments,
            "metadata": {
                "call_id": call_identifier,
                "processed_at": datetime.now().isoformat(),
                "transcript_length": len(transcript_content),
            }
        }
        
        # Step 2: Render HTML from the inspection data
        logger.info(f"Rendering HTML for transcript {call_identifier}")
        renderer = TemplateRenderer()
        html = renderer.render_html(inspection_data)
        
        # Save the HTML temporarily
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        html_filename = f"inspection_report_{call_identifier}_{timestamp}.html"
        html_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'reports',
            html_filename
        )
        
        os.makedirs(os.path.dirname(html_path), exist_ok=True)
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html)
            
        logger.info(f"Saved HTML to {html_path}")
        
        # Step 3: Generate PDF from the HTML
        logger.info(f"Generating PDF for transcript {call_identifier}")
        pdf_generator = PDFGenerator()
        pdf_filename = f"inspection_report_{call_identifier}_{timestamp}.pdf"
        pdf_result = pdf_generator.generate_from_html_file(html_path, pdf_filename)
        
        if not pdf_result or not pdf_result[0]:
            logger.error(f"Failed to generate PDF for transcript {call_identifier}")
            return None
            
        pdf_path, pdf_size = pdf_result
        logger.info(f"Generated PDF: {pdf_path} (Size: {pdf_size / 1024 / 1024:.2f} MB)")
        
        # Compress if needed
        if pdf_size > 10 * 1024 * 1024:
            logger.info(f"PDF is large ({pdf_size / 1024 / 1024:.2f} MB), attempting compression")
            compressed_path = pdf_generator.compress_pdf(pdf_path)
            if compressed_path:
                logger.info(f"Compressed PDF: {compressed_path} (Size: {os.path.getsize(compressed_path) / 1024 / 1024:.2f} MB)")
                return compressed_path
        
        return pdf_path
    
    except Exception as e:
        logger.error(f"Error generating inspection report from transcript content: {e}")
        return None

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Generate inspection reports from call transcripts")
    parser.add_argument("--transcript-id", help="ID of the transcript to process")
    parser.add_argument("--skip-mongodb", action="store_true", help="Skip storing the PDF in MongoDB")
    args = parser.parse_args()
    
    if args.transcript_id:
        # Process a single transcript
        result = await generate_inspection_report(args.transcript_id, not args.skip_mongodb)
        if result:
            print(f"Generated report: {result}")
            return 0
        else:
            print("Failed to generate report")
            return 1
    else:
        # List available transcripts and let user choose
        from pymongo import MongoClient
        import os
        from dotenv import load_dotenv
        
        load_dotenv()
        
        MONGO_URI = os.getenv('MONGO_URI', '****************************************')
        MONGO_DB = os.getenv('MONGO_DB', 'voice_assistant')
        MONGO_COLLECTION = os.getenv('MONGO_COLLECTION', 'transcripts')
        
        try:
            client = MongoClient(MONGO_URI)
            db = client[MONGO_DB]
            
            # Find all transcript collections
            transcript_collections = [coll for coll in db.list_collection_names() 
                                     if coll.startswith(f"{MONGO_COLLECTION}_")]
            
            if not transcript_collections:
                print("No transcript collections found in MongoDB")
                return 1
                
            # Gather transcripts from all collections
            transcripts = []
            for coll_name in transcript_collections:
                collection = db[coll_name]
                for doc in collection.find().sort("timestamp", -1).limit(10):
                    # Extract key information
                    call_id = doc.get("call_id", "Unknown")
                    timestamp = doc.get("formatted_time", "Unknown")
                    timestamp_obj = doc.get("date_time")
                    
                    # Format timestamp if it's a datetime object
                    if timestamp_obj:
                        try:
                            if isinstance(timestamp_obj, datetime):
                                timestamp = timestamp_obj.strftime("%Y-%m-%d %H:%M:%S")
                        except:
                            pass
                            
                    transcripts.append({
                        "call_id": call_id,
                        "timestamp": timestamp,
                        "collection": coll_name,
                        "identifier": doc.get("call_identifier", call_id)
                    })
            
            client.close()
            
            if not transcripts:
                print("No transcripts found in MongoDB")
                return 1
                
            # Display transcripts for selection
            print("Available transcripts:")
            for i, t in enumerate(transcripts):
                print(f"{i+1}. {t['identifier']} ({t['timestamp']})")
                
            # Get user selection
            try:
                selection = int(input("\nEnter number to process (0 to exit): "))
                if selection < 1 or selection > len(transcripts):
                    print("Invalid selection")
                    return 1
                    
                # Process selected transcript
                selected = transcripts[selection-1]
                print(f"Processing transcript {selected['identifier']}...")
                
                result = await generate_inspection_report(
                    selected['identifier'], 
                    not args.skip_mongodb
                )
                
                if result:
                    print(f"Generated report: {result}")
                    return 0
                else:
                    print("Failed to generate report")
                    return 1
                    
            except (ValueError, IndexError):
                print("Invalid selection")
                return 1
                
        except Exception as e:
            print(f"Error: {e}")
            return 1

if __name__ == "__main__":
    asyncio.run(main()) 