#!/usr/bin/env python
"""
This module provides a web UI for viewing and downloading vehicle inspection reports.
"""
import os
import base64
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from pathlib import Path

import gridfs
from bson import ObjectId
from fastapi import APIRouter, Request, HTTPException, UploadFile, File, Form, Depends
from fastapi.responses import HTMLResponse, FileResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from pymongo import MongoClient, DESCENDING
from pydantic import BaseModel

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
REPORTS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'reports')
TEMPLATES_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'templates')
STATIC_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'static')

# Create directories if they don't exist
os.makedirs(REPORTS_DIR, exist_ok=True)
os.makedirs(STATIC_DIR, exist_ok=True)
os.makedirs(os.path.join(STATIC_DIR, 'css'), exist_ok=True)
os.makedirs(os.path.join(STATIC_DIR, 'js'), exist_ok=True)

# Create router
router = APIRouter(prefix="/reports", tags=["reports"])

# Templates
templates = Jinja2Templates(directory=TEMPLATES_DIR)

# Models
class ReportModel(BaseModel):
    id: str
    call_id: str
    customer_name: str
    vehicle_info: str
    date_created: str
    file_name: str
    file_size: str


# MongoDB connection
def get_mongodb():
    """Get MongoDB client and database connection."""
    from dotenv import load_dotenv
    import os
    
    load_dotenv()
    
    MONGO_URI = os.getenv('MONGO_URI', '****************************************')
    MONGO_DB = os.getenv('MONGO_DB', 'voice_assistant')
    
    client = MongoClient(MONGO_URI)
    db = client[MONGO_DB]
    fs = gridfs.GridFS(db)
    
    return client, db, fs


# Routes
@router.get("/", response_class=HTMLResponse)
async def reports_dashboard(request: Request):
    """Main reports dashboard page."""
    return templates.TemplateResponse(
        "reports_dashboard.html", 
        {"request": request, "title": "AIREADY Inspection Reports"}
    )


@router.get("/list", response_class=JSONResponse)
async def list_reports():
    """List all reports as JSON for the UI to render."""
    client, db, fs = get_mongodb()
    
    try:
        # Query for reports in GridFS
        reports_data = []
        # Find all PDF files in GridFS
        for grid_out in fs.find({"contentType": "application/pdf"}).sort("uploadDate", DESCENDING):
            # Extract metadata from the GridFS file
            metadata = grid_out.metadata or {}
            
            # Get report details
            report = {
                "id": str(grid_out._id),
                "call_id": metadata.get("call_id", "Unknown"),
                "customer_name": metadata.get("customer_name", "Unknown"),
                "vehicle_info": metadata.get("vehicle_info", "Unknown Vehicle"),
                "date_created": grid_out.upload_date.strftime("%Y-%m-%d %H:%M:%S"),
                "file_name": grid_out.filename,
                "file_size": f"{grid_out.length / 1024:.1f} KB"
            }
            reports_data.append(report)
        
        # Also check the reports directory for any PDFs not yet in GridFS
        for file_path in Path(REPORTS_DIR).glob("**/*.pdf"):
            file_name = file_path.name
            stats = file_path.stat()
            file_size = stats.st_size
            mod_time = datetime.fromtimestamp(stats.st_mtime)
            
            # Check if this file is already in GridFS (by filename)
            if not fs.exists(filename=file_name):
                # Parse details from filename (inspection_report_CALL_ID_TIMESTAMP.pdf)
                parts = file_name.replace('.pdf', '').split('_')
                call_id = parts[2] if len(parts) > 2 else "Unknown"
                
                reports_data.append({
                    "id": f"local:{file_path}",
                    "call_id": call_id,
                    "customer_name": "Unknown (Local File)",
                    "vehicle_info": "Unknown Vehicle",
                    "date_created": mod_time.strftime("%Y-%m-%d %H:%M:%S"),
                    "file_name": file_name,
                    "file_size": f"{file_size / 1024:.1f} KB"
                })
        
        return reports_data
    
    except Exception as e:
        logger.error(f"Error listing reports: {e}")
        return {"error": str(e)}
    finally:
        client.close()


@router.get("/download/{report_id}")
async def download_report(report_id: str):
    """Download a report by its ID."""
    # Check if it's a local file
    if report_id.startswith("local:"):
        local_path = report_id[6:]  # Remove "local:" prefix
        if os.path.exists(local_path):
            return FileResponse(
                path=local_path,
                filename=os.path.basename(local_path),
                media_type="application/pdf"
            )
        else:
            raise HTTPException(status_code=404, detail="Report file not found")
    
    # Otherwise, it's in GridFS
    client, db, fs = get_mongodb()
    
    try:
        # Find file in GridFS
        try:
            file_id = ObjectId(report_id)
        except:
            raise HTTPException(status_code=400, detail="Invalid report ID")
        
        if not fs.exists(file_id):
            raise HTTPException(status_code=404, detail="Report not found in database")
        
        # Get file from GridFS
        grid_out = fs.get(file_id)
        
        # Create a temporary file to serve
        temp_file = os.path.join(REPORTS_DIR, f"temp_{report_id}.pdf")
        with open(temp_file, "wb") as f:
            f.write(grid_out.read())
        
        return FileResponse(
            path=temp_file,
            filename=grid_out.filename,
            media_type="application/pdf",
            background=lambda: os.remove(temp_file) # Clean up temp file after sending
        )
    
    except Exception as e:
        logger.error(f"Error downloading report: {e}")
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"Error downloading report: {str(e)}")
    finally:
        client.close()


@router.get("/view/{report_id}", response_class=HTMLResponse)
async def view_report(request: Request, report_id: str):
    """View a report in the browser."""
    return templates.TemplateResponse(
        "report_viewer.html", 
        {"request": request, "report_id": report_id, "title": "Report Viewer"}
    )


@router.post("/upload")
async def upload_report(
    file: UploadFile = File(...),
    call_id: str = Form(...),
    customer_name: str = Form(...),
    vehicle_info: str = Form(...)
):
    """Upload a report to GridFS."""
    if not file.filename.endswith('.pdf'):
        raise HTTPException(status_code=400, detail="Only PDF files are allowed")
    
    # Read file content
    content = await file.read()
    
    # Check file size (16MB limit for MongoDB)
    if len(content) > 16 * 1024 * 1024:
        raise HTTPException(status_code=400, detail="File is too large (max 16MB)")
    
    client, db, fs = get_mongodb()
    
    try:
        # Store file in GridFS with metadata
        file_id = fs.put(
            content,
            filename=file.filename,
            contentType="application/pdf",
            metadata={
                "call_id": call_id,
                "customer_name": customer_name,
                "vehicle_info": vehicle_info,
                "upload_date": datetime.now()
            }
        )
        
        return {"message": "Report uploaded successfully", "id": str(file_id)}
    
    except Exception as e:
        logger.error(f"Error uploading report: {e}")
        raise HTTPException(status_code=500, detail=f"Error uploading report: {str(e)}")
    finally:
        client.close()


def store_pdf_in_mongodb(pdf_path, call_id, customer_name, vehicle_info):
    """Store a PDF file in MongoDB GridFS."""
    # Check if file exists
    if not os.path.exists(pdf_path):
        logger.error(f"PDF file not found: {pdf_path}")
        return None
    
    # Check file size
    file_size = os.path.getsize(pdf_path)
    if file_size > 16 * 1024 * 1024:
        logger.error(f"PDF file too large: {file_size} bytes")
        return None
    
    client, db, fs = get_mongodb()
    
    try:
        # Read file content
        with open(pdf_path, "rb") as f:
            content = f.read()
        
        # Store in GridFS
        file_id = fs.put(
            content,
            filename=os.path.basename(pdf_path),
            contentType="application/pdf",
            metadata={
                "call_id": call_id,
                "customer_name": customer_name,
                "vehicle_info": vehicle_info,
                "upload_date": datetime.now()
            }
        )
        
        logger.info(f"PDF stored in MongoDB GridFS with ID: {file_id}")
        return str(file_id)
    
    except Exception as e:
        logger.error(f"Error storing PDF in MongoDB: {e}")
        return None
    finally:
        client.close()


def create_mongo_indexes():
    """Create indexes in MongoDB for better query performance."""
    client, db, fs = get_mongodb()
    
    try:
        # Get the fs.files collection (GridFS files collection)
        fs_files = db["fs.files"]
        
        # Create index on filename for faster lookups
        fs_files.create_index("filename")
        
        # Create indexes on metadata fields for faster filtering
        fs_files.create_index("metadata.call_id")
        fs_files.create_index("metadata.customer_name")
        fs_files.create_index("metadata.vehicle_info")
        fs_files.create_index("metadata.upload_date")
        fs_files.create_index("uploadDate")  # For sorting by upload date
        
        # Get all monthly transcript collections
        prefix = f"{os.getenv('MONGO_COLLECTION', 'transcripts')}_"
        transcript_collections = [coll for coll in db.list_collection_names() if coll.startswith(prefix)]
        
        # Create indexes on each transcript collection
        for collection_name in transcript_collections:
            collection = db[collection_name]
            
            # Create index on call_identifier for faster lookups
            collection.create_index("call_identifier", unique=True)
            
            # Create index on report-related fields
            collection.create_index("report_gridfs_id")
            collection.create_index("customer_name")
            collection.create_index("vehicle_info")
        
        logger.info("MongoDB indexes created successfully")
        return True
    
    except Exception as e:
        logger.error(f"Error creating MongoDB indexes: {e}")
        return False
    finally:
        client.close()