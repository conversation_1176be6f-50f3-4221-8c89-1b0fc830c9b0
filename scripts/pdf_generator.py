#!/usr/bin/env python
"""
Generate PDF files from HTML content using pdfkit/wkhtmltopdf.
Includes optimization for MongoDB storage (<16MB).
"""
import os
import logging
import subprocess
from typing import Optional, Tuple
import pdfkit
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("pdf_generator")

class PDFGenerator:
    """Generate PDF files from HTML content with size optimization."""
    
    def __init__(self, output_dir: str = None):
        """
        Initialize the PDF generator.
        
        Args:
            output_dir: Directory to save PDF files (defaults to reports/ in project root)
        """
        if output_dir is None:
            # Default to the reports directory in the project root
            output_dir = os.path.join(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                'reports'
            )
            
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # Configure pdfkit
        self.config = self._get_pdfkit_config()
        
    def _get_pdfkit_config(self) -> Optional[dict]:
        """
        Get pdfkit configuration based on the platform.
        
        Returns:
            pdfkit configuration object or None
        """
        try:
            # Check if wkhtmltopdf is in PATH
            subprocess.run(['wkhtmltopdf', '-V'], capture_output=True, check=True)
            return None  # Use default configuration
        except (subprocess.SubprocessError, FileNotFoundError):
            # Try to locate wkhtmltopdf in common installation paths
            paths = [
                '/usr/bin/wkhtmltopdf',
                '/usr/local/bin/wkhtmltopdf',
                'C:\\Program Files\\wkhtmltopdf\\bin\\wkhtmltopdf.exe',
                'C:\\Program Files (x86)\\wkhtmltopdf\\bin\\wkhtmltopdf.exe'
            ]
            
            for path in paths:
                if os.path.exists(path):
                    logger.info(f"Found wkhtmltopdf at {path}")
                    return {'wkhtmltopdf': path}
            
            logger.warning("wkhtmltopdf not found. PDF generation may fail.")
            return None
    
    def get_default_pdf_options(self, optimize_size: bool = True) -> dict:
        """
        Get default PDF generation options.
        
        Args:
            optimize_size: Whether to optimize PDF size for MongoDB storage
            
        Returns:
            Dictionary of wkhtmltopdf options
        """
        options = {
            'page-size': 'Letter',
            'margin-top': '0.75in',
            'margin-right': '0.75in',
            'margin-bottom': '0.75in',
            'margin-left': '0.75in',
            'encoding': 'UTF-8',
            'no-outline': None,
            'enable-local-file-access': None,
            'disable-external-links': None,
            'disable-javascript': None
        }
        
        # Add size optimization options
        if optimize_size:
            options.update({
                'dpi': '96',  # Lower DPI for smaller file size
                'image-dpi': '96',  # Lower image DPI
                'image-quality': '85',  # Slightly reduce image quality
                'compress': None,  # Enable compression
                'no-background': None,  # Remove backgrounds to reduce size
            })
            
        return options
    
    def generate_from_html_string(self, html_content: str, output_filename: str, 
                                optimize_size: bool = True) -> Tuple[Optional[str], int]:
        """
        Generate a PDF from HTML string content.
        
        Args:
            html_content: HTML content to convert
            output_filename: Filename for the PDF (without directory)
            optimize_size: Whether to optimize PDF size for MongoDB storage
            
        Returns:
            Tuple with path to the generated PDF and file size in bytes, or (None, 0) if generation failed
        """
        try:
            output_path = os.path.join(self.output_dir, output_filename)
            
            # Generate PDF with appropriate options
            options = self.get_default_pdf_options(optimize_size)
            
            pdfkit.from_string(
                html_content,
                output_path,
                configuration=self.config,
                options=options
            )
            
            # Check file size
            file_size = os.path.getsize(output_path)
            
            logger.info(f"Generated PDF: {output_path} (Size: {file_size / 1024 / 1024:.2f} MB)")
            
            # If file is still too large and we're optimizing, try more aggressive optimization
            if file_size > 15 * 1024 * 1024 and optimize_size:  # If larger than 15MB
                logger.warning(f"PDF is too large ({file_size / 1024 / 1024:.2f} MB). Applying more aggressive optimization.")
                
                # More aggressive options
                options.update({
                    'dpi': '72',  # Lower DPI further
                    'image-dpi': '72',
                    'image-quality': '75',
                    'grayscale': None,  # Convert to grayscale if necessary
                })
                
                # Regenerate with more aggressive options
                pdfkit.from_string(
                    html_content,
                    output_path,
                    configuration=self.config,
                    options=options
                )
                
                file_size = os.path.getsize(output_path)
                logger.info(f"Regenerated PDF with aggressive optimization: {output_path} (Size: {file_size / 1024 / 1024:.2f} MB)")
                
                # If still too large, warn but still return the file
                if file_size > 16 * 1024 * 1024:
                    logger.warning(f"PDF is still too large for MongoDB storage ({file_size / 1024 / 1024:.2f} MB > 16 MB)")
            
            return output_path, file_size
            
        except Exception as e:
            logger.error(f"Error generating PDF: {e}")
            return None, 0
            
    def generate_from_html_file(self, html_file_path: str, output_filename: str = None,
                              optimize_size: bool = True) -> Tuple[Optional[str], int]:
        """
        Generate a PDF from an HTML file.
        
        Args:
            html_file_path: Path to the HTML file
            output_filename: Filename for the PDF (without directory)
                            If None, derives from the HTML filename
            optimize_size: Whether to optimize PDF size for MongoDB storage
            
        Returns:
            Tuple with path to the generated PDF and file size in bytes, or (None, 0) if generation failed
        """
        try:
            # If output filename not provided, derive from HTML filename
            if output_filename is None:
                html_path = Path(html_file_path)
                output_filename = f"{html_path.stem}.pdf"
                
            output_path = os.path.join(self.output_dir, output_filename)
            
            # Generate PDF with appropriate options
            options = self.get_default_pdf_options(optimize_size)
            
            pdfkit.from_file(
                html_file_path,
                output_path,
                configuration=self.config,
                options=options
            )
            
            # Check file size
            file_size = os.path.getsize(output_path)
            
            logger.info(f"Generated PDF: {output_path} (Size: {file_size / 1024 / 1024:.2f} MB)")
            
            # If file is still too large and we're optimizing, try more aggressive optimization
            if file_size > 15 * 1024 * 1024 and optimize_size:  # If larger than 15MB
                logger.warning(f"PDF is too large ({file_size / 1024 / 1024:.2f} MB). Applying more aggressive optimization.")
                
                # More aggressive options
                options.update({
                    'dpi': '72',  # Lower DPI further
                    'image-dpi': '72',
                    'image-quality': '75',
                    'grayscale': None,  # Convert to grayscale if necessary
                })
                
                # Regenerate with more aggressive options
                pdfkit.from_file(
                    html_file_path,
                    output_path,
                    configuration=self.config,
                    options=options
                )
                
                file_size = os.path.getsize(output_path)
                logger.info(f"Regenerated PDF with aggressive optimization: {output_path} (Size: {file_size / 1024 / 1024:.2f} MB)")
                
                # If still too large, warn but still return the file
                if file_size > 16 * 1024 * 1024:
                    logger.warning(f"PDF is still too large for MongoDB storage ({file_size / 1024 / 1024:.2f} MB > 16 MB)")
            
            return output_path, file_size
            
        except Exception as e:
            logger.error(f"Error generating PDF: {e}")
            return None, 0
            
    def compress_pdf(self, pdf_path: str, quality: str = 'ebook') -> Optional[str]:
        """
        Compress an existing PDF file using ghostscript.
        
        Args:
            pdf_path: Path to the PDF to compress
            quality: Compression preset ('screen', 'ebook', 'printer', 'prepress')
                   - screen: lowest quality, smallest size (72 dpi)
                   - ebook: medium quality, medium size (150 dpi)
                   - printer: better quality, larger size (300 dpi)
                   - prepress: best quality, largest size (300 dpi, color preserving)
                   
        Returns:
            Path to the compressed PDF or None if compression failed
        """
        try:
            # Check if ghostscript is available
            gs_command = 'gs'
            try:
                subprocess.run([gs_command, '--version'], capture_output=True, check=True)
            except (subprocess.SubprocessError, FileNotFoundError):
                logger.error("Ghostscript not found. PDF compression will be skipped.")
                return None
                
            # Define output path
            pdf_dir = os.path.dirname(pdf_path)
            pdf_name = os.path.basename(pdf_path)
            output_path = os.path.join(pdf_dir, f"compressed_{pdf_name}")
            
            # Define compression command
            command = [
                gs_command,
                '-sDEVICE=pdfwrite',
                f'-dPDFSETTINGS=/{quality}',
                '-dCompatibilityLevel=1.4',
                '-dNOPAUSE',
                '-dQUIET',
                '-dBATCH',
                f'-sOutputFile={output_path}',
                pdf_path
            ]
            
            # Execute compression
            result = subprocess.run(command, capture_output=True, check=True)
            
            # Check if compression was successful and file exists
            if os.path.exists(output_path):
                original_size = os.path.getsize(pdf_path)
                compressed_size = os.path.getsize(output_path)
                
                reduction = (1 - compressed_size / original_size) * 100
                logger.info(f"Compressed PDF: {output_path}")
                logger.info(f"Size reduction: {original_size / 1024 / 1024:.2f} MB → " 
                           f"{compressed_size / 1024 / 1024:.2f} MB ({reduction:.1f}%)")
                
                return output_path
            else:
                logger.error("Compression failed: Output file was not created")
                return None
                
        except Exception as e:
            logger.error(f"Error compressing PDF: {e}")
            return None

if __name__ == "__main__":
    # Example usage
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python pdf_generator.py <html_file>")
        sys.exit(1)
        
    html_file = sys.argv[1]
    
    generator = PDFGenerator()
    pdf_path, file_size = generator.generate_from_html_file(html_file)
    
    if pdf_path:
        print(f"PDF generated: {pdf_path} (Size: {file_size / 1024 / 1024:.2f} MB)")
        
        # Compress if larger than 10MB
        if file_size > 10 * 1024 * 1024:
            print("PDF is large, attempting compression...")
            compressed_path = generator.compress_pdf(pdf_path)
            if compressed_path:
                print(f"Compressed PDF: {compressed_path} (Size: {os.path.getsize(compressed_path) / 1024 / 1024:.2f} MB)")
    else:
        print("Failed to generate PDF") 