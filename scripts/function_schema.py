#!/usr/bin/env python
"""
OpenAI function calling schema for extracting vehicle inspection data from transcripts.
"""
import json
import os
from typing import Dict, List, Any, Optional

# Load questions from voice-script.json
SCRIPT_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'voice-script.json')

def load_voice_script() -> Dict[str, List[str]]:
    """Load the voice script questions from the JSON file."""
    try:
        with open(SCRIPT_PATH, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading voice script: {e}")
        return {}

# Build function schema based on voice script categories
def build_inspection_function_schema() -> Dict[str, Any]:
    """
    Builds the OpenAI function schema for extracting vehicle inspection data.
    
    Returns:
        Dict containing the function schema definition.
    """
    voice_script = load_voice_script()
    
    # Build properties for each category
    properties = {}
    required = []
    
    for category, questions in voice_script.items():
        category_properties = {}
        
        for question in questions:
            # Create a field ID from the question (simplified version)
            field_id = create_field_id(question)
            
            # Add the field to properties
            category_properties[field_id] = {
                "type": "string",
                "description": question
            }
            
        # Add the category as an object with its own properties
        properties[category] = {
            "type": "object",
            "description": f"Information about vehicle {category.replace('_', ' ')}",
            "properties": category_properties,
            "required": list(category_properties.keys())
        }
        
        # Add this category to the required list
        required.append(category)
    
    # Add vehicle and customer identification fields
    properties["identification"] = {
        "type": "object",
        "description": "Basic identification information about the vehicle and customer",
        "properties": {
            "customer_name": {
                "type": "string", 
                "description": "Name of the customer or vehicle owner"
            },
            "technician_name": {
                "type": "string",
                "description": "Name of the technician performing the inspection"
            },
            "vehicle_make": {
                "type": "string",
                "description": "Make of the vehicle"
            },
            "vehicle_model": {
                "type": "string",
                "description": "Model of the vehicle"
            },
            "vehicle_year": {
                "type": "string",
                "description": "Year of the vehicle"
            },
            "license_plate": {
                "type": "string",
                "description": "License plate number"
            }
        },
        "required": ["customer_name", "technician_name", "vehicle_make", "vehicle_model"]
    }
    
    required.append("identification")
    
    # Construct the complete function definition
    function_definition = {
        "name": "extract_vehicle_inspection",
        "description": "Extract structured data from a vehicle inspection transcript",
        "parameters": {
            "type": "object",
            "properties": properties,
            "required": required
        }
    }
    
    return function_definition

def create_field_id(question: str) -> str:
    """
    Create a simplified field ID from a question.
    
    Example: "What is the condition of the brake pads?" -> "brake_pads_condition"
    """
    # Remove question prefix
    simplified = question.lower().replace("what is the condition of the ", "")
    simplified = simplified.replace("what is the condition during ", "")
    simplified = simplified.replace("were any ", "")
    simplified = simplified.replace("are there any ", "")
    simplified = simplified.replace("do you ", "")
    simplified = simplified.replace("any ", "")
    
    # Remove punctuation
    simplified = simplified.replace("?", "").replace(".", "").replace(",", "")
    
    # Handle special cases
    if "recommend" in simplified:
        return "recommended_repairs"
    if "additional notes" in simplified:
        return "additional_notes"
    
    # Create field ID
    parts = simplified.split()
    if len(parts) > 2:
        # For longer phrases, use main components with condition suffix
        if simplified.endswith("condition"):
            return f"{'_'.join(parts[:-1])}_condition"
        else:
            return f"{'_'.join(parts)}"
    else:
        # For shorter phrases, just join with underscores
        return f"{'_'.join(parts)}"

def get_inspection_function_calling() -> List[Dict[str, Any]]:
    """
    Returns the complete function calling definition for the OpenAI API.
    
    Returns:
        List containing the function definition.
    """
    return [build_inspection_function_schema()]

if __name__ == "__main__":
    # Print the schema when run directly
    schema = build_inspection_function_schema()
    print(json.dumps(schema, indent=2))