#!/usr/bin/env python
"""
Script to verify MongoDB transcript storage and list stored transcripts.
"""
import os
import json
from pymongo import MongoClient
from datetime import datetime, timedelta
from dotenv import load_dotenv
from pprint import pprint

load_dotenv()

# MongoDB Configuration
MONGO_URI = os.getenv('MONGO_URI', '****************************************')
MONGO_DB = os.getenv('MONGO_DB', 'voice_assistant')
MONGO_COLLECTION_BASE = os.getenv('MONGO_COLLECTION', 'transcripts')

def list_collections():
    """List all transcript collections in the database."""
    try:
        client = MongoClient(MONGO_URI)
        db = client[MONGO_DB]
        
        # Get collections with transcripts_ prefix
        collections = [coll for coll in db.list_collection_names() 
                    if coll.startswith(f"{MONGO_COLLECTION_BASE}_")]
        
        print(f"\n=== Transcript Collections ===")
        if not collections:
            print("No transcript collections found.")
        else:
            for coll in sorted(collections):
                count = db[coll].count_documents({})
                print(f"- {coll}: {count} transcript(s)")
        
        client.close()
        return collections
    except Exception as e:
        print(f"Error listing collections: {e}")
        return []

def list_recent_transcripts(days=7):
    """List transcripts from the last N days."""
    try:
        client = MongoClient(MONGO_URI)
        db = client[MONGO_DB]
        
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # Get all collections with transcripts_ prefix
        collections = [coll for coll in db.list_collection_names() 
                    if coll.startswith(f"{MONGO_COLLECTION_BASE}_")]
        
        print(f"\n=== Recent Transcripts (Last {days} Days) ===")
        
        found = False
        for coll in collections:
            # Query for recent transcripts in each collection
            transcripts = db[coll].find({
                "date_time": {"$gte": start_date, "$lte": end_date}
            }).sort("date_time", -1)
            
            for transcript in transcripts:
                found = True
                print(f"\nCall ID: {transcript.get('call_id')}")
                print(f"Time: {transcript.get('date_time')}")
                print(f"Duration: {transcript.get('metadata', {}).get('duration', 0):.2f} seconds")
                print(f"Segments: {transcript.get('metadata', {}).get('transcript_segments', 0)}")
                print(f"Identifier: {transcript.get('call_identifier')}")
                
                # Print a snippet of the transcript
                if transcript.get('transcript'):
                    snippet = transcript['transcript'][:200] + "..." if len(transcript['transcript']) > 200 else transcript['transcript']
                    print(f"\nTranscript snippet:\n{snippet}")
                
                print("-" * 50)
        
        if not found:
            print(f"No transcripts found from the last {days} days.")
        
        client.close()
    except Exception as e:
        print(f"Error listing recent transcripts: {e}")

def show_transcript_stats():
    """Show statistics about stored transcripts."""
    try:
        client = MongoClient(MONGO_URI)
        db = client[MONGO_DB]
        
        # Get all collections with transcripts_ prefix
        collections = [coll for coll in db.list_collection_names() 
                    if coll.startswith(f"{MONGO_COLLECTION_BASE}_")]
        
        total_transcripts = 0
        total_duration = 0
        
        for coll in collections:
            collection = db[coll]
            count = collection.count_documents({})
            total_transcripts += count
            
            # Calculate total duration
            duration_pipeline = [
                {"$group": {"_id": None, "total": {"$sum": "$metadata.duration"}}}
            ]
            duration_result = list(collection.aggregate(duration_pipeline))
            if duration_result and duration_result[0].get('total'):
                total_duration += duration_result[0]['total']
        
        print(f"\n=== Transcript Statistics ===")
        print(f"Total transcripts: {total_transcripts}")
        print(f"Total call duration: {total_duration/60:.2f} minutes")
        print(f"Collections: {len(collections)}")
        
        client.close()
    except Exception as e:
        print(f"Error showing transcript stats: {e}")

if __name__ == "__main__":
    print("\n===== MongoDB Transcript Verification =====")
    print(f"MongoDB URI: {MONGO_URI}")
    print(f"Database: {MONGO_DB}")
    print(f"Collection base: {MONGO_COLLECTION_BASE}")
    
    try:
        # Test basic connection
        client = MongoClient(MONGO_URI)
        server_info = client.server_info()
        print(f"\n✅ Connected to MongoDB version: {server_info.get('version')}")
        client.close()
        
        # List collections and stats
        collections = list_collections()
        show_transcript_stats()
        
        # Show recent transcripts
        if collections:
            list_recent_transcripts()
        
    except Exception as e:
        print(f"\n❌ Failed to connect to MongoDB: {e}")
        print("Make sure the MongoDB container is running and the connection details are correct.")
