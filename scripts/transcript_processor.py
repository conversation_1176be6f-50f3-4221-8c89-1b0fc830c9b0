#!/usr/bin/env python
"""
Simplified TranscriptProcessor for generating inspection reports.
This module provides basic transcript processing functionality.
"""
import os
import json
import logging
from typing import Dict, Any, Optional
from pymongo import MongoClient
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("transcript_processor")

# Load environment variables
load_dotenv()

class TranscriptProcessor:
    """Simple transcript processor for generating inspection reports."""
    
    def __init__(self):
        """Initialize the transcript processor."""
        self.mongo_uri = os.getenv('MONGO_URI', '****************************************')
        self.mongo_client = None
        self.db = None
        
    def connect(self):
        """Connect to MongoDB."""
        try:
            self.mongo_client = MongoClient(self.mongo_uri)
            self.db = self.mongo_client.get_default_database()
            logger.info("Connected to MongoDB")
        except Exception as e:
            logger.error(f"Error connecting to MongoDB: {e}")
            raise
    
    def process_transcript(self, call_id: str) -> Optional[Dict[str, Any]]:
        """
        Process a transcript and extract inspection data.
        This method attempts to fetch already-extracted data from the reports database first.
        
        Args:
            call_id: The ID of the call to process
            
        Returns:
            Extracted inspection data or None if processing failed
        """
        try:
            print(f"🔍 TranscriptProcessor: Looking for extracted data for call_id: {call_id}")
            
            # Try to fetch already-extracted data from reports database
            from pymongo import MongoClient
            import os
            
            reports_uri = os.getenv("REPORTS_MONGO_URI", "*******************************************************************************")
            reports_db_name = os.getenv("REPORTS_DB_NAME", "inspection_reports")
            reports_collection_name = os.getenv("REPORTS_COLLECTION_NAME", "inspections")
            
            try:
                reports_client = MongoClient(reports_uri, serverSelectionTimeoutMS=5000)
                reports_db = reports_client[reports_db_name]
                reports_collection = reports_db[reports_collection_name]
                
                # Search for inspection data that matches this call_id
                # The call_identifier might have timestamp appended, so search by partial match
                query = {"$or": [
                    {"call_identifier": call_id},
                    {"call_identifier": {"$regex": f"^{call_id}_"}},
                    {"call_id": call_id}
                ]}
                
                inspection_data = reports_collection.find_one(query, sort=[("_id", -1)])  # Get most recent
                reports_client.close()
                
                if inspection_data:
                    print(f"✅ Found extracted inspection data in reports database")
                    print(f"🔑 Data keys: {list(inspection_data.keys())}")
                    print(f"👤 Vehicle owner: {inspection_data.get('vehicle_owner', 'N/A')}")
                    print(f"🚗 Vehicle: {inspection_data.get('make_model_year', 'N/A')}")
                    
                    # Convert MongoDB ObjectId to string if present
                    if '_id' in inspection_data:
                        del inspection_data['_id']
                    
                    logger.info(f"Successfully retrieved inspection data for call {call_id} from reports database")
                    return inspection_data
                else:
                    print(f"⚠️ No extracted inspection data found in reports database for call_id: {call_id}")
                    
            except Exception as db_error:
                print(f"❌ Error accessing reports database: {db_error}")
                logger.error(f"Error accessing reports database: {db_error}")
            
            # Fallback: Return minimal inspection data structure if no extracted data found
            logger.info(f"Using fallback data for call ID: {call_id}")
            print(f"📋 Using fallback data for call_id: {call_id}")
            
            return {
                "call_id": call_id,
                "vehicle_owner": "Unknown",
                "client_email": "<EMAIL>", 
                "unit_number": "Unknown",
                "car_type": "Unknown",
                "inspection_date": "2025-06-18",
                "checklist_items": [],
                "inspector_name": "AI Assistant"
            }
            
        except Exception as e:
            logger.error(f"Error processing transcript {call_id}: {e}")
            print(f"❌ Error in process_transcript: {e}")
            return None
    
    def save_processed_data(self, call_id: str, inspection_data: Dict[str, Any]):
        """
        Save processed inspection data to MongoDB.
        
        Args:
            call_id: The ID of the call
            inspection_data: The processed inspection data
        """
        try:
            if not self.db:
                self.connect()
                
            collection = self.db.processed_inspections
            result = collection.insert_one(inspection_data)
            logger.info(f"Saved processed data for call {call_id} with ID: {result.inserted_id}")
            
        except Exception as e:
            logger.error(f"Error saving processed data for call {call_id}: {e}")
    
    def close(self):
        """Close MongoDB connection."""
        if self.mongo_client:
            self.mongo_client.close()
            logger.info("MongoDB connection closed")