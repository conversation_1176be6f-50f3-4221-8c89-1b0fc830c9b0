#!/usr/bin/env python
"""
Module for generating inspection reports from call transcripts.
This module is used by the main application to generate reports from stored transcripts.
"""
import os
import asyncio
import logging
from typing import Optional, Dict, Any

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("inspection_report")

async def generate_inspection_report_from_transcript(transcript_content: str, call_identifier: str) -> Optional[str]:
    """
    Generate an inspection report directly from transcript content.
    
    Args:
        transcript_content: The transcript text content
        call_identifier: A unique identifier for the call/transcript
        
    Returns:
        Path to the generated PDF report or None if generation failed
    """
    from scripts.generate_inspection_report import generate_inspection_report_from_transcript as gen_report
    
    try:
        logger.info(f"Generating inspection report for transcript {call_identifier}")
        
        # Call the implementation in the generate_inspection_report module
        pdf_path = await gen_report(transcript_content, call_identifier)
        
        if pdf_path:
            logger.info(f"Successfully generated report: {pdf_path}")
            return pdf_path
        else:
            logger.error(f"Failed to generate report for {call_identifier}")
            return None
            
    except Exception as e:
        logger.error(f"Error generating inspection report: {e}")
        return None

async def generate_inspection_report_from_call_id(call_id: str) -> Optional[str]:
    """
    Generate an inspection report from a call ID that has a transcript in MongoDB.
    
    Args:
        call_id: The ID of the call to process
        
    Returns:
        Path to the generated PDF report or None if generation failed
    """
    from scripts.generate_inspection_report import generate_inspection_report
    
    try:
        logger.info(f"Generating inspection report for call ID {call_id}")
        
        # Call the implementation in the generate_inspection_report module
        pdf_path = await generate_inspection_report(call_id)
        
        if pdf_path:
            logger.info(f"Successfully generated report: {pdf_path}")
            return pdf_path
        else:
            logger.error(f"Failed to generate report for call ID {call_id}")
            return None
            
    except Exception as e:
        logger.error(f"Error generating inspection report: {e}")
        return None

def get_transcript_from_mongodb(call_id: str) -> Optional[Dict[str, Any]]:
    """
    Retrieve a transcript from MongoDB by call ID.
    
    Args:
        call_id: The call ID to retrieve
        
    Returns:
        The transcript document or None if not found
    """
    from pymongo import MongoClient
    import os
    from dotenv import load_dotenv
    
    load_dotenv()
    
    MONGO_URI = os.getenv('MONGO_URI', '****************************************')
    MONGO_DB = os.getenv('MONGO_DB', 'voice_assistant')
    MONGO_COLLECTION = os.getenv('MONGO_COLLECTION', 'transcripts')
    
    try:
        # Connect to MongoDB
        client = MongoClient(MONGO_URI)
        db = client[MONGO_DB]
        
        # Search across all transcript collections with pattern matching
        collections = [coll for coll in db.list_collection_names() 
                      if coll.startswith(f"{MONGO_COLLECTION}_")]
        
        for collection_name in collections:
            collection = db[collection_name]
            
            # Try exact match first
            transcript = collection.find_one({"call_id": call_id})
            if transcript:
                client.close()
                return transcript
                
            # Try partial match with call_identifier
            transcript = collection.find_one({"call_identifier": {"$regex": call_id}})
            if transcript:
                client.close()
                return transcript
        
        logger.warning(f"Transcript with call_id {call_id} not found")
        client.close()
        return None
        
    except Exception as e:
        logger.error(f"Error retrieving transcript from MongoDB: {e}")
        return None

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python inspection_report.py <call_id>")
        sys.exit(1)
        
    call_id = sys.argv[1]
    
    # Get the transcript
    transcript_doc = get_transcript_from_mongodb(call_id)
    
    if not transcript_doc:
        print(f"Transcript with call_id {call_id} not found")
        sys.exit(1)
        
    # Extract the transcript content
    transcript_content = transcript_doc.get("transcript")
    if not transcript_content:
        print(f"No transcript content found for call_id {call_id}")
        sys.exit(1)
        
    # Generate the report
    result = asyncio.run(generate_inspection_report_from_transcript(
        transcript_content, 
        call_id
    ))
    
    if result:
        print(f"Generated report: {result}")
        sys.exit(0)
    else:
        print("Failed to generate report")
        sys.exit(1) 