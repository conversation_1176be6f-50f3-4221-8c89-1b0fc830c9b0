#!/usr/bin/env python
"""
Render inspection data into HTML using Jinja2 templates.
"""
import os
import json
from typing import Dict, Any, Optional
from datetime import datetime
import logging
from jinja2 import Environment, FileSystemLoader

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("template_renderer")

class TemplateRenderer:
    """
    Renders inspection data into HTML using templates.
    """
    
    def __init__(self, template_dir: str = None):
        """
        Initialize the template renderer.
        
        Args:
            template_dir: Path to the templates directory
        """
        if template_dir is None:
            # Default to the templates directory in the project root
            template_dir = os.path.join(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                'templates'
            )
            
        self.template_dir = template_dir
        self.env = Environment(
            loader=FileSystemLoader(template_dir),
            autoescape=True
        )
        
        # Add custom filters
        self.env.filters['format_date'] = self.format_date
        
    def format_date(self, date_str: str) -> str:
        """Convert ISO date to formatted date string."""
        try:
            if isinstance(date_str, datetime):
                dt = date_str
            else:
                dt = datetime.fromisoformat(date_str)
            return dt.strftime("%Y-%m-%d")
        except (ValueError, TypeError):
            return date_str
    
    def map_status(self, value: str) -> Dict[str, str]:
        """
        Map inspection answers to status categories.
        
        Args:
            value: The inspection answer
            
        Returns:
            Dict with status class and display text
        """
        value_lower = value.lower() if value else ""
        
        # Map to status categories
        if any(term in value_lower for term in ["good", "excellent", "ok", "fine", "working"]):
            return {"class": "status-ok", "text": "OK"}
        elif any(term in value_lower for term in ["repair", "replace", "bad", "poor", "not working"]):
            return {"class": "status-repair", "text": "Needs Repair"}
        elif any(term in value_lower for term in ["changed", "replaced", "serviced"]):
            return {"class": "status-changed", "text": "Changed"}
        elif any(term in value_lower for term in ["n/a", "not applicable", "not installed"]):
            return {"class": "status-na", "text": "N/A"}
        else:
            # Default to OK if status can't be determined
            return {"class": "status-ok", "text": "OK"}

    def prepare_template_data(self, inspection_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare inspection data for template rendering.
        
        Args:
            inspection_data: The inspection data from the transcript processor
            
        Returns:
            Dict with formatted data for the template
        """
        # Get the inner inspection data if it's wrapped
        if "inspection_data" in inspection_data:
            data = inspection_data["inspection_data"]
        else:
            data = inspection_data
            
        # Extract identification data
        identification = data.get("identification", {})
        vehicle_info = data.get("vehicle_info", {})
        exterior = data.get("exterior", {})
        under_hood = data.get("under_the_hood", {})
        brakes_suspension = data.get("brakes_and_suspension", {})
        interior = data.get("interior", {})
        road_test = data.get("road_test", {})
        final = data.get("final", {})
        
        # Format checklist items
        checklist_items = []
        
        # Add exterior items
        for key, value in exterior.items():
            item_name = key.replace("_", " ").title()
            status = self.map_status(value)
            checklist_items.append({
                "name": item_name,
                "status_class": status["class"],
                "status_text": status["text"],
                "notes": value
            })
            
        # Add under hood items
        for key, value in under_hood.items():
            item_name = key.replace("_", " ").title()
            status = self.map_status(value)
            checklist_items.append({
                "name": item_name,
                "status_class": status["class"],
                "status_text": status["text"],
                "notes": value
            })
            
        # Add brakes and suspension items
        for key, value in brakes_suspension.items():
            item_name = key.replace("_", " ").title()
            status = self.map_status(value)
            checklist_items.append({
                "name": item_name,
                "status_class": status["class"],
                "status_text": status["text"],
                "notes": value
            })
            
        # Add interior items
        for key, value in interior.items():
            item_name = key.replace("_", " ").title()
            status = self.map_status(value)
            checklist_items.append({
                "name": item_name,
                "status_class": status["class"],
                "status_text": status["text"],
                "notes": value
            })
        
        # Format the vehicle info
        make_model_year = f"{identification.get('vehicle_make', '')} {identification.get('vehicle_model', '')}"
        if identification.get('vehicle_year'):
            make_model_year += f" / {identification.get('vehicle_year')}"
            
        # Prepare the template data
        template_data = {
            "owner": identification.get("customer_name", ""),
            "vin": vehicle_info.get("vin", ""),
            "make_model_year": make_model_year,
            "license_plate": identification.get("license_plate", ""),
            "odometer": vehicle_info.get("odometer_reading", ""),
            "engine_hours": "",  # Not available in transcript
            "inspection_date": vehicle_info.get("date_of_inspection", datetime.now().strftime("%Y-%m-%d")),
            "service_level": "Standard",  # Default value
            "checklist_items": checklist_items,
            "inspector_name": identification.get("technician_name", ""),
            "inspection_location": "",  # Not available in transcript
            "additional_notes": final.get("additional_notes", "")
        }
        
        return template_data
        
    def render_html(self, inspection_data: Dict[str, Any], template_name: str = "result-inspection.html") -> str:
        """
        Render inspection data to HTML.
        
        Args:
            inspection_data: The inspection data
            template_name: Name of the template to use
            
        Returns:
            Rendered HTML string
        """
        try:
            # Prepare the template data
            template_data = self.prepare_template_data(inspection_data)
            
            # Get the template
            template = self.env.get_template(template_name)
            
            # Render the template
            rendered_html = template.render(**template_data)
            logger.info(f"Successfully rendered template {template_name}")
            
            return rendered_html
            
        except Exception as e:
            logger.error(f"Error rendering template: {e}")
            raise
            
    def save_html(self, html: str, output_path: str) -> bool:
        """
        Save the rendered HTML to a file.
        
        Args:
            html: The rendered HTML string
            output_path: Path to save the HTML file
            
        Returns:
            True if saved successfully, False otherwise
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Write the HTML to the file
            with open(output_path, 'w') as f:
                f.write(html)
                
            logger.info(f"Saved HTML to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving HTML: {e}")
            return False

if __name__ == "__main__":
    # Example usage
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python template_renderer.py <inspection_data_file>")
        sys.exit(1)
        
    data_file = sys.argv[1]
    
    try:
        # Load the inspection data
        with open(data_file, 'r') as f:
            inspection_data = json.load(f)
            
        # Render the template
        renderer = TemplateRenderer()
        html = renderer.render_html(inspection_data)
        
        # Save the rendered HTML
        output_path = os.path.join(
            os.path.dirname(data_file),
            f"report_{datetime.now().strftime('%Y%m%d%H%M%S')}.html"
        )
        renderer.save_html(html, output_path)
        
        print(f"Saved report to {output_path}")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)