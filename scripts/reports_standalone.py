#!/usr/bin/env python
"""
Standalone application for the AIREADY Inspection Reports UI.
This can be run separately from the main voice assistant application.
"""
import os
import sys
import logging
from typing import Dict, Any
import uvicorn
from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from dotenv import load_dotenv

# Add parent directory to path to allow imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the reports router
from scripts.report_ui import router as reports_router

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("reports_standalone")

# Load environment variables
load_dotenv()

# Directories
REPORTS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'reports')
TEMPLATES_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'templates')
STATIC_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'static')

# Create directory if they don't exist
os.makedirs(REPORTS_DIR, exist_ok=True)
os.makedirs(STATIC_DIR, exist_ok=True)
os.makedirs(os.path.join(STATIC_DIR, 'css'), exist_ok=True)

# Create FastAPI app
app = FastAPI(
    title="AIREADY Inspection Reports",
    description="View and download vehicle inspection reports",
    version="1.0.0"
)

# Mount static files
app.mount("/static", StaticFiles(directory=STATIC_DIR), name="static")

# Templates
templates = Jinja2Templates(directory=TEMPLATES_DIR)

# Include the reports router
app.include_router(reports_router)

# Root route redirects to reports
@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """Redirect to reports dashboard."""
    return templates.TemplateResponse(
        "reports_dashboard.html", 
        {"request": request, "title": "AIREADY Inspection Reports"}
    )

# Check MongoDB connection
@app.on_event("startup")
async def startup_event():
    """Check MongoDB connection on startup."""
    try:
        from pymongo import MongoClient
        
        MONGO_URI = os.getenv('MONGO_URI', '****************************************')
        
        # Test connection
        client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=5000)
        client.server_info()  # Will raise exception if cannot connect
        
        logger.info("Successfully connected to MongoDB")
        client.close()
        
        # Create indexes if needed
        from scripts.report_ui import create_mongo_indexes
        create_mongo_indexes()
        logger.info("MongoDB indexes created/verified")
        
    except Exception as e:
        logger.error(f"MongoDB connection error: {e}")
        logger.warning("Reports UI will start, but MongoDB features may not work correctly")

# Health check endpoint
@app.get("/health")
async def health() -> Dict[str, Any]:
    """Health check endpoint."""
    return {
        "status": "ok",
        "message": "AIREADY Inspection Reports UI is running"
    }

if __name__ == "__main__":
    # Run standalone server
    port = int(os.getenv("REPORTS_PORT", 10001))
    logger.info(f"Starting AIREADY Inspection Reports UI on port {port}")
    uvicorn.run(app, host="0.0.0.0", port=port)