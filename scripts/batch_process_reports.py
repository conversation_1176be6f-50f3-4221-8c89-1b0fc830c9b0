#!/usr/bin/env python
"""
Batch process transcripts to generate inspection reports.
This script finds recent transcripts and generates reports for each one.
"""
import os
import sys
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import argparse

# Add parent directory to path to allow imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import components
from scripts.generate_inspection_report import generate_inspection_report
from pymongo import MongoClient
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("batch_process_reports")

# Load environment variables
load_dotenv()

# MongoDB Configuration
MONGO_URI = os.getenv('MONGO_URI', '****************************************')
MONGO_DB = os.getenv('MON<PERSON><PERSON>_<PERSON>', 'voice_assistant')
MONGO_COLLECTION = os.getenv('MONGO_COLLECTION', 'transcripts')

async def find_recent_transcripts(days: int = 7, limit: int = 10) -> List[Dict[str, Any]]:
    """
    Find recent transcripts from MongoDB.
    
    Args:
        days: Number of days to look back
        limit: Maximum number of transcripts to process
        
    Returns:
        List of transcript documents
    """
    client = MongoClient(MONGO_URI)
    db = client[MONGO_DB]
    
    try:
        # Calculate cutoff date
        cutoff_date = datetime.now() - timedelta(days=days)
        
        # Find all transcript collections
        transcript_collections = [coll for coll in db.list_collection_names() 
                                 if coll.startswith(f"{MONGO_COLLECTION}_")]
        
        if not transcript_collections:
            logger.warning("No transcript collections found in MongoDB")
            return []
            
        # Gather transcripts from all collections
        transcripts = []
        for coll_name in transcript_collections:
            collection = db[coll_name]
            
            # Query for transcripts
            query = {}
            if days > 0:
                # Add date filter if days is specified
                query["date_time"] = {"$gte": cutoff_date}
                
            # Find transcripts, sort by date (newest first)
            for doc in collection.find(query).sort("date_time", -1).limit(limit):
                # Extract key information
                call_id = doc.get("call_id", "Unknown")
                timestamp = doc.get("formatted_time", "Unknown")
                timestamp_obj = doc.get("date_time")
                
                # Format timestamp if it's a datetime object
                if timestamp_obj:
                    try:
                        if isinstance(timestamp_obj, datetime):
                            timestamp = timestamp_obj.strftime("%Y-%m-%d %H:%M:%S")
                    except:
                        pass
                        
                transcripts.append({
                    "call_id": call_id,
                    "timestamp": timestamp,
                    "collection": coll_name,
                    "identifier": doc.get("call_identifier", call_id)
                })
                
                # Break if we've reached the limit
                if len(transcripts) >= limit:
                    break
            
            # Break if we've reached the limit
            if len(transcripts) >= limit:
                break
                
        return transcripts
        
    except Exception as e:
        logger.error(f"Error finding recent transcripts: {e}")
        return []
    finally:
        client.close()

async def process_transcripts(transcripts: List[Dict[str, Any]], save_to_mongodb: bool = True) -> List[str]:
    """
    Process a list of transcripts and generate reports.
    
    Args:
        transcripts: List of transcript documents
        save_to_mongodb: Whether to save PDFs to MongoDB
        
    Returns:
        List of generated PDF paths
    """
    results = []
    
    for i, transcript in enumerate(transcripts):
        logger.info(f"Processing transcript {i+1}/{len(transcripts)}: {transcript['identifier']}")
        
        try:
            pdf_path = await generate_inspection_report(
                transcript['identifier'],
                save_to_mongodb=save_to_mongodb
            )
            
            if pdf_path:
                logger.info(f"Generated report: {pdf_path}")
                results.append(pdf_path)
            else:
                logger.error(f"Failed to generate report for {transcript['identifier']}")
                
        except Exception as e:
            logger.error(f"Error processing transcript {transcript['identifier']}: {e}")
            
        # Add a small delay between processing to avoid rate limits
        await asyncio.sleep(1)
        
    return results

async def main():
    """Main entry point for batch processing."""
    parser = argparse.ArgumentParser(description="Batch process transcripts for report generation")
    parser.add_argument("--days", type=int, default=7, help="Number of days to look back for transcripts")
    parser.add_argument("--limit", type=int, default=10, help="Maximum number of transcripts to process")
    parser.add_argument("--skip-mongodb", action="store_true", help="Skip storing PDFs in MongoDB")
    args = parser.parse_args()
    
    # Positional limit argument override
    if len(sys.argv) >= 2 and sys.argv[1].isdigit():
        args.limit = int(sys.argv[1])
    
    logger.info(f"Finding transcripts from the last {args.days} days (limit: {args.limit})")
    
    # Find recent transcripts
    transcripts = await find_recent_transcripts(days=args.days, limit=args.limit)
    
    if not transcripts:
        logger.warning("No transcripts found to process")
        return 1
        
    logger.info(f"Found {len(transcripts)} transcripts to process")
    
    # Process transcripts
    results = await process_transcripts(transcripts, not args.skip_mongodb)
    
    logger.info(f"Batch processing complete. Generated {len(results)} reports.")
    
    if not results:
        logger.warning("No reports were generated")
        return 1
        
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)