#!/usr/bin/env python3
"""
Flexible extraction system that can handle various conversation patterns.
This module provides multiple extraction strategies for different types of conversations.
"""

import re
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timezone

class FlexibleExtractor:
    """A flexible extraction system that can handle various conversation patterns."""

    def __init__(self):
        self.vehicle_keywords = [
            'ford', 'chevy', 'chevrolet', 'toyota', 'honda', 'nissan', 'gmc', 'ram', 'dodge',
            'f-150', 'f-250', 'f-350', 'f-450', 'f-550', 'silverado', 'sierra', 'tacoma',
            'camry', 'accord', 'altima', 'truck', 'car', 'vehicle', 'unit'
        ]

        self.inspection_keywords = [
            'brake', 'brakes', 'tire', 'tires', 'engine', 'oil', 'light', 'lights',
            'door', 'doors', 'window', 'windows', 'seat', 'seatbelt', 'mirror',
            'windshield', 'battery', 'fluid', 'belt', 'hose', 'filter', 'suspension'
        ]

        self.status_keywords = {
            'good': ['good', 'fine', 'ok', 'okay', 'working', 'functional', 'excellent', 'great'],
            'fair': ['fair', 'decent', 'acceptable', 'average', 'moderate'],
            'poor': ['poor', 'bad', 'broken', 'damaged', 'worn', 'needs repair', 'replace', 'fix'],
            'na': ['n/a', 'na', 'not applicable', 'not available', 'none', 'no']
        }

        # Conversation classification patterns
        self.inspection_patterns = [
            r'inspection\s+protocol',
            r'we\s+will\s+be\s+doing.*inspection',
            r'beginning\s+inspection',
            r'check\s+the\s+\w+',
            r'condition\s+of',
            r'how\s+(?:is|are)\s+the\s+\w+',
            r'what\s+about\s+the\s+\w+'
        ]

        self.vehicle_info_patterns = [
            r'unit\s+number',
            r'maker\s+and\s+model',
            r'vehicle\s+make',
            r'client.*name',
            r'email\s+address',
            r'odometer\s+reading'
        ]

        self.work_order_patterns = [
            r'work\s+order',
            r'service\s+request',
            r'maintenance\s+task',
            r'repair\s+order'
        ]
    
    def classify_conversation(self, text: str, structured_conv: List[Dict] = None) -> Dict[str, Any]:
        """Classify the conversation type and confidence level."""
        classification = {
            'type': 'unknown',
            'confidence': 0.0,
            'inspection_score': 0,
            'vehicle_info_score': 0,
            'work_order_score': 0,
            'conversation_quality': 'poor',
            'extraction_recommendation': 'openai_fallback'
        }

        text_lower = text.lower()

        # Score inspection patterns
        for pattern in self.inspection_patterns:
            matches = len(re.findall(pattern, text_lower, re.IGNORECASE))
            classification['inspection_score'] += matches

        # Score vehicle info patterns
        for pattern in self.vehicle_info_patterns:
            matches = len(re.findall(pattern, text_lower, re.IGNORECASE))
            classification['vehicle_info_score'] += matches

        # Score work order patterns
        for pattern in self.work_order_patterns:
            matches = len(re.findall(pattern, text_lower, re.IGNORECASE))
            classification['work_order_score'] += matches

        # Analyze structured conversation if available
        if structured_conv:
            qa_pairs = 0
            inspection_questions = 0
            vehicle_questions = 0

            for i, item in enumerate(structured_conv):
                if item.get('role') == 'assistant':
                    content = item.get('content', '').lower()

                    # Count Q&A pairs
                    if '?' in content and i + 1 < len(structured_conv):
                        if structured_conv[i + 1].get('role') == 'user':
                            qa_pairs += 1

                    # Count inspection questions
                    if any(keyword in content for keyword in self.inspection_keywords):
                        inspection_questions += 1

                    # Count vehicle info questions
                    if any(re.search(pattern, content) for pattern in self.vehicle_info_patterns):
                        vehicle_questions += 1

            classification['qa_pairs'] = qa_pairs
            classification['inspection_questions'] = inspection_questions
            classification['vehicle_questions'] = vehicle_questions

            # Boost scores based on structured conversation
            classification['inspection_score'] += inspection_questions * 2
            classification['vehicle_info_score'] += vehicle_questions * 2

        # Determine conversation type
        total_score = (classification['inspection_score'] +
                      classification['vehicle_info_score'] +
                      classification['work_order_score'])

        if classification['inspection_score'] >= 3:
            classification['type'] = 'inspection'
            classification['confidence'] = min(0.9, classification['inspection_score'] / 10)
        elif classification['vehicle_info_score'] >= 2:
            classification['type'] = 'vehicle_related'
            classification['confidence'] = min(0.8, classification['vehicle_info_score'] / 8)
        elif classification['work_order_score'] >= 1:
            classification['type'] = 'work_order'
            classification['confidence'] = min(0.7, classification['work_order_score'] / 5)
        elif total_score > 0:
            classification['type'] = 'mixed'
            classification['confidence'] = min(0.6, total_score / 10)
        else:
            classification['type'] = 'other'
            classification['confidence'] = 0.1

        # Determine conversation quality
        if structured_conv and len(structured_conv) >= 10:
            classification['conversation_quality'] = 'good'
        elif structured_conv and len(structured_conv) >= 5:
            classification['conversation_quality'] = 'fair'
        elif len(text) >= 300:
            classification['conversation_quality'] = 'fair'
        else:
            classification['conversation_quality'] = 'poor'

        # Determine extraction recommendation
        if (classification['inspection_score'] >= 2 or
            classification['vehicle_info_score'] >= 3 or
            classification['conversation_quality'] == 'good'):
            classification['extraction_recommendation'] = 'flexible_extraction'
        elif classification['confidence'] >= 0.3:
            classification['extraction_recommendation'] = 'hybrid_approach'
        else:
            classification['extraction_recommendation'] = 'openai_fallback'

        return classification

    def extract_vehicle_info(self, text: str, structured_conv: List[Dict] = None) -> Dict[str, str]:
        """Extract vehicle information using multiple strategies."""
        info = {
            'vehicle_owner': '',
            'client_email': '',
            'unit_number': '',
            'vehicle_make': '',
            'vehicle_model': '',
            'vehicle_year': '',
            'make_model_year': '',
            'odometer_reading': '',
            'car_type': 'General Vehicle'
        }
        
        # Strategy 1: Structured conversation analysis
        if structured_conv:
            info.update(self._extract_from_structured_conversation(structured_conv))
        
        # Strategy 2: Pattern matching on full text
        info.update(self._extract_from_text_patterns(text))
        
        # Strategy 3: Keyword-based extraction
        info.update(self._extract_from_keywords(text))
        
        # Post-process and clean up
        info = self._clean_extracted_info(info)
        
        return info
    
    def extract_inspection_items(self, text: str, structured_conv: List[Dict] = None) -> List[Dict[str, str]]:
        """Extract inspection items using multiple strategies."""
        items = []
        
        # Strategy 1: Q&A pairs from structured conversation
        if structured_conv:
            items.extend(self._extract_qa_pairs(structured_conv))
        
        # Strategy 2: Keyword-based extraction from text
        items.extend(self._extract_from_inspection_keywords(text))
        
        # Strategy 3: Statement-based extraction
        items.extend(self._extract_from_statements(text))
        
        # Remove duplicates and clean up
        items = self._deduplicate_items(items)
        
        return items
    
    def _extract_from_structured_conversation(self, structured_conv: List[Dict]) -> Dict[str, str]:
        """Extract info from structured conversation."""
        info = {}
        
        for i, item in enumerate(structured_conv):
            if item.get('role') == 'assistant':
                content = item.get('content', '').lower()
                
                # Look for next user response
                if i + 1 < len(structured_conv) and structured_conv[i + 1].get('role') == 'user':
                    response = structured_conv[i + 1].get('content', '')
                    
                    # Extract based on question patterns
                    if 'unit number' in content:
                        info['unit_number'] = response.strip()
                    elif 'name' in content and 'client' in content:
                        info['vehicle_owner'] = response.strip()
                    elif 'email' in content:
                        info['client_email'] = response.strip()
                    elif 'maker' in content and 'model' in content:
                        info['make_model_year'] = response.strip()
                        # Try to split make and model
                        parts = response.strip().split()
                        if len(parts) >= 2:
                            info['vehicle_make'] = parts[0]
                            info['vehicle_model'] = ' '.join(parts[1:])
                    elif 'odometer' in content:
                        info['odometer_reading'] = response.strip()
                
                # Look for inspection type announcements
                if 'we will be doing' in content and 'inspection' in content:
                    # Extract inspection type
                    match = re.search(r'we will be doing a ([^.]+) inspection', content, re.IGNORECASE)
                    if match:
                        info['car_type'] = match.group(1).strip()
        
        return info
    
    def _extract_from_text_patterns(self, text: str) -> Dict[str, str]:
        """Extract info using regex patterns."""
        info = {}
        
        # Email pattern
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, text, re.IGNORECASE)
        if emails:
            info['client_email'] = emails[0]
        
        # Unit number patterns
        unit_patterns = [
            r'unit[:\s#]*([A-Z0-9-]+)',
            r'#([A-Z0-9-]+)',
            r'unit number[:\s]*([A-Z0-9-]+)'
        ]
        for pattern in unit_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                info['unit_number'] = matches[0]
                break
        
        # Vehicle make/model patterns
        vehicle_patterns = [
            r'(\d{4})\s+(ford|chevy|chevrolet|toyota|honda|nissan|gmc|ram|dodge)\s+([a-z0-9-]+)',
            r'(ford|chevy|chevrolet|toyota|honda|nissan|gmc|ram|dodge)\s+([a-z0-9-]+)\s*(\d{4})?',
            r'(ford|chevy|chevrolet|toyota|honda|nissan|gmc|ram|dodge)\s+(f-\d+|silverado|sierra|tacoma|camry|accord|altima)'
        ]
        for pattern in vehicle_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                match = matches[0]
                if len(match) == 3 and match[0].isdigit():  # Year Make Model
                    info['vehicle_year'] = match[0]
                    info['vehicle_make'] = match[1].title()
                    info['vehicle_model'] = match[2].title()
                    info['make_model_year'] = f"{match[1].title()} {match[2].title()} / {match[0]}"
                elif len(match) >= 2:  # Make Model (Year?)
                    info['vehicle_make'] = match[0].title()
                    info['vehicle_model'] = match[1].title()
                    if len(match) > 2 and match[2]:
                        info['vehicle_year'] = match[2]
                        info['make_model_year'] = f"{match[0].title()} {match[1].title()} / {match[2]}"
                    else:
                        info['make_model_year'] = f"{match[0].title()} {match[1].title()}"
                break
        
        # Odometer patterns
        odometer_patterns = [
            r'odometer[:\s]+([0-9,]+)\s*(miles?|mi|km)',
            r'([0-9,]+)\s*(miles?|mi)\s*on\s*odometer',
            r'([0-9,]+)\s*(miles?|mi|km)'
        ]
        for pattern in odometer_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                info['odometer_reading'] = f"{matches[0][0]} {matches[0][1]}"
                break
        
        # Name patterns
        name_patterns = [
            r'name[:\s]+([A-Z][a-z]+\s+[A-Z][a-z]+)',
            r'customer[:\s]+([A-Z][a-z]+\s+[A-Z][a-z]+)',
            r'client[:\s]+([A-Z][a-z]+\s+[A-Z][a-z]+)'
        ]
        for pattern in name_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                info['vehicle_owner'] = matches[0]
                break
        
        return info
    
    def _extract_from_keywords(self, text: str) -> Dict[str, str]:
        """Extract info based on keyword presence."""
        info = {}
        text_lower = text.lower()
        
        # Determine vehicle type based on keywords
        if any(keyword in text_lower for keyword in ['truck', 'f-150', 'f-250', 'f-350', 'silverado', 'sierra']):
            info['car_type'] = 'Light-Duty Vehicle'
        elif any(keyword in text_lower for keyword in ['tractor', 'semi', 'trailer']):
            info['car_type'] = 'Power-Unit/Tractor'
        elif any(keyword in text_lower for keyword in ['forklift', 'lift']):
            info['car_type'] = 'Liftgate/Forklift'
        
        return info
    
    def _extract_qa_pairs(self, structured_conv: List[Dict]) -> List[Dict[str, str]]:
        """Extract Q&A pairs for inspection items."""
        items = []
        
        for i, item in enumerate(structured_conv):
            if item.get('role') == 'assistant':
                question = item.get('content', '')
                
                # Look for next user response
                if i + 1 < len(structured_conv) and structured_conv[i + 1].get('role') == 'user':
                    response = structured_conv[i + 1].get('content', '')
                    
                    # Check if this looks like an inspection question
                    if self._is_inspection_question(question):
                        item_name = self._extract_item_name(question)
                        status = self._determine_status(response)
                        
                        items.append({
                            'item': item_name,
                            'status': status,
                            'notes': response.strip()
                        })
        
        return items
    
    def _extract_from_inspection_keywords(self, text: str) -> List[Dict[str, str]]:
        """Extract inspection items based on keywords in text."""
        items = []
        
        for keyword in self.inspection_keywords:
            # Look for mentions of this inspection item
            pattern = rf'\b{keyword}[s]?\b[^.!?]*[.!?]'
            matches = re.findall(pattern, text, re.IGNORECASE)
            
            for match in matches:
                status = self._determine_status(match)
                if status != 'UNKNOWN':
                    items.append({
                        'item': keyword.title(),
                        'status': status,
                        'notes': match.strip()
                    })
        
        return items
    
    def _extract_from_statements(self, text: str) -> List[Dict[str, str]]:
        """Extract inspection items from general statements."""
        items = []
        
        # Look for statements like "The brakes are good" or "Engine oil needs changing"
        statement_patterns = [
            r'the\s+(\w+)\s+(?:is|are)\s+([^.!?]+)[.!?]',
            r'(\w+)\s+(?:is|are)\s+([^.!?]+)[.!?]',
            r'(\w+)\s+(?:needs?|requires?)\s+([^.!?]+)[.!?]'
        ]
        
        for pattern in statement_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                item_name = match[0].strip()
                description = match[1].strip()
                
                # Check if this is an inspection-related item
                if any(keyword in item_name.lower() for keyword in self.inspection_keywords):
                    status = self._determine_status(description)
                    items.append({
                        'item': item_name.title(),
                        'status': status,
                        'notes': f"{item_name} {description}"
                    })
        
        return items
    
    def _is_inspection_question(self, question: str) -> bool:
        """Check if a question is inspection-related."""
        question_lower = question.lower()
        
        # Check for inspection keywords
        if any(keyword in question_lower for keyword in self.inspection_keywords):
            return True
        
        # Check for question patterns
        inspection_patterns = [
            r'how\s+(?:is|are|do|does)',
            r'what\s+(?:is|are)\s+the\s+condition',
            r'check\s+the',
            r'condition\s+of'
        ]
        
        return any(re.search(pattern, question_lower) for pattern in inspection_patterns)
    
    def _extract_item_name(self, question: str) -> str:
        """Extract the item name from a question."""
        question_lower = question.lower()
        
        # Look for inspection keywords in the question
        for keyword in self.inspection_keywords:
            if keyword in question_lower:
                return keyword.title()
        
        # Try to extract from common question patterns
        patterns = [
            r'(?:how|what).*?(brake|tire|engine|oil|light|door|window|seat|belt|mirror|windshield|battery|fluid|filter|suspension)',
            r'condition\s+of\s+(?:the\s+)?(\w+)',
            r'check\s+(?:the\s+)?(\w+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, question_lower)
            if match:
                return match.group(1).title()
        
        # Fallback: use the question itself (shortened)
        return question[:30] + "..." if len(question) > 30 else question
    
    def _determine_status(self, response: str) -> str:
        """Determine status from response text."""
        response_lower = response.lower()
        
        for status, keywords in self.status_keywords.items():
            if any(keyword in response_lower for keyword in keywords):
                return status.upper()
        
        # Default mapping
        if any(word in response_lower for word in ['yes', 'working', 'functional']):
            return 'OK'
        elif any(word in response_lower for word in ['no', 'not working', 'broken']):
            return 'POOR'
        
        return 'UNKNOWN'
    
    def _deduplicate_items(self, items: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """Remove duplicate inspection items."""
        seen = set()
        unique_items = []
        
        for item in items:
            item_key = item['item'].lower()
            if item_key not in seen:
                seen.add(item_key)
                unique_items.append(item)
        
        return unique_items
    
    def _clean_extracted_info(self, info: Dict[str, str]) -> Dict[str, str]:
        """Clean and validate extracted information."""
        # Remove empty values
        cleaned = {k: v for k, v in info.items() if v and v.strip()}
        
        # Add current timestamp for missing dates
        current_utc = datetime.now(timezone.utc)
        if 'inspection_date_vehicle' not in cleaned:
            cleaned['inspection_date_vehicle'] = current_utc.strftime('%Y-%m-%d')
        if 'report_generation_datetime' not in cleaned:
            cleaned['report_generation_datetime'] = current_utc.strftime('%Y-%m-%d %H:%M:%S')
        if 'signoff_date' not in cleaned:
            cleaned['signoff_date'] = current_utc.strftime('%Y-%m-%d')
        
        return cleaned
