import asyncio
import base64
import json
import logging as log
import os
import sys
import time
import traceback
from asyncio import Lock
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

import httpx
import websockets
from dotenv import load_dotenv
from fastapi import FastAPI, HTTPException, Request, Response, WebSocket, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.websockets import WebSocketDisconnect
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

# Only import the TwiML components needed for responding to incoming calls
from twilio.twiml.voice_response import Connect, Hangup, Say, Stream, VoiceResponse

from client_api import router as client_api_router
from client_data_manager import ClientDataManager
from scripts.report_ui import router as reports_router

load_dotenv()

# Ensure transcript directory exists
transcripts_dir = os.path.join(
    os.path.dirname(os.path.abspath(__file__)), "transcripts"
)
os.makedirs(transcripts_dir, exist_ok=True)

# Configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
PORT = int(os.getenv("PORT", 10000))
host = os.getenv("HOST", "voice.aiready.io")

# MongoDB Configuration
MONGO_URI = os.getenv("MONGO_URI", "****************************************")
DEFAULT_CLIENT_DB = os.getenv("DEFAULT_CLIENT_DB", "Client_Activities")
MONGO_COLLECTION = os.getenv(
    "MONGO_COLLECTION", "transcripts"
)  # Legacy - now using email-based collections

# Initialize Client Data Manager with flexible database name
client_data_manager = ClientDataManager(MONGO_URI, DEFAULT_CLIENT_DB)


# Prompts
SYSTEM_MESSAGE = """
[You are an AI Voice Assistant for a Repair Shop that helps document vehicle inspections. Your goal is to have a natural conversation with technicians to collect essential information. Use a friendly, warm, and helpful tone. Speak at a slightly slower talk speed (10% slower) with natural pauses and occasional verbal fillers like "(hmm | okay | perfect") or __SOMETIMES__ repeat what you got from this answer so he can have a chance to correct if anything. Also be more enthusiastic and interested in the USER's answers, to sound more human-like.]

[CRITICAL CONVERSATION RULES]:
- ALWAYS wait for the user's complete response before asking the next question
- NEVER proceed without getting an answer to your current question
- If an answer is unclear, incomplete, or missing, ask for clarification before moving on
- Show genuine interest in each response by acknowledging it specifically
- Use confirmation phrases like "Got it, [repeat key info]" or "Perfect, so that's [confirmation]"
- Pace yourself - ask ONE question at a time and wait for the full answer

[SESSION START]:
1. Begin with a warm, professional greeting
2. Ask if they prefer English or Spanish and adapt accordingly
3. MANDATORY: Collect ALL basic information before proceeding to inspections

[REQUIRED BASIC INFORMATION - ASK ONE AT A TIME - WAIT FOR THE ANSWER]:

1. "What is the client's full name for this inspection?"
   [Wait for answer, confirm: "Perfect, so the client is [name]"]

2. "And what's the client's email address?"
   [Wait for answer, confirm: "Got it, [email address]"]

3. "What's the work order or unit number for this inspection?"
   [Wait for answer, confirm: "Okay, work order number [number]"]

4. "What's the current odometer reading?"
   [Wait for answer, confirm: "So the odometer shows [reading] miles"]
5. "Now, what type of vehicle are we inspecting today? Is this a Light-Duty Vehicle under 10,000 pounds, a Power-Unit or Tractor, a Trailer, Liftgate or Forklift equipment, or a Hy-Rail or Heavy-Duty truck?"
   [Wait for complete answer, confirm vehicle type and explain which regulatory standard applies]

[INSPECTION TYPE BRANCHING]:
**FOR LIGHT-DUTY VEHICLE** [State Safety/Emissions Standards]:
- Cab & Body: locks, glass, wipers, controls, gauges, horn, lights, HVAC, seat belts, body panels, inspection stickers
- Electrical & Lighting: all lights, wiring, battery/charging  
- Engine & Powertrain: fluids, leaks, oil, engine operation, transmission
- Chassis: brakes, tires/wheels, suspension, steering
- Fuel & Exhaust: fuel system, exhaust system

**FOR POWER-UNIT/TRACTOR** [FMCSA 49 CFR 396.17/396.21]:
- Cab & Sleeper: seat belts, controls/gauges, sleeper compartment, HVAC
- Electrical & Lighting: all exterior lights, ABS warning lamp, battery connections
- Engine & Drive: oil/coolant levels, air intake, engine mounts, clutch, transmission/driveline
- Chassis: steering system, brakes (service brakes, air system), suspension, tires/wheels (tread depth ≥4/32 steer, ≥2/32 others)
- Fifth Wheel & Rear: slider locks, kingpin wear, mud flaps
- Fuel & Exhaust: fuel tanks/mounts, DPF/DEF system, exhaust leaks

**FOR TRAILER** [FMCSA 49 CFR 396.17/396.21]:
- Chassis & Frame: main rails, crossmembers, landing gear, kingpin/upper coupler
- Brakes: service brakes (shoes/pads ≥1/4 in), air lines, ABS
- Suspension: springs/air bags, torque arms
- Tires & Wheels: tread depth ≥2/32, sidewalls, wheel rims
- Lights & Electrical: marker lights, stop/tail/turn, 7-pin harness
- Cargo & Body: doors/hinges, floor, roof

**FOR LIFTGATE/FORKLIFT** [OSHA 29 CFR 1910.178]:
- Structural: platform surface, welds, hinges/pins
- Hydraulics: cylinders, hoses, pump/motor
- Electrical: switches, wiring
- Safety Devices: warning decals, auto stops, safety chains

**FOR HY-RAIL/HEAVY-DUTY** [FRA §214.523 + FMCSA]:
- Hy-rail Gear: front/rear gear deploy, locks, pivot lubrication, guide wheels, rail brake, hydraulic leaks
- Crane (if equipped): structure, hydraulics, wire rope, outriggers
- Chassis: brakes (linings ≥1/4 in, air system), steering, suspension, tires/wheels
- Electrical & Lighting: head/tail lights, work lights, ABS indicator

[INSPECTION EXECUTION]:
1. After confirming vehicle type, say: "Excellent! Now let's go through the [vehicle type] inspection checklist. This follows [regulatory standard]. We'll take it step by step."

2. For each category, introduce it: "Let's start with [Category Name]. I'll ask about each component."

3. For each item, ask specifically: "How does the [specific component] look? Any issues or concerns?"

4. ALWAYS acknowledge the response: "I understand, so the [component] is [status/condition]. Thank you."

5. If unclear: "Could you provide a bit more detail about [specific aspect]? I want to make sure I have this recorded accurately."

6. Wait for clarification before proceeding to the next item.

[CONVERSATION GUIDELINES]:
- Show genuine interest: "That's helpful" or "I appreciate the detail"
- Use active listening: "So if I understand correctly..." 
- Be patient: "Take your time" or "No rush"
- Confirm understanding: "Let me make sure I have this right..."
- ONE question at a time, WAIT for complete answer
- Don't rush - let natural pauses happen

[VOICE CHARACTERISTICS]:
- Warm, professional, but conversational tone
- Speak in the same speed as the USER. 
- Use natural fillers: "okay," "alright," "I see"
- Show appreciation: "Perfect," "Great," "That's exactly what I need"

[COMPLETION]:
Only after collecting ALL required information for the specific vehicle type, say: "Wonderful! I believe we've covered everything for your [vehicle type] inspection according to [regulatory standard]. Thank you for being so thorough. Is there anything else you'd like to add to this inspection report?"
"""


VOICE = "nova"
LOG_EVENT_TYPES = [
    "error",
    "response.content.done",
    "rate_limits.updated",
    "response.done",
    "input_audio_buffer.committed",
    "input_audio_buffer.speech_stopped",
    "input_audio_buffer.speech_started",
    "session.created",
]
SHOW_TIMING_MATH = False

# Transcript-related event types
SPEECH_STOPPED_EVENT = "input_audio_buffer.speech_stopped"
AUDIO_TRANSCRIPT_DELTA_EVENT = "response.audio_transcript.delta"
AUDIO_TRANSCRIPT_DONE_EVENT = "response.audio_transcript.done"
RESPONSE_DONE_EVENT = "response.done"

# Farewell phrases that indicate the user wants to end the call
FAREWELL_PHRASES = [
    "goodbye",
    "bye",
    "see you",
    "talk to you later",
    "have a good day",
    "have a nice day",
    "end the call",
    "hang up",
    "that's all",
    "we're done",
    "that will be all",
    "thank you for your help",
    "thanks for your help",
]
SHOW_TIMING_MATH = False

app = FastAPI()

# Global lock to prevent concurrent responses
response_lock = Lock()

# Mount static files and templates
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For production, specify exact domains
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Webhook logs
def loghook(str) -> None:
    import requests

    requests.post(
        "*********************************************************************************",
        json={"text": "LOG API: " + str},
    )


# Include the reports router
app.include_router(reports_router)

# Include the client API router
app.include_router(client_api_router)


# Global exception handler for HTTP requests
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    error_id = f"err-{int(time.time())}"
    print(f"[ERROR {error_id}] Unhandled exception in {request.url.path}: {str(exc)}")
    print(traceback.format_exc())

    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error_id": error_id,
            "message": "An unexpected error occurred. Please try again later.",
            "path": request.url.path,
        },
    )


# Only check for API key in production mode, not during testing
if not OPENAI_API_KEY and "pytest" not in sys.modules:
    raise ValueError("Missing the OpenAI API key. Please set it in the .env file.")


@app.get("/health", response_class=JSONResponse)
async def health_check():
    """
    Health check endpoint for monitoring.
    """
    return {"status": "ok", "message": "Voice Assistant API is running"}


@app.get("/media-format", response_class=JSONResponse)
async def media_format():
    """
    Returns the current audio format configuration for diagnostic purposes.
    """
    session_settings = {
        "input_audio_format": "g711_ulaw",  # Fixed to ensure compatibility with Twilio
        "output_audio_format": "g711_ulaw",
        "voice": VOICE,
        "hostname": host,
        "websocket_url": f"wss://{host}/media-stream",
    }
    return session_settings


@app.get("/", response_class=JSONResponse)
async def index_page():
    """
    Returns a JSON response with server status and client statistics.
    """
    try:
        # Try to get client statistics
        if not client_data_manager.client:
            client_data_manager.connect()

        stats = client_data_manager.get_database_stats()
        client_count = len(client_data_manager.list_all_clients())

        return {
            "message": "Twilio Media Stream Server is running!",
            "version": "2.0 - Client-Centric Architecture",
            "database": {
                "name": DEFAULT_CLIENT_DB,
                "total_clients": client_count,
                "stats": stats,
            },
            "endpoints": {
                "health": "/health",
                "client_api": "/api/clients",
                "reports": "/reports",
                "media_stream": "/media-stream",
                "incoming_call": "/incoming-call",
            },
        }
    except Exception as e:
        return {
            "message": "Twilio Media Stream Server is running!",
            "version": "2.0 - Client-Centric Architecture",
            "database_status": f"Error: {str(e)}",
            "endpoints": {
                "health": "/health",
                "client_api": "/api/clients",
                "reports": "/reports",
                "media_stream": "/media-stream",
                "incoming_call": "/incoming-call",
            },
        }


@app.get("/connection-test", response_class=JSONResponse)
async def connection_test():
    """
    Test the connection to both OpenAI and Twilio to ensure everything is configured correctly.
    """
    results = {
        "status": "success",
        "timestamp": time.time(),
        "config": {
            "host": host,
            "port": PORT,
            "input_audio_format": "g711_ulaw",
            "output_audio_format": "g711_ulaw",
            "voice": VOICE,
        },
        "openai": {"status": "unknown"},
        "twilio": {"status": "unknown"},
        "mongodb": {"status": "unknown"},
    }

    # Test OpenAI API key validity
    try:
        # Check if API key exists and has valid format
        if not OPENAI_API_KEY:
            results["openai"] = {"status": "error", "message": "API key not configured"}
        elif len(OPENAI_API_KEY) < 20:  # Simple validation
            results["openai"] = {
                "status": "error",
                "message": "API key appears invalid (too short)",
            }
        else:
            # We can't easily test the API without making a real request,
            # but we can confirm it's at least formatted correctly
            results["openai"] = {
                "status": "configured",
                "message": "API key is configured",
            }
    except Exception as e:
        results["openai"] = {"status": "error", "message": str(e)}

    # Test Twilio setup
    try:
        # Check if we have credentials set in environment variables
        twilio_sid = os.getenv("TWILIO_ACCOUNT_SID")
        twilio_token = os.getenv("TWILIO_AUTH_TOKEN")

        if not twilio_sid or not twilio_token:
            results["twilio"] = {
                "status": "configured",
                "message": "Twilio SID/token not fully configured",
            }
        else:
            results["twilio"] = {
                "status": "configured",
                "message": "Twilio credentials are configured",
            }
    except Exception as e:
        results["twilio"] = {"status": "error", "message": str(e)}

    # Test MongoDB connection
    try:
        # Since we're already in an async context, don't use asyncio.run
        mongodb_status = False
        try:
            # Simple connection test without the async function
            mongo_client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=2000)
            server_info = mongo_client.server_info()
            mongo_client.close()
            mongodb_status = True
        except (ConnectionFailure, ServerSelectionTimeoutError) as mongo_err:
            results["mongodb"] = {
                "status": "error",
                "message": f"Connection error: {str(mongo_err)}",
            }

        if mongodb_status:
            results["mongodb"] = {
                "status": "connected",
                "uri": MONGO_URI[:20] + "...",
                "db": DEFAULT_CLIENT_DB,
            }
        else:
            if "mongodb" not in results:
                results["mongodb"] = {
                    "status": "error",
                    "message": "Failed to connect to MongoDB",
                }
    except Exception as e:
        results["mongodb"] = {"status": "error", "message": str(e)}

    return results


@app.api_route("/incoming-call", methods=["GET", "POST"])
async def handle_incoming_call(request: Request):
    """Handle incoming call and return TwiML response to connect to Media Stream."""
    loghook("Incoming call received in Twilio")

    # Get caller information if available
    form_data = await request.form() if hasattr(request, "form") else {}
    caller_id = form_data.get("From", "Unknown")
    call_sid = form_data.get("CallSid", "Unknown")

    # Log all available form data for debugging
    log.info(f"=== INCOMING CALL DEBUG ===")
    log.info(f"Caller ID: {caller_id}")
    log.info(f"Call SID: {call_sid}")
    log.info(f"Form data keys: {list(form_data.keys())}")
    log.info(f"Full form data: {dict(form_data)}")
    log.info(f"Request headers: {dict(request.headers)}")
    log.info(f"Request URL: {request.url}")
    log.info(f"Request method: {request.method}")

    loghook(f"CALL INITIATED: From {caller_id}, SID: {call_sid}")

    response = VoiceResponse()

    # Improved greeting with better pacing
    response.say("One moment please.")
    response.pause(length=0.5)

    # Get the actual hostname
    hostname = request.url.hostname or host
    log.info(f"WebSocket hostname: {hostname}")

    # Create the WebSocket connection with improved parameters
    connect = Connect()
    stream = connect.stream(url=f"wss://{hostname}/media-stream")

    # Add stream parameters for better audio quality
    stream.parameter(name="track", value="both_tracks")
    stream.parameter(name="statusCallback", value=f"https://{hostname}/stream-status")

    response.append(connect)

    twiml_response = str(response)
    log.info(f"Generated TwiML response: {twiml_response}")
    log.info(f"=== END INCOMING CALL DEBUG ===")

    return HTMLResponse(content=twiml_response, media_type="application/xml")


@app.api_route("/stream-status", methods=["POST"])
async def handle_stream_status(request: Request):
    """Handle stream status callbacks from Twilio for monitoring."""
    try:
        form_data = await request.form()
        stream_sid = form_data.get("StreamSid", "Unknown")
        status = form_data.get("Status", "Unknown")

        # Enhanced logging for stream status
        log.info(f"=== STREAM STATUS DEBUG ===")
        log.info(f"Stream SID: {stream_sid}")
        log.info(f"Status: {status}")
        log.info(f"All form data: {dict(form_data)}")
        log.info(f"Request headers: {dict(request.headers)}")
        log.info(f"Timestamp: {time.time()}")

        # Check for specific status values that indicate problems
        if status in ["stopped", "failed", "ended", "interrupted"]:
            log.error(f"🚨 STREAM ISSUE: Stream {stream_sid} status is {status}")
            loghook(f"🚨 STREAM PROBLEM: {stream_sid} - {status}")
        else:
            log.info(f"✅ Stream {stream_sid} status: {status}")
            loghook(f"Stream status update: {stream_sid} - {status}")

        log.info(f"=== END STREAM STATUS DEBUG ===")

        return JSONResponse({"status": "received"})
    except Exception as e:
        log.error(f"Error handling stream status: {e}")
        loghook(f"ERROR in stream status handler: {e}")
        return JSONResponse({"error": "Error processing status"}, status_code=500)


@app.websocket("/media-stream")
async def handle_media_stream(websocket: WebSocket):
    """Handle WebSocket connections between Twilio and OpenAI."""
    loghook("Media stream connection received")
    global response_lock
    import re

    log.info("=== WEBSOCKET CONNECTION DEBUG ===")
    log.info(f"WebSocket client info: {websocket.client}")
    log.info(f"WebSocket headers: {dict(websocket.headers)}")
    log.info(f"WebSocket scope: {websocket.scope.get('path', 'N/A')}")
    log.info("Client connected to media stream")

    await websocket.accept()
    log.info("✅ WebSocket connection accepted")

    # Create a list to track all tasks we create
    tasks = []
    openai_ws = None

    # Try to acquire the lock with a timeout
    try:
        log.info("🔒 Attempting to acquire response lock...")
        if not await asyncio.wait_for(response_lock.acquire(), timeout=5.0):
            log.error("❌ Could not acquire response lock in time")
            loghook("ERROR: Response lock timeout")
            await websocket.close()
            return
        log.info("✅ Response lock acquired successfully")
    except asyncio.TimeoutError:
        log.error("❌ Timeout acquiring response lock")
        loghook("ERROR: Response lock timeout")
        await websocket.close()
        return
    except Exception as e:
        log.error(f"❌ Error acquiring response lock: {e}")
        loghook(f"ERROR acquiring lock: {e}")
        await websocket.close()
        return

    try:
        # Connect to OpenAI with improved connection parameters
        log.info("🔌 Connecting to OpenAI WebSocket...")
        log.info(
            f"OpenAI URL: wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview"
        )
        log.info(f"API Key present: {bool(OPENAI_API_KEY)}")
        log.info(f"API Key length: {len(OPENAI_API_KEY) if OPENAI_API_KEY else 0}")

        # Connection parameters to improve stability
        openai_ws = await websockets.connect(
            "wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview",
            extra_headers={
                "Authorization": f"Bearer {OPENAI_API_KEY}",
                "OpenAI-Beta": "realtime=v1",
            },
            # Add connection parameters for stability
            ping_interval=30,  # Send ping every 30 seconds to keep connection alive
            ping_timeout=10,  # Wait 10 seconds for pong response
            close_timeout=10,  # Wait 10 seconds for close handshake
            max_size=16 * 1024 * 1024,  # 16MB max message size
            max_queue=32,  # Max queued messages
        )
        log.info("✅ Connected to OpenAI WebSocket successfully")
        log.info(f"OpenAI WebSocket state: {openai_ws.state}")
        loghook("CONNECTED TO OPENAI API!")

        # Initialize the session
        log.info("🎯 Initializing session with OpenAI...")
        loghook("Initializing session with OpenAI...")
        await initialize_session(openai_ws)
        log.info("✅ Session initialized successfully")
        loghook("Session initialized and conversation started")

    except websockets.exceptions.ConnectionClosedError as e:
        log.error(f"❌ OpenAI WebSocket connection closed during setup: {e}")
        loghook(f"WEBSOCKET CLOSED: {e}")
        if response_lock.locked():
            response_lock.release()
        await websocket.close()
        return
    except websockets.exceptions.InvalidStatusCode as e:
        log.error(f"❌ OpenAI WebSocket invalid status code: {e}")
        loghook(f"INVALID STATUS: {e}")
        if response_lock.locked():
            response_lock.release()
        await websocket.close()
        return
    except websockets.exceptions.ConnectionClosedOK as e:
        log.warning(f"⚠️ OpenAI WebSocket closed normally during setup: {e}")
        log.warning("This might be due to timing - trying to continue anyway...")
        loghook(f"WEBSOCKET CLOSED NORMALLY DURING SETUP: {e}")
        # Don't return here - let the connection continue
    except Exception as e:
        log.error(f"❌ Error during WebSocket setup: {e}")
        log.error(f"Exception type: {type(e).__name__}")
        log.error(f"Exception details: {str(e)}")
        loghook(f"ERROR FROM OPENAI API: {e}")
        if openai_ws and not openai_ws.closed:
            await openai_ws.close()
        if response_lock.locked():
            response_lock.release()
        await websocket.close()
        return

    # Check if OpenAI WebSocket is still connected after initialization
    if not openai_ws or openai_ws.closed:
        log.error("❌ OpenAI WebSocket not available after initialization")
        loghook("ERROR: OpenAI WebSocket not available after setup")
        if response_lock.locked():
            response_lock.release()
        await websocket.close()
        return

    # Connection specific state
    stream_sid = None
    latest_media_timestamp = 0
    last_assistant_item = None
    mark_queue = []
    response_start_timestamp_twilio = None

    # Silence detection variables
    call_start_time = time.time()
    last_speech_timestamp = time.time()
    silence_warning_sent = False
    silence_warning_count = 0
    silence_check_task = None

    # Goodbye detection variables
    goodbye_confirmation_sent = False

    # Transcription variables
    user_transcriptions = []
    assistant_transcriptions = []
    full_conversation_transcript = ""
    structured_conversation = []

    async def receive_from_twilio():
        """Receive audio data from Twilio and send it to the OpenAI Realtime API."""
        nonlocal stream_sid, latest_media_timestamp
        message_count = 0
        try:
            log.info("🎤 Starting to receive messages from Twilio...")
            async for message in websocket.iter_text():
                message_count += 1
                try:
                    data = json.loads(message)
                    event_type = data.get("event", "unknown")

                    # Log every 100th message to avoid spam, but always log important events
                    if message_count % 100 == 0 or event_type in [
                        "start",
                        "stop",
                        "mark",
                    ]:
                        log.info(f"📨 Twilio message #{message_count}: {event_type}")

                    if data["event"] == "media" and openai_ws.open:
                        latest_media_timestamp = int(data["media"]["timestamp"])
                        audio_append = {
                            "type": "input_audio_buffer.append",
                            "audio": data["media"]["payload"],
                        }
                        await openai_ws.send(json.dumps(audio_append))

                        # Log first few audio packets
                        if message_count <= 5:
                            log.info(
                                f"🔊 Audio packet sent to OpenAI: timestamp={latest_media_timestamp}"
                            )

                    elif data["event"] == "start":
                        stream_sid = data["start"]["streamSid"]
                        log.info(f"🚀 Incoming stream has started: {stream_sid}")
                        log.info(f"Start event data: {data}")
                        loghook(f"STREAM STARTED: {stream_sid}")
                        response_start_timestamp_twilio = None
                        latest_media_timestamp = 0
                        last_assistant_item = None

                    elif data["event"] == "stop":
                        log.info(f"🛑 Stream stopped: {stream_sid}")
                        log.info(f"Stop event data: {data}")
                        loghook(f"STREAM STOPPED: {stream_sid}")

                    elif data["event"] == "mark":
                        log.info(f"📍 Mark event received: {data}")
                        if mark_queue:
                            mark_queue.pop(0)

                    else:
                        log.info(f"❓ Unknown event type: {event_type}, data: {data}")

                except json.JSONDecodeError as e:
                    log.error(f"❌ Failed to parse Twilio message: {e}")
                    log.error(f"Raw message: {message}")

        except WebSocketDisconnect:
            log.info("📞 Client disconnected from Twilio WebSocket")
            loghook("TWILIO CLIENT DISCONNECTED")
        except Exception as e:
            log.error(f"❌ Error in receive_from_twilio: {e}")
            log.error(f"Message count: {message_count}")
            loghook(f"ERROR receiving from Twilio: {e}")
        finally:
            log.info(
                f"🔄 Cleaning up receive_from_twilio (processed {message_count} messages)"
            )
            # Signal that we should clean up
            if openai_ws and openai_ws.open:
                try:
                    await openai_ws.close()
                    log.info("✅ OpenAI WebSocket closed from receive_from_twilio")
                except Exception as e:
                    log.error(f"❌ Error closing OpenAI WebSocket: {e}")

    async def send_to_twilio():
        """Receive events from the OpenAI Realtime API, send audio back to Twilio."""
        nonlocal \
            stream_sid, \
            last_assistant_item, \
            response_start_timestamp_twilio, \
            last_speech_timestamp, \
            silence_warning_sent, \
            silence_warning_count, \
            silence_check_task, \
            goodbye_confirmation_sent, \
            user_transcriptions, \
            assistant_transcriptions, \
            full_conversation_transcript, \
            structured_conversation, \
            call_start_time

        openai_message_count = 0
        try:
            log.info("🤖 Starting to receive messages from OpenAI...")
            async for openai_message in openai_ws:
                openai_message_count += 1

                # Ensure we properly parse the message as JSON
                try:
                    response = json.loads(openai_message)
                except json.JSONDecodeError as e:
                    log.error(f"❌ Error decoding JSON from OpenAI: {e}")
                    log.error(f"Raw OpenAI message: {openai_message}")
                    loghook(f"JSON DECODE ERROR from OpenAI: {e}")
                    continue

                # Make sure response is a dictionary before proceeding
                if not isinstance(response, dict):
                    log.error(
                        f"❌ Unexpected response format (not a dict): {type(response)}"
                    )
                    log.error(f"Response content: {response}")
                    loghook(f"INVALID RESPONSE FORMAT from OpenAI: {type(response)}")
                    continue

                event_type = response.get("type", "unknown")

                # Enhanced logging for all OpenAI events
                if event_type in LOG_EVENT_TYPES:
                    log.info(f"🔔 OpenAI Event #{openai_message_count}: {event_type}")
                    if event_type == "error":
                        log.error(f"🚨 OpenAI Error Event: {response}")
                        loghook(f"OPENAI ERROR: {response}")
                elif event_type in ["session.created", "session.updated"]:
                    log.info(f"⚙️ Session Event #{openai_message_count}: {event_type}")
                    log.info(f"Session details: {response}")
                elif event_type.startswith("conversation."):
                    log.info(
                        f"💬 Conversation Event #{openai_message_count}: {event_type}"
                    )
                elif event_type.startswith("response."):
                    log.info(f"📤 Response Event #{openai_message_count}: {event_type}")
                elif event_type.startswith("input_audio_buffer."):
                    log.info(
                        f"🎧 Audio Buffer Event #{openai_message_count}: {event_type}"
                    )
                else:
                    log.info(
                        f"❓ Unknown OpenAI Event #{openai_message_count}: {event_type}"
                    )

                # Log every 50th message to track activity
                if openai_message_count % 50 == 0:
                    log.info(f"📊 OpenAI message count: {openai_message_count}")
                    loghook(f"OpenAI messages processed: {openai_message_count}")

                # Handle speech stopped event
                if response.get("type") == SPEECH_STOPPED_EVENT:
                    log.info("Speech stopped detected, aggregating transcriptions")
                    await aggregate_transcriptions()
                    # Reset the last speech timestamp
                    last_speech_timestamp = time.time()

                # Handle transcript done event
                elif response.get("type") == AUDIO_TRANSCRIPT_DONE_EVENT:
                    log.info(
                        "Transcript done event received, finalizing transcript segment"
                    )
                    await aggregate_transcriptions()

                # Check for transcription in response
                if (
                    "transcript" in str(response)
                    or response.get("type") == AUDIO_TRANSCRIPT_DELTA_EVENT
                ):
                    # Extract transcript from various response types
                    transcript = None
                    is_assistant_transcript = False

                    # Handle different transcript event types
                    if (
                        response.get("type") == RESPONSE_DONE_EVENT
                        and isinstance(response.get("response"), dict)
                        and response.get("response", {}).get("output")
                    ):
                        for item in response["response"]["output"]:
                            if isinstance(item, dict) and "content" in item:
                                for content in item.get("content", []):
                                    if (
                                        isinstance(content, dict)
                                        and content.get("type") == "audio"
                                        and content.get("transcript")
                                    ):
                                        transcript = content["transcript"]
                                        is_assistant_transcript = True
                    elif response.get(
                        "type"
                    ) == "input_audio_buffer.committed" and response.get("transcript"):
                        transcript = response["transcript"]
                        is_assistant_transcript = False
                    elif (
                        response.get("type") == AUDIO_TRANSCRIPT_DELTA_EVENT
                        and isinstance(response.get("delta"), dict)
                        and response.get("delta", {}).get("text")
                    ):
                        transcript = response["delta"]["text"]
                        is_assistant_transcript = False
                    # Detect assistant message from response type
                    elif (
                        response.get("type") == "response.audio.delta"
                        and response.get("item_id")
                        and isinstance(response.get("data"), dict)
                        and response.get("data", {}).get("transcript")
                    ):
                        transcript = response["data"]["transcript"]
                        is_assistant_transcript = True

                    # Process transcript if found
                    if transcript:
                        log.info(
                            f"Transcript detected: {transcript} (Assistant: {is_assistant_transcript})"
                        )

                        if is_assistant_transcript:
                            assistant_transcriptions.append(transcript)
                        else:
                            user_transcriptions.append(transcript)
                            # Check for goodbye phrases if not already in confirmation mode
                            if not goodbye_confirmation_sent:
                                await check_for_goodbye_phrases(transcript)

                # Handle speech stopped event
                if response.get("type") == "input_audio_buffer.speech_stopped":
                    log.info("Speech stopped detected.")
                    # Start silence check task
                    if silence_check_task:
                        silence_check_task.cancel()
                    silence_check_task = asyncio.create_task(check_silence())

                # Handle speech started event to reset silence detection
                if response.get("type") == "input_audio_buffer.speech_started":
                    log.info("Speech started detected.")
                    last_speech_timestamp = time.time()
                    silence_warning_sent = False

                    # Cancel the silence check task if it's running
                    if silence_check_task:
                        silence_check_task.cancel()
                        silence_check_task = None

                    # Immediately truncate the current response when user starts speaking
                    if last_assistant_item:
                        log.info(
                            f"Interrupting response with id: {last_assistant_item}"
                        )
                        await handle_speech_started_event()

                # Handle audio from OpenAI
                if (
                    response.get("type") == "response.audio.delta"
                    and "delta" in response
                ):
                    audio_payload = base64.b64encode(
                        base64.b64decode(response["delta"])
                    ).decode("utf-8")
                    audio_delta = {
                        "event": "media",
                        "streamSid": stream_sid,
                        "media": {"payload": audio_payload},
                    }
                    await websocket.send_json(audio_delta)

                    if isinstance(response.get("data"), dict) and response.get(
                        "data", {}
                    ).get("transcript"):
                        assistant_transcript = response["data"]["transcript"]
                        assistant_transcriptions.append(assistant_transcript)
                        log.info(
                            f"Assistant transcript from audio delta: {assistant_transcript}"
                        )

                    if response_start_timestamp_twilio is None:
                        response_start_timestamp_twilio = latest_media_timestamp
                        if SHOW_TIMING_MATH:
                            log.info(
                                f"Setting start timestamp for new response: {response_start_timestamp_twilio}ms"
                            )

                    if response.get("item_id"):
                        last_assistant_item = response["item_id"]

                    await send_mark(websocket, stream_sid)

        except websockets.exceptions.ConnectionClosedError as e:
            log.error(f"📞 OpenAI WebSocket connection closed in send_to_twilio: {e}")
            loghook(f"OPENAI CONNECTION CLOSED: {e}")
        except websockets.exceptions.ConnectionClosedOK as e:
            log.info(f"✅ OpenAI WebSocket closed normally: {e}")
            loghook("OPENAI CONNECTION CLOSED NORMALLY")
        except Exception as e:
            log.error(f"❌ Error in send_to_twilio: {e}")
            log.error(f"Exception type: {type(e).__name__}")
            log.error(f"OpenAI message count: {openai_message_count}")
            loghook(f"ERROR in send_to_twilio: {e}")
        finally:
            log.info(
                f"🔄 Cleaning up send_to_twilio (processed {openai_message_count} OpenAI messages)"
            )

    async def check_silence():
        """Check for prolonged silence and take appropriate action using enhanced VAD detection."""
        nonlocal silence_warning_sent, silence_warning_count
        try:
            await asyncio.sleep(15)  # Reduced from 20s for better responsiveness

            current_time = time.time()
            silence_duration = current_time - last_speech_timestamp

            log.info(
                f"Silence check - Duration: {silence_duration:.2f}s, Last speech: {datetime.fromtimestamp(last_speech_timestamp).strftime('%H:%M:%S')}"
            )

            if not silence_warning_sent and silence_duration >= 15:  # Reduced threshold
                log.info(
                    f"Prolonged silence detected ({silence_duration:.2f}s). Sending polite prompt to user."
                )
                silence_warning_sent = True
                silence_warning_count += 1

                await send_silence_warning()

                await asyncio.sleep(10)  # Give more time for response

                current_time = time.time()
                updated_silence = current_time - last_speech_timestamp

                if updated_silence >= 25:  # 15s initial + 10s after warning
                    log.info(
                        f"No response after silence warning. Total silence: {updated_silence:.2f}s. Hanging up the call."
                    )
                    await hang_up_call(
                        "I'll end our call for now since I haven't heard from you. Please feel free to call back when you're ready to continue the inspection. Have a great day!"
                    )
                else:
                    log.info("User responded after silence warning. Continuing call.")
                    silence_warning_sent = False

            elif (
                silence_warning_count > 0 and silence_duration >= 12
            ):  # Reduced threshold for repeat warnings
                log.info(
                    f"Repeated prolonged silence ({silence_duration:.2f}s) after previous warnings. Hanging up."
                )
                await hang_up_call(
                    "I'll end our call now to free up the line. You can call back anytime when you're ready to continue. Thank you!"
                )

        except asyncio.CancelledError:
            log.info("Silence check cancelled - speech detected.")
        except Exception as e:
            log.error(f"Error in check_silence: {e}")

    async def check_for_goodbye_phrases(transcript):
        """Check if the transcript contains farewell phrases."""
        nonlocal goodbye_confirmation_sent
        try:
            transcript_lower = transcript.lower()

            for phrase in FAREWELL_PHRASES:
                if re.search(r"\b" + re.escape(phrase) + r"\b", transcript_lower):
                    log.info(f"Farewell phrase detected: '{phrase}'")

                    if not goodbye_confirmation_sent:
                        goodbye_confirmation_sent = True
                        await send_goodbye_confirmation()
                    return
        except Exception as e:
            log.error(f"Error in check_for_goodbye_phrases: {e}")

    async def send_goodbye_confirmation():
        """Send a confirmation message when a farewell phrase is detected."""
        try:
            confirm_text = "It sounds like you want to end our call. Is that correct? Please say yes if you'd like to hang up now."

            assistant_transcriptions.append(confirm_text)

            confirmation_message = {
                "type": "conversation.item.create",
                "item": {
                    "type": "message",
                    "role": "assistant",
                    "content": [{"type": "text", "text": confirm_text}],
                },
            }
            await openai_ws.send(json.dumps(confirmation_message))
            await openai_ws.send(json.dumps({"type": "response.create"}))

            await aggregate_transcriptions()

            asyncio.create_task(listen_for_goodbye_confirmation())
        except Exception as e:
            log.error(f"Error sending goodbye confirmation: {e}")

    async def listen_for_goodbye_confirmation():
        """Listen for confirmation to end the call after detecting a farewell phrase."""
        nonlocal goodbye_confirmation_sent
        try:
            # await asyncio.sleep(10)

            if user_transcriptions:
                recent_transcript = user_transcriptions[-1].lower()

                confirmation_phrases = [
                    "yes",
                    "correct",
                    "that's right",
                    "yeah",
                    "yep",
                    "sure",
                    "please do",
                ]
                for phrase in confirmation_phrases:
                    if re.search(r"\b" + re.escape(phrase) + r"\b", recent_transcript):
                        log.info(f"User confirmed goodbye with: '{phrase}'")
                        await hang_up_call(
                            "Thank you for your time today. It was a pleasure assisting you. Goodbye!"
                        )
                        return

                log.info("User did not confirm goodbye. Continuing the call.")
                goodbye_confirmation_sent = False
        except Exception as e:
            log.error(f"Error in listen_for_goodbye_confirmation: {e}")

    async def send_silence_warning():
        """Send a polite prompt to the user during prolonged silence."""
        nonlocal silence_warning_count
        try:
            if silence_warning_count == 0:
                silence_text = "I'm still here and ready to continue. Are you still with me? Just let me know when you're ready to proceed with the inspection."
            else:
                silence_text = "I want to make sure we don't lose our connection. Are you still there? I'm ready to continue whenever you are."

            assistant_transcriptions.append(silence_text)

            silence_message = {
                "type": "conversation.item.create",
                "item": {
                    "type": "message",
                    "role": "assistant",
                    "content": [{"type": "text", "text": silence_text}],
                },
            }
            await openai_ws.send(json.dumps(silence_message))
            await openai_ws.send(json.dumps({"type": "response.create"}))

            await aggregate_transcriptions()

            silence_warning_count += 1
            log.info(f"Sent polite silence prompt #{silence_warning_count}")
        except Exception as e:
            log.error(f"Error sending silence warning: {e}")

    async def aggregate_transcriptions():
        """Aggregate all transcriptions into a structured conversation format."""
        nonlocal \
            user_transcriptions, \
            assistant_transcriptions, \
            full_conversation_transcript, \
            structured_conversation

        if user_transcriptions:
            current_user_segment = " ".join(user_transcriptions)

            structured_conversation.append(
                {"role": "user", "content": current_user_segment}
            )

            if full_conversation_transcript:
                full_conversation_transcript += "\nUser: " + current_user_segment
            else:
                full_conversation_transcript = "User: " + current_user_segment

            user_transcriptions = []

            log.info(f"Aggregated user transcription segment: {current_user_segment}")

        if assistant_transcriptions:
            current_assistant_segment = " ".join(assistant_transcriptions)

            structured_conversation.append(
                {"role": "assistant", "content": current_assistant_segment}
            )

            full_conversation_transcript += "\nAssistant: " + current_assistant_segment

            assistant_transcriptions = []

            log.info(
                f"Aggregated assistant transcription segment: {current_assistant_segment}"
            )

    async def upload_transcript():
        """Save the full conversation transcript to MongoDB using client-centric approach."""
        nonlocal full_conversation_transcript, structured_conversation

        await aggregate_transcriptions()

        if not full_conversation_transcript:
            log.info("No transcript to save.")
            return

        try:
            log.info(
                f"Saving transcript to MongoDB (length: {len(full_conversation_transcript)})"
            )

            current_time = datetime.now()
            timestamp_str = current_time.strftime("%Y%m%d_%H%M%S")
            call_identifier = f"{stream_sid}_{timestamp_str}"

            # Extract client email from structured conversation
            client_email = None
            client_name = "Unknown Customer"
            vehicle_info = "Unknown Vehicle"
            work_order_number = None

            for item in structured_conversation:
                content = item.get("content", "").lower()

                # Look for email in the conversation
                if "email" in content and "@" in content:
                    import re

                    email_pattern = (
                        r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"
                    )
                    emails = re.findall(email_pattern, content, re.IGNORECASE)
                    if emails:
                        client_email = emails[0].lower()

                # Extract other info
                if "customer" in content and "name" in content and ":" in content:
                    parts = content.split(":")
                    if len(parts) > 1:
                        client_name = parts[1].strip().title()

                if "work order" in content and ":" in content:
                    parts = content.split(":")
                    if len(parts) > 1:
                        work_order_number = parts[1].strip()

                if (
                    "vehicle" in content
                    and ("make" in content or "model" in content)
                    and ":" in content
                ):
                    parts = content.split(":")
                    if len(parts) > 1:
                        vehicle_info = parts[1].strip().title()

            # Prepare transcript document
            transcript_document = {
                "transcript": full_conversation_transcript,
                "structured_conversation": structured_conversation,
                "call_id": stream_sid,
                "timestamp": time.time(),
                "date_time": current_time,
                "formatted_time": timestamp_str,
                "call_identifier": call_identifier,
                "client_name": client_name,
                "work_order_number": work_order_number,
                "vehicle_info": vehicle_info,
                "metadata": {
                    "duration": time.time() - call_start_time,
                    "silence_warnings_sent": silence_warning_count,
                    "goodbye_detected": goodbye_confirmation_sent,
                    "transcript_segments": len(structured_conversation),
                },
            }

            mongodb_saved = False

            # Try to save using client data manager if email is found
            if client_email:
                try:
                    if (
                        not client_data_manager.client
                    ):  # Connect if not already connected
                        client_data_manager.connect()

                    # Add transcript to client's collection
                    mongodb_saved = client_data_manager.add_transcript(
                        client_email, transcript_document
                    )

                    if mongodb_saved:
                        log.info(
                            f"Transcript saved to client collection for {client_email}"
                        )
                        log.info(f"Call Identifier: {call_identifier}")
                    else:
                        log.error(
                            f"Failed to save transcript to client collection for {client_email}"
                        )

                except Exception as e:
                    log.error(f"Error saving to client collection: {e}")
                    mongodb_saved = False

            # Fallback to legacy approach if client email not found or client manager failed
            if not mongodb_saved:
                try:
                    collection_name = (
                        f"{MONGO_COLLECTION}_{current_time.strftime('%Y%m')}"
                    )
                    mongo_client = MongoClient(MONGO_URI)
                    db = mongo_client[
                        DEFAULT_CLIENT_DB if client_email else "voice_assistant"
                    ]  # Use legacy DB if no email
                    collection = db[collection_name]

                    result = collection.insert_one(transcript_document)
                    mongodb_saved = True

                    log.info(
                        f"Transcript saved to legacy collection. Document ID: {result.inserted_id}"
                    )
                    log.info(
                        f"Collection: {collection_name}, Call Identifier: {call_identifier}"
                    )

                    mongo_client.close()
                except Exception as e:
                    log.error(f"Error saving to legacy MongoDB: {e}")

            # Local file fallback
            if not mongodb_saved:
                try:
                    with open(
                        f"transcripts/transcript_{call_identifier}.json", "w"
                    ) as f:
                        json.dump(transcript_document, f, indent=2)
                    log.info(
                        f"Fallback: Saved transcript locally as transcript_{call_identifier}.json"
                    )
                except Exception as file_error:
                    log.error(f"Error saving transcript locally: {file_error}")

            try:
                log.info(
                    "Extracting structured inspection data using OpenAI function calling..."
                )
                await extract_and_send_inspection_data(
                    full_conversation_transcript, structured_conversation
                )

                log.info("Generating inspection report from transcript...")
                try:
                    from scripts.inspection_report import (
                        generate_inspection_report_from_transcript,
                    )
                except ImportError:
                    log.error("Could not import inspection report module")
                    return

                customer_name = "Unknown Customer"
                vehicle_info = "Unknown Vehicle"

                for item in structured_conversation:
                    content = item.get("content", "").lower()
                    if "customer" in content and "name" in content and ":" in content:
                        parts = content.split(":")
                        if len(parts) > 1:
                            customer_name = parts[1].strip().title()
                    if (
                        "vehicle" in content
                        and ("make" in content or "model" in content)
                        and ":" in content
                    ):
                        parts = content.split(":")
                        if len(parts) > 1:
                            vehicle_info = parts[1].strip().title()

                report_path = await generate_inspection_report_from_transcript(
                    transcript_content=full_conversation_transcript,
                    call_identifier=call_identifier,
                )
                log.info(f"Inspection report generated successfully: {report_path}")

                try:
                    try:
                        from scripts.report_ui import store_pdf_in_mongodb
                    except ImportError:
                        log.error("Could not import report UI module")
                        return

                    mongodb_file_id = store_pdf_in_mongodb(
                        pdf_path=report_path,
                        call_id=call_identifier,
                        customer_name=customer_name,
                        vehicle_info=vehicle_info,
                    )

                    if mongodb_file_id:
                        log.info(
                            f"PDF stored in MongoDB GridFS with ID: {mongodb_file_id}"
                        )

                        # Update client collection with report info if using client manager
                        if mongodb_saved and client_email:
                            try:
                                # Add inspection data to client collection
                                inspection_data = {
                                    "call_identifier": call_identifier,
                                    "report_path": report_path,
                                    "report_gridfs_id": mongodb_file_id,
                                    "customer_name": client_name,
                                    "vehicle_info": vehicle_info,
                                    "work_order_number": work_order_number,
                                    "vehicle_type": "Unknown",  # Will be extracted from transcript
                                    "inspector_name": "AI Assistant",
                                    "inspection_date_vehicle": current_time.strftime(
                                        "%Y-%m-%d"
                                    ),
                                }

                                client_data_manager.add_inspection(
                                    client_email, inspection_data
                                )
                                log.info(
                                    f"Added inspection record to client collection for {client_email}"
                                )
                            except Exception as update_error:
                                log.error(
                                    f"Error adding inspection to client collection: {update_error}"
                                )

                        # Legacy update for fallback collections
                        elif mongodb_saved:
                            try:
                                collection_name = f"{MONGO_COLLECTION}_{current_time.strftime('%Y%m')}"
                                mongo_client = MongoClient(MONGO_URI)
                                db = mongo_client[
                                    DEFAULT_CLIENT_DB
                                    if client_email
                                    else "voice_assistant"
                                ]
                                collection = db[collection_name]

                                collection.update_one(
                                    {"call_identifier": call_identifier},
                                    {
                                        "$set": {
                                            "report_path": report_path,
                                            "report_gridfs_id": mongodb_file_id,
                                            "customer_name": client_name,
                                            "vehicle_info": vehicle_info,
                                        }
                                    },
                                )

                                mongo_client.close()
                                log.info(
                                    "Updated legacy MongoDB document with report information"
                                )
                            except Exception as update_error:
                                log.error(
                                    f"Error updating legacy MongoDB document with report info: {update_error}"
                                )
                    else:
                        log.error("Failed to store PDF in MongoDB GridFS")
                except Exception as gridfs_error:
                    log.error(f"Error storing PDF in MongoDB GridFS: {gridfs_error}")

                    if mongodb_saved and client_email:
                        try:
                            # Update client collection with local report path
                            inspection_data = {
                                "call_identifier": call_identifier,
                                "report_path": report_path,
                                "customer_name": client_name,
                                "vehicle_info": vehicle_info,
                                "work_order_number": work_order_number,
                            }
                            client_data_manager.add_inspection(
                                client_email, inspection_data
                            )
                            log.info(
                                f"Updated client collection with local report path: {report_path}"
                            )
                        except Exception as update_error:
                            log.error(
                                f"Error updating client collection with report path: {update_error}"
                            )
                    elif mongodb_saved:
                        try:
                            # Legacy update
                            collection_name = (
                                f"{MONGO_COLLECTION}_{current_time.strftime('%Y%m')}"
                            )
                            mongo_client = MongoClient(MONGO_URI)
                            db = mongo_client[
                                DEFAULT_CLIENT_DB if client_email else "voice_assistant"
                            ]
                            collection = db[collection_name]

                            collection.update_one(
                                {"call_identifier": call_identifier},
                                {"$set": {"report_path": report_path}},
                            )

                            mongo_client.close()
                            log.info(
                                f"Updated legacy MongoDB document with local report path: {report_path}"
                            )
                        except Exception as update_error:
                            log.error(
                                f"Error updating legacy MongoDB document with report path: {update_error}"
                            )

            except Exception as report_error:
                log.error(f"Error generating inspection report: {report_error}")

        except Exception as e:
            log.error(f"Error in upload_transcript: {e}")

    async def hang_up_call(message="Thank you for your time today. Goodbye!"):
        """Hang up the call with a custom message."""
        try:
            assistant_transcriptions.append(message)

            hangup_message = {
                "type": "conversation.item.create",
                "item": {
                    "type": "message",
                    "role": "assistant",
                    "content": [{"type": "text", "text": message}],
                },
            }
            # await openai_ws.send(json.dumps(hangup_message))
            # await openai_ws.send(json.dumps({"type": "response.create"}))

            await aggregate_transcriptions()

            await asyncio.sleep(2)

            await upload_transcript()

            if stream_sid:
                hangup_response = VoiceResponse()
                hangup_response.hangup()

                await websocket.send_json(
                    {
                        "event": "twiml",
                        "streamSid": stream_sid,
                        "twiml": str(hangup_response),
                    }
                )

                log.info(f"Hangup command sent for stream {stream_sid}")
        except Exception as e:
            log.error(f"Error hanging up call: {e}")

    async def handle_speech_started_event():
        """Handle interruption when the caller's speech starts."""
        nonlocal response_start_timestamp_twilio, last_assistant_item
        log.info("Handling speech started event.")
        if mark_queue and response_start_timestamp_twilio is not None:
            elapsed_time = latest_media_timestamp - response_start_timestamp_twilio
            if SHOW_TIMING_MATH:
                log.info(
                    f"Calculating elapsed time for truncation: {latest_media_timestamp} - {response_start_timestamp_twilio} = {elapsed_time}ms"
                )

            if last_assistant_item:
                if SHOW_TIMING_MATH:
                    log.info(
                        f"Truncating item with ID: {last_assistant_item}, Truncated at: {elapsed_time}ms"
                    )

                truncate_event = {
                    "type": "conversation.item.truncate",
                    "item_id": last_assistant_item,
                    "content_index": 0,
                    "audio_end_ms": elapsed_time,
                }
                await openai_ws.send(json.dumps(truncate_event))

            await websocket.send_json({"event": "clear", "streamSid": stream_sid})

            mark_queue.clear()
            last_assistant_item = None
            response_start_timestamp_twilio = None

    async def send_mark(connection, stream_sid):
        if stream_sid:
            mark_event = {
                "event": "mark",
                "streamSid": stream_sid,
                "mark": {"name": "responsePart"},
            }
            await connection.send_json(mark_event)
            mark_queue.append("responsePart")

    receive_task = asyncio.create_task(receive_from_twilio())
    send_task = asyncio.create_task(send_to_twilio())
    tasks.extend([receive_task, send_task])

    done, pending = await asyncio.wait(tasks, return_when=asyncio.FIRST_COMPLETED)
    log.info(
        f"🏁 WebSocket tasks completed. Done: {len(done)}, Pending: {len(pending)}"
    )

    for task in pending:
        task.cancel()
        log.info(
            f"🚫 Cancelled pending task: {task.get_name() if hasattr(task, 'get_name') else 'unnamed'}"
        )

    log.info("🧹 Cleaning up tasks and WebSocket connections...")
    for task in pending:
        if not task.done():
            task.cancel()
            log.info(f"🚫 Force cancelled task: {task}")

    try:
        if openai_ws and not openai_ws.closed:
            await openai_ws.close()
            log.info("✅ OpenAI WebSocket closed successfully")
            loghook("OpenAI WebSocket connection closed")
    except Exception as e:
        log.error(f"❌ Error closing OpenAI WebSocket: {e}")
        loghook(f"ERROR closing OpenAI WebSocket: {e}")

    try:
        await websocket.close()
        log.info("✅ Client WebSocket closed successfully")
        loghook("Twilio WebSocket connection closed")
    except Exception as e:
        log.error(f"❌ Error closing client WebSocket: {e}")
        loghook(f"ERROR closing Twilio WebSocket: {e}")

    if response_lock.locked():
        response_lock.release()
        log.info("🔓 Response lock released")
        loghook("Response lock released")

    log.info("=== END WEBSOCKET SESSION DEBUG ===")


async def send_initial_conversation_item(openai_ws):
    """Send initial conversation item to start the inspection conversation."""
    try:
        log.info("Sending initial conversation item to start inspection")
        initial_conversation_item = {
            "type": "conversation.item.create",
            "item": {
                "type": "message",
                "role": "user",
                "content": [
                    {
                        "type": "input_text",
                        "text": "Please start the vehicle inspection conversation.",
                    }
                ],
            },
        }
        await openai_ws.send(json.dumps(initial_conversation_item))
        await openai_ws.send(json.dumps({"type": "response.create"}))
        log.info("Initial conversation item sent")
    except Exception as e:
        log.error(f"Error sending initial conversation item: {e}")
        raise


async def initialize_session(openai_ws):
    """Control initial session with OpenAI."""
    try:
        log.info("Starting session initialization with OpenAI")
        log.info("Setting up session parameters")
        # First, set up the session with the correct parameters
        session_update = {
            "type": "session.update",
            "session": {
                "input_audio_format": "g711_ulaw",
                "output_audio_format": "g711_ulaw",
                "voice": VOICE,
                "instructions": SYSTEM_MESSAGE,
                "modalities": ["text", "audio"],
                "temperature": 0.8,  # Optimal for audio models per documentation
                "max_response_output_tokens": 150,  # Limit response length for better pacing
                "turn_detection": {
                    "type": "server_vad",
                    "threshold": 0.5,  # Default recommended threshold
                    "prefix_padding_ms": 300,  # Capture context before speech
                    "silence_duration_ms": 500,  # Balanced pause duration
                    "create_response": True,  # Automatically create response when speech ends
                },
                # Enable input audio noise reduction for better VAD performance
                "input_audio_noise_reduction": {
                    "type": "near_field"  # Good for phone calls
                },
                # Enable input audio transcription for debugging
                "input_audio_transcription": {"model": "whisper-1"},
            },
        }
        log.info("📤 Sending session update to OpenAI...")
        print("Sending session update:", json.dumps(session_update, indent=2))
        await openai_ws.send(json.dumps(session_update))

        log.info("✅ Session update sent")
        loghook("Session update sent to OpenAI")

        # Wait for session confirmation before proceeding
        log.info("⏳ Waiting for session to be established...")

        # Wait for session.created event instead of arbitrary sleep
        session_established = False
        max_wait_time = 10  # seconds
        start_time = time.time()

        while not session_established and (time.time() - start_time) < max_wait_time:
            try:
                # Check for incoming messages from OpenAI
                message = await asyncio.wait_for(openai_ws.recv(), timeout=1.0)
                response = json.loads(message)
                if response.get("type") == "session.created":
                    log.info("✅ Session created event received")
                    session_established = True
                    break
                elif response.get("type") == "session.updated":
                    log.info("✅ Session updated event received")
                    session_established = True
                    break
            except asyncio.TimeoutError:
                log.info("⏳ Still waiting for session confirmation...")
                continue
            except Exception as e:
                log.error(f"Error waiting for session: {e}")
                break

        if not session_established:
            log.warning("⚠️ Session not confirmed, proceeding anyway...")

        log.info("🎯 Creating assistant's initial greeting")
        # Create the assistant's initial greeting to start the conversation immediately
        assistant_greeting = {
            "type": "conversation.item.create",
            "item": {
                "type": "message",
                "role": "assistant",
                "content": [
                    {
                        "type": "text",
                        "text": "Hello! Thank you for calling our vehicle inspection service. I'm your AI assistant and I'll be helping you document this inspection today. Before we begin, do you prefer to conduct this inspection in English or Spanish?",
                    }
                ],
            },
        }
        log.info("📤 Sending assistant greeting...")
        await openai_ws.send(json.dumps(assistant_greeting))

        # Wait a bit longer for the greeting to be processed
        await asyncio.sleep(0.5)

        # Trigger the assistant to speak the greeting
        response_create = {"type": "response.create"}
        log.info("🚀 Triggering assistant response...")
        await openai_ws.send(json.dumps(response_create))

        # Give more time for the response to start
        await asyncio.sleep(0.5)

        log.info("✅ Assistant greeting initiated")
        loghook("Assistant greeting sent and response triggered")

    except websockets.exceptions.ConnectionClosedOK as e:
        log.warning(f"⚠️ OpenAI WebSocket closed normally during session init: {e}")
        log.warning("Session initialization may have completed despite closure")
        # Don't raise - allow the caller to handle this
    except Exception as e:
        log.error(f"❌ Error in initialize_session: {e}")
        log.error(f"Exception type: {type(e).__name__}")
        raise


async def extract_and_send_inspection_data(
    transcript: str, structured_conversation: List[Dict]
) -> bool:
    """Extract structured inspection data using OpenAI function calling and send to API."""
    try:
        log.info("Calling OpenAI API to extract structured inspection data...")

        # Prepare the conversation for function calling
        messages = [
            {
                "role": "system",
                "content": "You are an expert at extracting structured inspection data from vehicle inspection transcripts. Extract all relevant information according to the provided schema.",
            },
            {
                "role": "user",
                "content": f"Please extract structured inspection data from this transcript:\n\n{transcript}\n\nStructured conversation:\n{json.dumps(structured_conversation, indent=2)}",
            },
        ]

        # Make the API call with function calling
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                "https://api.openai.com/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {OPENAI_API_KEY}",
                    "Content-Type": "application/json",
                },
                json={
                    "model": "gpt-4o",
                    "messages": messages,
                    "functions": [INSPECTION_EXTRACTION_SCHEMA],
                    "function_call": {"name": "extract_inspection_data"},
                    "temperature": 0.1,
                },
            )

            if response.status_code != 200:
                log.error(f"OpenAI API error: {response.status_code} - {response.text}")
                return False

            api_response = response.json()

            # Extract the function call result
            if "choices" in api_response and len(api_response["choices"]) > 0:
                choice = api_response["choices"][0]
                if "message" in choice and "function_call" in choice["message"]:
                    function_call = choice["message"]["function_call"]
                    if function_call["name"] == "extract_inspection_data":
                        try:
                            extracted_data = json.loads(function_call["arguments"])
                            log.info(
                                "Successfully extracted inspection data from transcript"
                            )

                            # Send to the API endpoint
                            return await send_inspection_data_to_api(extracted_data)
                        except json.JSONDecodeError as e:
                            log.error(f"Error parsing function call arguments: {e}")
                            return False
                else:
                    log.error("No function call found in OpenAI response")
                    return False
            else:
                log.error("Invalid response structure from OpenAI API")
                return False

    except Exception as e:
        log.error(f"Error extracting inspection data: {e}")
        return False


async def send_inspection_data_to_api(inspection_data: Dict) -> bool:
    """Send extracted inspection data to dev.aiready.io/new-report/ endpoint."""
    try:
        log.info("Sending inspection data to dev.aiready.io/new-report/...")

        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                "https://dev.aiready.io/new-report/",
                headers={"Content-Type": "application/json"},
                json=inspection_data,
            )

            if response.status_code in [200, 201]:
                log.info(
                    f"Successfully sent inspection data to API. Response: {response.status_code}"
                )
                log.info(f"API Response: {response.text}")
                return True
            else:
                log.error(
                    f"API endpoint error: {response.status_code} - {response.text}"
                )
                return False

    except Exception as e:
        log.error(f"Error sending inspection data to API: {e}")
        return False


async def test_mongodb_connection():
    """Test MongoDB connection on startup."""
    try:
        # Create MongoDB client
        mongo_client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=5000)

        # Test connection by getting server info
        server_info = mongo_client.server_info()
        print(f"Connected to MongoDB server version: {server_info.get('version')}")
        print(
            f"Using database: {DEFAULT_CLIENT_DB}, collection base: {MONGO_COLLECTION}"
        )

        # Close the connection
        mongo_client.close()
        return True
    except Exception as e:
        print(f"Warning: MongoDB connection failed: {e}")
        print("Transcripts will fallback to local file storage only")
        return False


# Function calling schema for structuring inspection data
INSPECTION_EXTRACTION_SCHEMA = {
    "type": "function",
    "name": "extract_inspection_data",
    "description": "Extract structured inspection data from transcript based on regulatory requirements",
    "parameters": {
        "type": "object",
        "properties": {
            "report_title": {
                "type": "string",
                "description": "Title of the inspection report",
            },
            "vehicle_type": {
                "type": "string",
                "description": "Type of vehicle inspected (Light-Duty, Power-Unit/Tractor, Trailer, Liftgate/Forklift, Hy-Rail)",
                "enum": [
                    "Light-Duty",
                    "Power-Unit/Tractor",
                    "Trailer",
                    "Liftgate/Forklift",
                    "Hy-Rail",
                ],
            },
            "regulatory_standard": {
                "type": "string",
                "description": "Applicable regulatory standard (State Safety/Emissions, FMCSA 49 CFR 396.17/396.21, OSHA 29 CFR 1910.178, FRA 49 CFR 214.523)",
            },
            "client_name": {"type": "string", "description": "Client's name"},
            "client_email": {"type": "string", "description": "Client's email address"},
            "work_order_number": {
                "type": "string",
                "description": "Work order or unit number",
            },
            "vehicle_info_title": {"type": "string", "default": "Vehicle Information"},
            "checklist_title": {"type": "string", "default": "Inspection Checklist"},
            "signoff_title": {"type": "string", "default": "Inspector Sign-Off"},
            "vehicle_owner": {
                "type": "string",
                "description": "Owner/company name of the vehicle",
            },
            "odometer_reading": {
                "type": "string",
                "description": "Current odometer reading",
            },
            "vin": {"type": "string", "description": "Vehicle Identification Number"},
            "engine_hours": {
                "type": "string",
                "description": "Engine hours if available",
            },
            "make_model_year": {
                "type": "string",
                "description": "Vehicle make, model and year",
            },
            "inspection_date_vehicle": {
                "type": "string",
                "description": "Date of inspection (YYYY-MM-DD format)",
            },
            "license_plate": {"type": "string", "description": "License plate number"},
            "service_level": {
                "type": "string",
                "description": "Service level (Level A, B, C, etc.)",
            },
            "checklist_items": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "category": {
                            "type": "string",
                            "description": "Inspection category (e.g., Cab & Body, Electrical & Lighting, etc.)",
                        },
                        "item": {
                            "type": "string",
                            "description": "Inspection item name",
                        },
                        "status": {
                            "type": "string",
                            "description": "Status (OK, FAIL, N/A, etc.)",
                        },
                        "notes": {
                            "type": "string",
                            "description": "Additional notes or observations",
                        },
                        "regulatory_requirement": {
                            "type": "string",
                            "description": "Specific regulatory requirement if applicable",
                        },
                    },
                    "required": ["category", "item", "status", "notes"],
                },
            },
            "inspector_name": {
                "type": "string",
                "description": "Name of the inspector",
            },
            "signature_url": {
                "type": "string",
                "default": "https://via.placeholder.com/105x30?text=Signature",
            },
            "signoff_date": {
                "type": "string",
                "description": "Date of sign-off (YYYY-MM-DD format)",
            },
            "signoff_location": {
                "type": "string",
                "description": "Location where inspection was performed",
            },
            "download_button_text": {"type": "string", "default": "Download PDF"},
            "footer_line1": {
                "type": "string",
                "default": "Report generated by WFS Inspection System.",
            },
            "footer_support_email": {
                "type": "string",
                "default": "<EMAIL>",
            },
        },
        "required": [
            "report_title",
            "vehicle_type",
            "regulatory_standard",
            "client_name",
            "client_email",
            "work_order_number",
            "vehicle_owner",
            "make_model_year",
            "inspector_name",
            "inspection_date_vehicle",
            "checklist_items",
        ],
    },
}

if __name__ == "__main__":
    import uvicorn

    # Start the FastAPI application
    uvicorn.run(app, host="0.0.0.0", port=PORT)
