# Voice Assistant for Vehicle Inspections

[![Tests](https://github.com/aiready-org/voice-stream-api/actions/workflows/main.yml/badge.svg)](https://github.com/aiready-org/voice-stream-api/actions/workflows/main.yml)
[![codecov](https://codecov.io/gh/aiready-org/voice-stream-api/branch/main/graph/badge.svg)](https://codecov.io/gh/aiready-org/voice-stream-api)
[![Python](https://img.shields.io/badge/python-3.13-blue.svg)](https://www.python.org/downloads/release/python-3132/)

A comprehensive AI-powered voice assistant system for conducting vehicle inspections over phone calls. The system integrates FastAPI, Twilio Voice API, OpenAI Realtime API, MongoDB, and generates professional PDF inspection reports with a modern web dashboard.

## System Architecture

```
┌─────────────────┐    WebRTC/Media    ┌─────────────────┐
│    Phone Call   │◄─────────────────►│   Twilio Voice  │
│   (Inspector)   │                   │      API        │
└─────────────────┘                   └─────────┬───────┘
                                                │
                                     WebSocket  │
                                                ▼
┌─────────────────┐    Realtime API   ┌─────────────────┐
│   OpenAI GPT    │◄─────────────────►│ Voice Assistant │
│ Realtime Audio  │                   │FastAPI (Port 8000)│
└─────────────────┘                   └─────────┬───────┘
         ▲                                      │
         │ Function Calling                     │ Save Data
         │ (Extract Data)                       ▼
         └─────────────────────────────┌─────────────────┐
                                       │   MongoDB       │
                                       │   Database      │
                                       └─────────┬───────┘
                                                │ Read Data
                                                ▼
┌─────────────────┐    HTTP/API       ┌─────────────────┐
│   PDF Reports   │◄─────────────────►│ Reports Dashboard│
│   Generation    │                   │FastAPI (Port 5003)│
│  (Puppeteer)    │                   │  Tailwind CSS   │
└─────────────────┘                   └─────────────────┘
```

## Features

### 🎯 **Core Voice Assistant Capabilities**
- **Real-time Voice Conversations**: Conducts structured vehicle inspections over phone calls
- **AI-Powered Dialogue Management**: Introduces itself as "Workforce AI Assistant" and follows inspection protocols
- **Intelligent Call Control**: Detects silence, manages call duration (30-min max), and graceful hangups
- **Multi-language Support**: Asks for language preference (English/Spanish) at call start

### 🔍 **Vehicle Inspection Process** 
- **Structured Question Flow**: Follows regulatory inspection standards for different vehicle types
- **Vehicle Type Detection**: Automatically adapts questions based on vehicle category:
  - Light-Duty Vehicle (under 10,000 lbs)
  - Power-Unit/Tractor  
  - Trailer
  - Liftgate/Forklift equipment
  - Hy-Rail/Heavy-Duty truck
  - General DOT Commercial Vehicle
- **Dynamic Inspection Items**: Covers safety-critical components based on vehicle type

### 🧠 **AI Data Extraction**
- **OpenAI Function Calling**: Extracts structured inspection data from natural conversation
- **Intelligent Parsing**: Converts conversational responses into standardized inspection results  
- **Status Classification**: Maps technician responses to status categories (OK, GOOD, FAIR, POOR, REPAIR)
- **Comprehensive Coverage**: Captures every inspection item discussed during the call

### 💾 **Data Management**
- **MongoDB Integration**: Stores transcripts and inspection data with client-centric architecture
- **Dual Database Strategy**: 
  - Main database for transcripts and client data
  - Reports database for dashboard integration
- **GridFS Storage**: Handles large PDF files efficiently
- **Automatic Timestamps**: Tracks call timing and report generation

### 📊 **Reports Dashboard**
- **Real-time Updates**: Shows new inspections immediately after call completion
- **Color-coded Status**: Visual status indicators (🟢 OK, 🟡 Fair/Good, 🔴 Critical/Repair)
- **Professional PDF Generation**: Creates formatted inspection reports with Puppeteer
- **Modern Web Interface**: Responsive Tailwind CSS design with statistics and filtering
- **Live Polling**: Auto-refreshes every 5 seconds to show new reports

## System Workflow

### 📞 **Complete Inspection Flow**

```mermaid
graph TD
    A[📞 Phone Call Initiated] --> B[🤖 AI Introduces as Workforce AI Assistant]
    B --> C[🌍 Language Selection: English/Spanish]
    C --> D[📋 Collect Basic Information]
    D --> E[🚗 Vehicle Type Identification] 
    E --> F[📝 Dynamic Inspection Questions]
    F --> G[💬 Natural Conversation]
    G --> H[🎯 AI Extracts Structured Data]
    H --> I[💾 Save to MongoDB]
    I --> J[📊 Real-time Dashboard Update]
    J --> K[📄 PDF Report Generation]
    
    D --> D1[Unit Number]
    D --> D2[Make & Model] 
    D --> D3[Client Name]
    D --> D4[Client Email]
    D --> D5[Odometer Reading]
    
    E --> E1[Light-Duty Vehicle]
    E --> E2[Power-Unit/Tractor]
    E --> E3[Trailer]
    E --> E4[Liftgate/Forklift]
    E --> E5[Hy-Rail/Heavy-Duty]
    
    F --> F1[Safety Components]
    F --> F2[Regulatory Items]
    F --> F3[Vehicle-Specific Checks]
```

### 🔄 **Step-by-Step Process**

1. **📞 Call Initiation**
   - Inspector calls the Twilio number
   - Twilio forwards call to Voice Assistant via WebSocket
   - OpenAI Realtime API handles voice processing

2. **🤖 AI Introduction & Setup**
   ```
   "Workforce AI Assistant. English or Spanish?"
   → Collect: Unit Number, Make/Model, Client Info, Odometer
   → Identify vehicle type for appropriate inspection branch
   ```

3. **🔍 Dynamic Inspection Process**
   - AI asks questions specific to vehicle type
   - Inspector provides verbal responses about component conditions
   - Natural conversation flow with one question at a time
   - AI adapts based on responses and maintains inspection protocol

4. **🧠 Real-time Data Processing**
   - Every question-answer pair captured in structured conversation
   - OpenAI function calling extracts inspection data:
     ```json
     {
       "vehicle_owner": "John Smith",
       "unit_number": "UNIT-12345", 
       "car_type": "Light-Duty Vehicle",
       "checklist_items": [
         {"item": "Brakes", "status": "OK", "notes": "Good condition"},
         {"item": "Tires", "status": "FAIR", "notes": "Some wear visible"}
       ],
       "report_generation_datetime": "2025-06-18 14:30:25"
     }
     ```

5. **💾 Dual Database Storage**
   - **Main Database**: Complete transcript and client data
   - **Reports Database**: Structured inspection data for dashboard
   - Both databases updated simultaneously for data integrity

6. **📊 Immediate Dashboard Updates**
   - New inspection appears in real-time dashboard
   - Color-coded status indicators applied automatically
   - Statistics updated (total reports, daily/weekly counts)

7. **📄 Professional Report Generation**
   - PDF created with company branding and formatting
   - Color-coded checklist items for easy scanning
   - Digital signature placeholder replaced with "Inspector" text
   - Professional layout with vehicle information and inspection details

### 🎯 **Data Flow Architecture**

```
[Phone Call] 
    ↓ (WebRTC/Media Stream)
[Twilio Voice API]
    ↓ (WebSocket)
[Voice Assistant - main.py]
    ↓ (Real-time Audio)
[OpenAI Realtime API]
    ↓ (Function Calling)
[Data Extraction]
    ↓ (Structured JSON)
[MongoDB Storage] → [Reports Database]
    ↓ (HTTP/API)           ↓ (Real-time Polling)
[External API]         [Dashboard UI]
    (Optional)              ↓ (PDF Generation)
                        [Puppeteer Reports]
```

### 🔧 **Environment Integration**

- **Development**: Local MongoDB + Reports Dashboard
- **Production**: Scalable MongoDB + External API integration
- **Hybrid**: Local reports + External API backup
- **Configurable**: Environment variables control data flow

## Setup and Run

### Quick Start (Recommended)

1. **Clone and Setup Environment**
```bash
git clone <repository>
cd voice-assistant
cp .env.example .env  # Edit with your API keys
```

2. **Start the Complete System**
```bash
# Start voice assistant
python main.py  # Port 8000

# In another terminal, start reports dashboard
cd reports/
make setup      # Complete dashboard setup
# Or manually: docker compose up -d && make init-db
```

3. **Access Applications**
- **Voice Assistant**: http://localhost:8000
- **Reports Dashboard**: http://localhost:5003  
- **API Documentation**: http://localhost:8000/docs

### Development Setup

#### Voice Assistant (Main Application)
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
pip install -e .  # Development mode

# Configure environment
cp .env.example .env
# Edit .env with your OpenAI API key and MongoDB settings

python main.py
```

#### Reports Dashboard  
```bash
cd reports/

# Using Makefile (easiest)
make setup                    # Complete setup
make start                   # Start services  
make init-db                 # Initialize with sample data
make open                    # Open in browser

# Or manual setup
cp .env.example .env         # Configure environment
docker compose up -d         # Start MongoDB + Dashboard
docker compose exec reports-app npm run init-db  # Sample data
```

### Docker Deployment

```bash
# Voice Assistant
docker build -t voice-assistant .
docker run -p 8000:8000 --env-file .env voice-assistant

# Reports Dashboard (from reports/ directory)
cd reports/
docker compose up -d
```

### System URLs

| Service | URL | Description |
|---------|-----|-------------|
| Voice Assistant | http://localhost:8000 | Main FastAPI application |
| Voice Assistant Docs | http://localhost:8000/docs | API documentation |
| Reports Dashboard | http://localhost:5003 | Modern inspection reports UI |
| Reports Health Check | http://localhost:5003/health | Dashboard status |
| MongoDB | mongodb://localhost:27018 | Database (reports) |

## Environment Configuration

### Voice Assistant (.env)
```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Server Configuration  
PORT=8000
HOST=0.0.0.0

# Main MongoDB Configuration (for transcripts and client data)
MONGO_URI=****************************************
DEFAULT_CLIENT_DB=Client_Activities
MONGO_COLLECTION=voice-api
MONGODB_ENABLED=true

# Reports Database Configuration (for dashboard integration)
REPORTS_MONGO_URI=*******************************************************************************
REPORTS_DB_NAME=inspection_reports

# External API Configuration (optional - set to "disabled" to use only local database)
REPORT_APP_URI=disabled

# Slack Integration (optional)
SLACK_WEBHOOK_URL=your_slack_webhook_url_here

# Feature Flags
ENABLE_MONGODB_LOGGING=false
```

### Reports Dashboard (reports/.env)
```bash
# Server Configuration
PORT=5003
NODE_ENV=development

# MongoDB Configuration (connects to same database as voice assistant writes to)
MONGODB_URI=*******************************************************************************

# Advanced Configuration
DEBUG_MODE=false
ENABLE_REQUEST_LOGGING=true
CORS_ORIGINS=*
PUPPETEER_HEADLESS=true
ENABLE_SAMPLE_DATA=true
```

### Environment Setup Examples

#### Development (Local)
```bash
# Voice Assistant
OPENAI_API_KEY=sk-...
PORT=8000
MONGO_URI=mongodb://localhost:27017
REPORTS_MONGO_URI=*******************************************************************************
REPORT_APP_URI=disabled

# Reports Dashboard  
PORT=5003
MONGODB_URI=*******************************************************************************
DEBUG_MODE=true
```

#### Production
```bash
# Voice Assistant
OPENAI_API_KEY=sk-...
MONGO_URI=mongodb+srv://user:<EMAIL>/production
REPORTS_MONGO_URI=mongodb+srv://user:<EMAIL>/reports
REPORT_APP_URI=https://api.example.com/inspections

# Reports Dashboard
MONGODB_URI=mongodb+srv://user:<EMAIL>/reports
NODE_ENV=production
DEBUG_MODE=false
CORS_ORIGINS=https://yourdomain.com
```

## Reports Dashboard

The reports dashboard provides a modern web interface for viewing and managing vehicle inspection reports generated by the voice assistant.

### Features

- **📊 Real-time Dashboard**: Auto-refreshing every 5 seconds
- **🎨 Color-coded Status**: Visual indicators for inspection results
  - 🟢 **Green**: OK status (perfect condition)
  - 🟡 **Yellow**: GOOD/FAIR status (non-critical issues)  
  - 🔴 **Red**: POOR/REPAIR/REPLACE status (critical issues)
  - ⚫ **Gray**: N/A status (not applicable)
- **📄 PDF Generation**: Professional reports with company branding
- **📈 Statistics**: Total reports, daily/weekly/monthly counts
- **🔍 Filtering & Search**: Find specific inspections quickly
- **📱 Responsive Design**: Works on desktop and mobile devices

### Dashboard Management

```bash
cd reports/

# Essential commands
make help          # Show all available commands
make setup         # Complete setup with one command
make start         # Start dashboard services
make stop          # Stop all services
make restart       # Restart services
make status        # Show service status

# Development commands
make dev-docker    # Start with debugging enabled
make logs-follow   # Follow logs in real-time
make health        # Check application health

# Database commands
make init-db       # Initialize with sample data
make reset-db      # Reset database completely
make backup-db     # Backup database
make mongo-shell   # Access MongoDB directly

# Testing commands  
make test-api      # Test API endpoints
make test-pdf      # Test PDF generation
make troubleshoot  # Run diagnostic checks
```

### API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/` | Dashboard homepage |
| `GET` | `/api/inspections` | List all inspections (paginated) |
| `GET` | `/api/inspections/:id` | Get specific inspection |
| `GET` | `/api/inspections/:id/pdf` | Generate and download PDF |
| `GET` | `/health` | Health check |

### Integration with Voice Assistant

The voice assistant automatically saves inspection data to the reports database:

1. **Call Completion**: Voice assistant extracts structured data
2. **Local Save**: Data saved to `inspection_reports.inspections` collection  
3. **Real-time Update**: Dashboard immediately shows new inspection
4. **PDF Ready**: Click "View PDF" to generate professional report

### Data Structure

Inspection reports follow this standardized structure:
```json
{
  "_id": "ObjectId",
  "report_title": "Vehicle Inspection Report",
  "vehicle_owner": "John Smith", 
  "client_email": "<EMAIL>",
  "unit_number": "UNIT-12345",
  "car_type": "Light-Duty Vehicle",
  "make_model_year": "Ford F-150 / 2020",
  "odometer_reading": "75,450 mi",
  "inspection_date_vehicle": "2025-06-18",
  "report_generation_datetime": "2025-06-18 14:30:25",
  "checklist_items": [
    {
      "item": "Brakes",
      "status": "OK", 
      "notes": "Good condition, no issues found"
    }
  ],
  "inspector_name": "Workforce AI Assistant",
  "service_level": "Standard"
}
```

## Testing

The project includes comprehensive test suites for all major features. First, install the test dependencies:

```bash
pip install -r requirements-test.txt
```

Then run the tests:

```bash
# Run all tests
python -m pytest

# Run specific test suites
python -m pytest tests/test_ai_hangup.py
python -m pytest tests/test_transcription.py
python -m pytest tests/test_functional_integration.py

# Run tests with coverage report
python -m pytest --cov=. --cov-config=.coveragerc tests/
```

### Testing MongoDB Connection

A test script is provided to verify MongoDB connectivity:

```bash
python test_mongodb.py
```

### Test Structure

- **test_ai_hangup.py**: Tests for silence detection, farewell phrase detection, and call hangup.
- **test_transcription.py**: Tests for transcription aggregation and event handling.
- **test_functional_integration.py**: Integration tests combining multiple features.

## GitHub Actions

The project uses GitHub Actions for continuous integration. The workflow is defined in `.github/workflows/main.yml` and includes:

1. Runs linting with flake8
2. Executes test suites in separate steps:
   - AI-Initiated Hangup tests
   - Conversation Transcription tests
   - Functional Integration tests
3. Runs all tests together
4. Generates coverage reports and uploads them to Codecov

The workflow is triggered on pushes to the `main` branch and on pull requests to any branch.

## Ngrok Ingress

Setup ngrok and run for example `ngrok http --url=sharp-only-chimp.ngrok-free.app PORT` where `PORT` is uvicorn's port.

## Twilio Dev Phone

[Dev-Phone](https://www.twilio.com/docs/labs/dev-phone)

```
Current number: ****** 254 4652
```

## MongoDB Transcript Storage

The voice assistant saves conversation transcripts to MongoDB automatically. Each transcript is stored with rich metadata, making it easy to search and retrieve later.

### Key Features

- **Automatic Storage**: Transcripts are automatically saved to MongoDB when calls end
- **Monthly Collections**: Transcripts are organized in monthly collections (`transcripts_YYYYMM`)
- **Unique Identifiers**: Each transcript has a unique `call_identifier` combining the stream SID and timestamp
- **Rich Metadata**: Includes call duration, silence warnings, and other useful analytics
- **Fallback Mechanism**: If MongoDB connection fails, transcripts are saved locally to the `transcripts/` directory

### Using the MongoDB Integration

1. Start the MongoDB container and voice assistant using Docker:
   ```bash
   make docker-start
   ```

2. Test the MongoDB connection:
   ```bash
   make test-mongodb
   ```

3. For development purposes, you can rebuild and restart the containers:
   ```bash
   make docker-rebuild
   ```

4. To view stored transcripts, connect to MongoDB (with MongoDB Compass or similar tool):
   - Host: localhost:27017
   - Username: admin
   - Password: password
   - Database: voice_assistant
   - Collections: transcripts_YYYYMM (where YYYY is year, MM is month)

## Vehicle Inspection Reports System

The system automatically processes voice transcripts from vehicle inspections and generates professional PDF reports.

### Key Features

- **AI-Powered Data Extraction**: Uses OpenAI function calling to extract structured data from transcripts
- **PDF Generation**: Creates professional, branded PDF reports optimized for size (<16MB)
- **Modern Web UI**: Stylish dashboard for viewing and downloading reports
- **MongoDB Integration**: Stores reports in GridFS for efficient retrieval
- **Batch Processing**: Support for processing multiple transcripts at once

### Using the Reports System

1. Start the complete system using Docker Compose:
   ```bash
   docker-compose up -d
   ```

2. Access the Reports UI dashboard:
   ```
   http://localhost:10001/
   ```

3. Generate a report from a transcript:
   ```bash
   # Generate from a specific transcript ID
   make generate-report TRANSCRIPT_ID=your_transcript_id
   
   # Or interactively select from available transcripts
   make generate-report
   ```

4. Process multiple reports in batch:
   ```bash
   # Process the 10 most recent transcripts
   make process-batch-reports LIMIT=10
   ```

5. For development, install the report dependencies:
   ```bash
   make install-report-deps
   ```

6. Run the reports UI standalone:
   ```bash
   make run-reports-ui
   ```

### Reports System Architecture

The reports system consists of the following components:

1. **Transcript Processor**: Extracts structured data using OpenAI function calling
2. **Template Renderer**: Formats data into HTML using Jinja2 templates
3. **PDF Generator**: Converts HTML to optimized PDF files
4. **Reports UI**: Web interface for viewing and downloading reports
5. **MongoDB GridFS**: Storage system for PDF files

All components are containerized and can be deployed together using Docker Compose.

### Environment Configuration

MongoDB connection details and other settings can be configured in your `.env` file:
```
MONGO_URI=**************************************
MONGO_DB=voice_assistant 
MONGO_COLLECTION=transcripts
OPENAI_API_KEY=your_openai_api_key_here
REPORTS_PORT=10001
```