#!/usr/bin/env python3
"""
Test script to debug the inspection data extraction function.
"""

import asyncio
import json
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path so we can import from main.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import extract_and_send_inspection_data

# Sample inspection conversation for testing
SAMPLE_CONVERSATION = [
    {"role": "assistant", "content": "Workforce AI Assistant."},
    {"role": "assistant", "content": "English or Spanish?"},
    {"role": "user", "content": "English"},
    {"role": "assistant", "content": "What is the unit number?"},
    {"role": "user", "content": "UNIT-12345"},
    {"role": "assistant", "content": "What is the maker and model?"},
    {"role": "user", "content": "Ford F-150"},
    {"role": "assistant", "content": "What is the client's name?"},
    {"role": "user", "content": "<PERSON>"},
    {"role": "assistant", "content": "What is the client's email address?"},
    {"role": "user", "content": "<EMAIL>"},
    {"role": "assistant", "content": "What does the odometer reading say?"},
    {"role": "user", "content": "85,000 miles"},
    {"role": "assistant", "content": "We will be doing a Light-Duty Vehicle inspection"},
    {"role": "assistant", "content": "Beginning inspection protocol. Door locks?"},
    {"role": "user", "content": "Door locks are working fine"},
    {"role": "assistant", "content": "Door latches?"},
    {"role": "user", "content": "Good condition"},
    {"role": "assistant", "content": "Windshield condition?"},
    {"role": "user", "content": "No cracks, clear visibility"},
    {"role": "assistant", "content": "Brakes?"},
    {"role": "user", "content": "Brakes are in excellent condition"},
    {"role": "assistant", "content": "Engine oil?"},
    {"role": "user", "content": "Oil level is good, clean oil"},
    {"role": "assistant", "content": "Thank you, we are done here."}
]

SAMPLE_TRANSCRIPT = """
Assistant: Workforce AI Assistant.
Assistant: English or Spanish?
User: English
Assistant: What is the unit number?
User: UNIT-12345
Assistant: What is the maker and model?
User: Ford F-150
Assistant: What is the client's name?
User: John Smith
Assistant: What is the client's email address?
User: <EMAIL>
Assistant: What does the odometer reading say?
User: 85,000 miles
Assistant: We will be doing a Light-Duty Vehicle inspection
Assistant: Beginning inspection protocol. Door locks?
User: Door locks are working fine
Assistant: Door latches?
User: Good condition
Assistant: Windshield condition?
User: No cracks, clear visibility
Assistant: Brakes?
User: Brakes are in excellent condition
Assistant: Engine oil?
User: Oil level is good, clean oil
Assistant: Thank you, we are done here.
"""

async def test_extraction():
    """Test the extraction function with sample data."""
    print("🧪 Testing inspection data extraction...")
    print(f"📋 Sample conversation has {len(SAMPLE_CONVERSATION)} items")
    print(f"📄 Sample transcript has {len(SAMPLE_TRANSCRIPT)} characters")
    
    try:
        # Test the extraction function
        result = await extract_and_send_inspection_data(
            SAMPLE_TRANSCRIPT, 
            SAMPLE_CONVERSATION
        )
        
        print(f"\n✅ Extraction completed with result: {result}")
        
        if result:
            print("🎉 SUCCESS: Inspection data was extracted and processed!")
        else:
            print("❌ FAILED: Inspection data extraction failed")
            
    except Exception as e:
        print(f"💥 ERROR during extraction: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Check if OpenAI API key is available
    openai_key = os.getenv("OPENAI_API_KEY")
    if not openai_key:
        print("❌ ERROR: OPENAI_API_KEY not found in environment variables")
        print("Please set your OpenAI API key in the .env file")
        sys.exit(1)
    
    print(f"🔑 OpenAI API key found (length: {len(openai_key)})")
    
    # Run the test
    asyncio.run(test_extraction())
