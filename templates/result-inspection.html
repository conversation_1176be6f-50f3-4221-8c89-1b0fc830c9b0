<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Vehicle Inspection Report</title>
<style>
    :root {
        --primary: #102a43;
        --secondary: #243b53;
        --accent: #486581;
        --bg-gradient: linear-gradient(120deg, #f0f4f8 0%, #d9e2ec 100%);
        --success: #2cb67d;
        --warning: #ff8906;
        --danger: #e63946;
        --white: #fff;
        --gray: #bfc9d1;
        --light: #f0f4f8;
        --font-main: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    }
    body {
        background: var(--bg-gradient);
        font-family: var(--font-main);
        margin: 0;
        padding: 0;
        color: var(--secondary);
    }
    .container {
        background: var(--white);
        max-width: 950px;
        margin: 40px auto;
        box-shadow: 0 6px 40px 0 rgba(32, 56, 88, 0.20);
        border-radius: 20px;
        padding: 38px 44px 28px 44px;
    }
    .logo {
        font-weight: bold;
        color: var(--accent);
        font-size: 1.3rem;
        margin-bottom: 14px;
    }
    .title {
        font-weight: 900;
        font-size: 2.1rem;
        color: var(--primary);
        letter-spacing: -1px;
        margin-bottom: 28px;
    }
    .section-title {
        margin: 36px 0 20px;
        font-size: 1.18rem;
        color: var(--primary);
        border-left: 5px solid var(--accent);
        padding-left: 12px;
        letter-spacing: 1px;
        font-weight: 700;
    }
    .info-table, .checklist-table, .signoff-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 8px;
    }
    .info-table th, .checklist-table th, .signoff-table th {
        text-align: left;
        color: var(--accent);
        font-size: 1rem;
        font-weight: 600;
        padding-right: 16px;
    }
    .info-table td, .checklist-table td, .signoff-table td {
        font-size: 1rem;
        padding: 6px 12px 6px 0;
        color: var(--secondary);
        vertical-align: top;
    }
    .checklist-table {
        margin-bottom: 16px;
        border-radius: 12px;
        overflow: hidden;
        background: var(--light);
    }
    .checklist-table th,
    .checklist-table td {
        padding: 10px 12px;
    }
    .checklist-table tr {
        background: var(--white);
        transition: background 0.15s;
    }
    .checklist-table tr:hover {
        background: #edf2fb;
    }
    .checklist-table .status-ok {
        color: var(--success);
        font-weight: bold;
    }
    .checklist-table .status-changed {
        color: var(--accent);
        font-weight: bold;
    }
    .checklist-table .status-na {
        color: var(--gray);
        font-style: italic;
    }
    .checklist-table .status-repair {
        color: var(--danger);
        font-weight: bold;
        letter-spacing: 1px;
    }
    .signoff-table td, .signoff-table th {
        padding-top: 12px;
        padding-bottom: 12px;
    }
    .button {
        display: inline-block;
        background: var(--accent);
        color: var(--white);
        padding: 7px 22px;
        border-radius: 6px;
        text-decoration: none;
        font-weight: 700;
        font-size: 1rem;
        box-shadow: 0 2px 6px 0 rgba(72, 101, 129, 0.12);
        margin-top: 10px;
        transition: background 0.2s;
    }
    .button:hover { background: var(--primary);}
    .footer {
        text-align: right;
        font-size: 0.9rem;
        color: var(--gray);
        margin-top: 26px;
    }
    /* Responsive */
    @media (max-width: 700px) {
        .container {padding: 12px;}
        .title {font-size: 1.25rem;}
    }
</style>
</head>
<body>
<div class="container">
    <div class="logo">WFS</div>
    <div class="title">Vehicle Inspection Report</div>

    <div class="section-title">Vehicle Information</div>
    <table class="info-table">
        <tr><th>Owner</th><td>BNSF Railway Co.</td><th>Odometer Reading</th><td>45,678 mi</td></tr>
        <tr><th>VIN</th><td>1FD0X5HT6KED12345</td><th>Engine Hours</th><td>1,250 hrs</td></tr>
        <tr><th>Make / Model / Year</th><td>Ford F-550 / 2019</td><th>Inspection Date</th><td>2025-04-16</td></tr>
        <tr><th>License Plate</th><td>CA - ABC1234</td><th>Service Level</th><td>Level B</td></tr>
    </table>

    <div class="section-title">Inspection Checklist</div>
    <table class="checklist-table">
        <tr><th>Item</th><th>Status</th><th>Notes</th></tr>
        <tr>
            <td>Brakes</td>
            <td class="status-ok">OK</td>
            <td>Functional</td>
        </tr>
        <tr>
            <td>Engine Oil</td>
            <td class="status-changed">Changed</td>
            <td>Service completed</td>
        </tr>
        <tr>
            <td>Lights</td>
            <td class="status-ok">OK</td>
            <td>All operational</td>
        </tr>
        <tr>
            <td>Seat Belts</td>
            <td class="status-ok">OK</td>
            <td>Good condition</td>
        </tr>
        <tr>
            <td>HVAC</td>
            <td class="status-repair">Needs Repair</td>
            <td>AC not cooling</td>
        </tr>
        <tr>
            <td>Hyrail Gear</td>
            <td class="status-na">N/A</td>
            <td>Not applicable to this vehicle</td>
        </tr>
        <tr>
            <td>Liftgate</td>
            <td class="status-ok">OK</td>
            <td>Hydraulics functioning, greased</td>
        </tr>
        <tr>
            <td>Suspension</td>
            <td class="status-ok">OK</td>
            <td>No damage, air system intact</td>
        </tr>
        <tr>
            <td>Fuel System</td>
            <td class="status-ok">OK</td>
            <td>No leaks</td>
        </tr>
        <tr>
            <td>Wheels & Tires</td>
            <td class="status-ok">OK</td>
            <td>All lug nuts torqued</td>
        </tr>
        <tr>
            <td>Battery/Charging</td>
            <td class="status-ok">OK</td>
            <td>Voltage normal</td>
        </tr>
    </table>

    <div class="section-title">Inspector Sign-Off</div>
    <table class="signoff-table">
        <tr>
            <th>Inspector Name</th>
            <td>John Doe</td>
            <th>Signature</th>
            <td></td>
        </tr>
        <tr>
            <th>Date</th>
            <td>2025-04-16</td>
            <th>Location</th>
            <td>Denver, CO</td>
        </tr>
    </table>
    <div class="footer">
        Report generated by AIREADY Inspection System.
    </div>
</div>
</body>
</html>
