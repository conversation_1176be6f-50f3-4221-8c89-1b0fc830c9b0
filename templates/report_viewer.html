<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>AIREADY Inspection Report Viewer</title>
<link href="https://fonts.googleapis.com/css?family=Montserrat:400,600,700,900&display=swap" rel="stylesheet">
<style>
    :root {
        --primary-blue: #0f3460;
        --secondary-blue: #1a4b8c;
        --accent-blue: #1b5ba2;
        --highlight: #2a81e0;
        --text-light: #f0f4f8;
        --white: #fff;
        --dark-text: #102a43;
        --gray-100: #f8f9fa;
        --gray-200: #e9ecef;
        --gray-300: #dee2e6;
        --gray-400: #ced4da;
        --gray-500: #adb5bd;
        --gray-600: #6c757d;
        --shadow-sm: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
        --shadow-md: 0 4px 6px rgba(0,0,0,0.1), 0 1px 3px rgba(0,0,0,0.08);
        --shadow-lg: 0 10px 25px rgba(0,0,0,0.1), 0 5px 10px rgba(0,0,0,0.05);
        --font-main: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        --border-radius: 8px;
    }
    body {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
        font-family: var(--font-main);
        margin: 0;
        padding: 0;
        color: var(--dark-text);
        min-height: 100vh;
    }
    .container {
        background: var(--white);
        max-width: 1100px;
        margin: 40px auto;
        box-shadow: var(--shadow-lg);
        border-radius: var(--border-radius);
        padding: 2rem;
    }
    .header {
        text-align: center;
        margin-bottom: 1.5rem;
    }
    .logo {
        font-weight: 700;
        font-size: 1.5rem;
        color: var(--accent-blue);
        margin-bottom: 0.5rem;
        letter-spacing: 1px;
    }
    .title {
        font-weight: 900;
        font-size: 2.2rem;
        color: var(--primary-blue);
        margin-top: 0.5rem;
        margin-bottom: 1rem;
    }
    .action-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }
    .button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background: var(--accent-blue);
        color: var(--white);
        padding: 0.6rem 1.25rem;
        border-radius: var(--border-radius);
        text-decoration: none;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.2s ease;
        box-shadow: var(--shadow-sm);
    }
    .button:hover {
        background: var(--primary-blue);
        box-shadow: var(--shadow-md);
        transform: translateY(-2px);
    }
    .button svg {
        margin-right: 0.5rem;
    }
    .pdf-container {
        width: 100%;
        height: 800px;
        border: 1px solid var(--gray-300);
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }
    .iframe-wrapper {
        width: 100%;
        height: 100%;
        position: relative;
    }
    .iframe-wrapper iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: none;
    }
    .spinner {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        justify-content: center;
        align-items: center;
    }
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
    .spinner::before {
        content: "";
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 4px solid var(--gray-300);
        border-top-color: var(--accent-blue);
        animation: spin 1s linear infinite;
    }
    .footer {
        text-align: center;
        font-size: 0.9rem;
        color: var(--gray-500);
        margin-top: 2rem;
    }
    /* Responsive */
    @media (max-width: 768px) {
        .container {
            padding: 1.5rem;
            margin: 1rem;
        }
        .title {
            font-size: 1.8rem;
        }
        .pdf-container {
            height: 500px;
        }
    }
</style>
</head>
<body>
<div class="container">
    <div class="header">
        <div class="logo">AIREADY</div>
        <h1 class="title">Vehicle Inspection Report</h1>
    </div>

    <div class="action-bar">
        <a href="/reports/" class="button">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="19" y1="12" x2="5" y2="12"></line>
                <polyline points="12 19 5 12 12 5"></polyline>
            </svg>
            Back to Reports
        </a>
        <a href="/reports/download/{{ report_id }}" class="button">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="7 10 12 15 17 10"></polyline>
                <line x1="12" y1="15" x2="12" y2="3"></line>
            </svg>
            Download PDF
        </a>
    </div>

    <div class="pdf-container">
        <div class="iframe-wrapper">
            <div id="loading-spinner" class="spinner"></div>
            <iframe id="pdf-viewer" width="100%" height="100%" frameborder="0"></iframe>
        </div>
    </div>
    
    <div class="footer">
        AIREADY Inspection Report System
    </div>
</div>

<script>
    // Load the PDF
    document.addEventListener('DOMContentLoaded', function() {
        const reportId = "{{ report_id }}";
        const pdfViewer = document.getElementById('pdf-viewer');
        const loadingSpinner = document.getElementById('loading-spinner');
        
        // Hide spinner when PDF loaded
        pdfViewer.onload = function() {
            loadingSpinner.style.display = 'none';
        };
        
        // Direct PDF embedding - much better than Google Docs viewer
        pdfViewer.src = "/reports/download/" + reportId;
        
        // Fallback to Google PDF viewer if browser can't handle PDFs
        pdfViewer.onerror = function() {
            pdfViewer.src = "https://docs.google.com/viewer?url=" + 
                encodeURIComponent(window.location.origin + '/reports/download/' + reportId) + 
                "&embedded=true";
        };
    });
</script>
</body>
</html>