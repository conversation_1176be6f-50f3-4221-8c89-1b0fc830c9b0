<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>AIREADY Inspection Reports</title>
<link href="https://fonts.googleapis.com/css?family=Montserrat:400,600,700,900&display=swap" rel="stylesheet">
<link rel="stylesheet" href="/static/css/reports.css">
</head>
<body>
<div class="container">
    <div class="header">
        <div class="logo">AIREADY</div>
        <h1 class="title">Vehicle Inspection Reports</h1>
        <p class="subtitle">Access and manage all inspection reports in one place</p>
    </div>

    <div class="search-container">
        <input type="text" id="search-input" class="search-input" placeholder="Search reports by customer, vehicle, or call ID...">
    </div>
    
    <div id="reports-container">
        <div class="spinner"></div>
    </div>
    
    <div class="footer">
        AIREADY Inspection Report System<br>
        <span id="report-count">Loading reports...</span>
    </div>
</div>

<script>
// DOM elements
const reportsContainer = document.getElementById('reports-container');
const searchInput = document.getElementById('search-input');
const reportCount = document.getElementById('report-count');

// Reports data
let reportsData = [];

// Fetch reports data
async function fetchReports() {
    try {
        const response = await fetch('/reports/list');
        if (!response.ok) {
            throw new Error('Failed to fetch reports');
        }
        
        reportsData = await response.json();
        renderReports(reportsData);
        
        // Update report count
        reportCount.textContent = `${reportsData.length} reports available`;
    } catch (error) {
        console.error('Error fetching reports:', error);
        reportsContainer.innerHTML = `
            <div class="empty-state">
                <h3>Error loading reports</h3>
                <p>${error.message}</p>
            </div>
        `;
    }
}

// Render reports
function renderReports(reports) {
    if (reports.length === 0) {
        reportsContainer.innerHTML = `
            <div class="empty-state">
                <h3>No reports found</h3>
                <p>No inspection reports are currently available.</p>
            </div>
        `;
        return;
    }
    
    // Create reports grid
    const reportsGrid = document.createElement('div');
    reportsGrid.className = 'reports-grid';
    
    reports.forEach(report => {
        const reportCard = document.createElement('div');
        reportCard.className = 'report-card';
        
        // Format date for display
        const reportDate = new Date(report.date_created);
        const formattedDate = reportDate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
        
        reportCard.innerHTML = `
            <h3>${report.customer_name}</h3>
            <div class="report-details">
                <div class="report-detail">
                    <div class="detail-label">Vehicle:</div>
                    <div class="detail-value">${report.vehicle_info || 'Unknown'}</div>
                </div>
                <div class="report-detail">
                    <div class="detail-label">Date:</div>
                    <div class="detail-value">${formattedDate}</div>
                </div>
                <div class="report-detail">
                    <div class="detail-label">Call ID:</div>
                    <div class="detail-value">${report.call_id}</div>
                </div>
                <div class="report-detail">
                    <div class="detail-label">Size:</div>
                    <div class="detail-value">${report.file_size}</div>
                </div>
            </div>
            <div class="report-actions">
                <a href="/reports/view/${report.id}" class="button">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                    </svg>
                    View
                </a>
                <a href="/reports/download/${report.id}" class="button secondary">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7 10 12 15 17 10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                    </svg>
                    Download
                </a>
            </div>
        `;
        
        reportsGrid.appendChild(reportCard);
    });
    
    reportsContainer.innerHTML = '';
    reportsContainer.appendChild(reportsGrid);
}

// Search functionality
searchInput.addEventListener('input', (e) => {
    const searchTerm = e.target.value.toLowerCase();
    
    if (searchTerm === '') {
        renderReports(reportsData);
        return;
    }
    
    const filteredReports = reportsData.filter(report => {
        return (
            report.customer_name.toLowerCase().includes(searchTerm) ||
            report.vehicle_info.toLowerCase().includes(searchTerm) ||
            report.call_id.toLowerCase().includes(searchTerm)
        );
    });
    
    renderReports(filteredReports);
    reportCount.textContent = `${filteredReports.length} of ${reportsData.length} reports`;
});

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    fetchReports();
});
</script>
</body>
</html>