<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Vehicle Inspection Report</title>
<link href="https://fonts.googleapis.com/css?family=Montserrat:400,700&display=swap" rel="stylesheet">
<style>
    :root {
        --primary: #102a43;
        --secondary: #243b53;
        --accent: #486581;
        --bg-gradient: linear-gradient(120deg, #f0f4f8 0%, #d9e2ec 100%);
        --success: #2cb67d;
        --warning: #ff8906;
        --danger: #e63946;
        --white: #fff;
        --gray: #bfc9d1;
        --light: #f0f4f8;
        --font-main: 'Montserrat', Arial, sans-serif;
    }
    body {
        background: var(--bg-gradient);
        font-family: var(--font-main);
        margin: 0;
        padding: 0;
        color: var(--secondary);
    }
    .container {
        background: var(--white);
        max-width: 950px;
        margin: 40px auto;
        box-shadow: 0 6px 40px 0 rgba(32, 56, 88, 0.20);
        border-radius: 20px;
        padding: 38px 44px 28px 44px;
    }
    .logo {
        font-weight: bold;
        color: var(--accent);
        font-size: 1.3rem;
        margin-bottom: 14px;
    }
    .title {
        font-weight: 900;
        font-size: 2.1rem;
        color: var(--primary);
        letter-spacing: -1px;
        margin-bottom: 28px;
    }
    .section-title {
        margin: 36px 0 20px;
        font-size: 1.18rem;
        color: var(--primary);
        border-left: 5px solid var(--accent);
        padding-left: 12px;
        letter-spacing: 1px;
        font-weight: 700;
    }
    .info-table, .checklist-table, .signoff-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 8px;
    }
    .info-table th, .checklist-table th, .signoff-table th {
        text-align: left;
        color: var(--accent);
        font-size: 1rem;
        font-weight: 600;
        padding-right: 16px;
    }
    .info-table td, .checklist-table td, .signoff-table td {
        font-size: 1rem;
        padding: 6px 12px 6px 0;
        color: var(--secondary);
        vertical-align: top;
    }
    .checklist-table {
        margin-bottom: 16px;
        border-radius: 12px;
        overflow: hidden;
        background: var(--light);
    }
    .checklist-table th,
    .checklist-table td {
        padding: 10px 12px;
    }
    .checklist-table tr {
        background: var(--white);
        transition: background 0.15s;
    }
    .checklist-table tr:hover {
        background: #edf2fb;
    }
    .checklist-table .status-ok {
        color: var(--success);
        font-weight: bold;
    }
    .checklist-table .status-changed {
        color: var(--accent);
        font-weight: bold;
    }
    .checklist-table .status-na {
        color: var(--gray);
        font-style: italic;
    }
    .checklist-table .status-repair {
        color: var(--danger);
        font-weight: bold;
        letter-spacing: 1px;
    }
    .signoff-table td, .signoff-table th {
        padding-top: 12px;
        padding-bottom: 12px;
    }
    .button {
        display: inline-block;
        background: var(--accent);
        color: var(--white);
        padding: 7px 22px;
        border-radius: 6px;
        text-decoration: none;
        font-weight: 700;
        font-size: 1rem;
        box-shadow: 0 2px 6px 0 rgba(72, 101, 129, 0.12);
        margin-top: 10px;
        transition: background 0.2s;
    }
    .button:hover { background: var(--primary);}
    .footer {
        text-align: right;
        font-size: 0.9rem;
        color: var(--gray);
        margin-top: 26px;
    }
    /* Responsive */
    @media (max-width: 700px) {
        .container {padding: 12px;}
        .title {font-size: 1.25rem;}
    }
</style>
</head>
<body>
<div class="container">
    <div class="logo">WFS</div>
    <div class="title">Vehicle Inspection Report</div>

    <div class="section-title">Vehicle Information</div>
    <table class="info-table">
        <tr><th>Owner</th><td>{{ owner }}</td><th>Odometer Reading</th><td>{{ odometer }}</td></tr>
        <tr><th>VIN</th><td>{{ vin|default('Not provided', true) }}</td><th>Engine Hours</th><td>{{ engine_hours|default('Not available', true) }}</td></tr>
        <tr><th>Make / Model / Year</th><td>{{ make_model_year }}</td><th>Inspection Date</th><td>{{ inspection_date }}</td></tr>
        <tr><th>License Plate</th><td>{{ license_plate|default('Not provided', true) }}</td><th>Service Level</th><td>{{ service_level|default('Standard', true) }}</td></tr>
    </table>

    <div class="section-title">Inspection Checklist</div>
    <table class="checklist-table">
        <tr><th>Item</th><th>Status</th><th>Notes</th></tr>
        {% for item in checklist_items %}
        <tr>
            <td>{{ item.name }}</td>
            <td class="{{ item.status_class }}">{{ item.status_text }}</td>
            <td>{{ item.notes }}</td>
        </tr>
        {% endfor %}
    </table>

    <div class="section-title">Inspector Sign-Off</div>
    <table class="signoff-table">
        <tr>
            <th>Inspector Name</th>
            <td>{{ inspector_name }}</td>
            <th>Signature</th>
            <td><img src="https://via.placeholder.com/105x30?text=Signature" alt="signature" style="border-bottom:1px solid var(--gray);height:30px;"></td>
        </tr>
        <tr>
            <th>Date</th>
            <td>{{ inspection_date }}</td>
            <th>Location</th>
            <td>{{ inspection_location|default('Not specified', true) }}</td>
        </tr>
    </table>
    
    {% if additional_notes %}
    <div class="section-title">Additional Notes</div>
    <p>{{ additional_notes }}</p>
    {% endif %}
    
    <a href="#" class="button" id="download-pdf">Download PDF</a>
    <div class="footer">
        Report generated by Vehicle Inspection System.<br>
        For support, contact <a href="mailto:<EMAIL>" style="color: #486581; text-decoration:underline;"><EMAIL></a>
    </div>
</div>
</body>
</html>