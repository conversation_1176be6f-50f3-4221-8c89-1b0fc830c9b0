#!/usr/bin/env python3
"""
Test the enhanced extraction system with real transcript files.
"""

import asyncio
import json
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import extract_and_send_inspection_data
from flexible_extraction import FlexibleExtractor

async def test_real_transcripts():
    """Test the enhanced extraction system with real transcript files."""
    print("🔍 Testing Enhanced Extraction System with Real Transcripts")
    print("="*80)
    
    transcripts_dir = "transcripts"
    if not os.path.exists(transcripts_dir):
        print(f"❌ Transcripts directory not found: {transcripts_dir}")
        return
    
    # Get all transcript files
    transcript_files = [f for f in os.listdir(transcripts_dir) if f.endswith('.json')]
    transcript_files.sort(reverse=True)  # Most recent first
    
    print(f"📁 Found {len(transcript_files)} transcript files")
    
    # Test with a few recent files
    test_files = transcript_files[:5]  # Test with 5 most recent
    
    extractor = FlexibleExtractor()
    results = []
    
    for i, filename in enumerate(test_files, 1):
        filepath = os.path.join(transcripts_dir, filename)
        
        print(f"\n📄 Test {i}/{len(test_files)}: {filename}")
        print("-" * 60)
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                transcript_data = json.load(f)
            
            transcript = transcript_data.get('transcript', '')
            structured_conv = transcript_data.get('structured_conversation', [])
            
            print(f"📄 Transcript length: {len(transcript)} chars")
            print(f"💬 Structured conversation items: {len(structured_conv)}")
            
            # Classify the conversation
            classification = extractor.classify_conversation(transcript, structured_conv)
            print(f"🔍 Classification:")
            print(f"   Type: {classification['type']}")
            print(f"   Confidence: {classification['confidence']:.2f}")
            print(f"   Quality: {classification['conversation_quality']}")
            print(f"   Recommendation: {classification['extraction_recommendation']}")
            
            # Test extraction (but don't send to API to avoid spam)
            print(f"\n🧪 Testing extraction (dry run)...")
            
            # Extract with flexible system
            vehicle_info = extractor.extract_vehicle_info(transcript, structured_conv)
            inspection_items = extractor.extract_inspection_items(transcript, structured_conv)
            
            vehicle_fields_filled = len([v for v in vehicle_info.values() if v])
            print(f"🚗 Vehicle fields filled: {vehicle_fields_filled}/9")
            print(f"📋 Inspection items found: {len(inspection_items)}")
            
            # Show some extracted data
            if vehicle_fields_filled > 0:
                print("   Vehicle info found:")
                for key, value in vehicle_info.items():
                    if value and key not in ['inspection_date_vehicle', 'report_generation_datetime', 'signoff_date']:
                        print(f"      {key}: {value}")
            
            if inspection_items:
                print("   Inspection items found:")
                for j, item in enumerate(inspection_items[:3], 1):
                    print(f"      {j}. {item.get('item', 'N/A')}: {item.get('status', 'N/A')}")
                if len(inspection_items) > 3:
                    print(f"      ... and {len(inspection_items) - 3} more items")
            
            # Determine if this would be processed successfully
            would_extract = (
                len(inspection_items) > 0 or 
                vehicle_fields_filled >= 3
            )
            
            result = {
                'filename': filename,
                'transcript_length': len(transcript),
                'conversation_items': len(structured_conv),
                'classification': classification,
                'vehicle_fields': vehicle_fields_filled,
                'inspection_items': len(inspection_items),
                'would_extract': would_extract,
                'extraction_method': 'flexible' if would_extract else 'openai_fallback'
            }
            results.append(result)
            
            print(f"📊 Result: {'✅ Would extract data' if would_extract else '❌ Would need OpenAI fallback'}")
            
        except Exception as e:
            print(f"❌ Error processing {filename}: {e}")
            results.append({
                'filename': filename,
                'error': str(e)
            })
    
    # Summary
    print("\n" + "="*80)
    print("📊 SUMMARY OF REAL TRANSCRIPT TESTING")
    print("="*80)
    
    successful_extractions = sum(1 for r in results if r.get('would_extract', False))
    total_tests = len([r for r in results if 'error' not in r])
    
    print(f"📈 Overall Results:")
    print(f"   Total transcripts tested: {total_tests}")
    print(f"   Successful extractions: {successful_extractions}")
    print(f"   Success rate: {(successful_extractions/total_tests*100):.1f}%" if total_tests > 0 else "   Success rate: N/A")
    
    # Breakdown by conversation type
    type_counts = {}
    for result in results:
        if 'classification' in result:
            conv_type = result['classification']['type']
            type_counts[conv_type] = type_counts.get(conv_type, 0) + 1
    
    print(f"\n📋 Conversation Types Found:")
    for conv_type, count in type_counts.items():
        print(f"   {conv_type}: {count}")
    
    # Extraction method breakdown
    flexible_count = sum(1 for r in results if r.get('extraction_method') == 'flexible')
    openai_count = sum(1 for r in results if r.get('extraction_method') == 'openai_fallback')
    
    print(f"\n🔧 Extraction Methods:")
    print(f"   Flexible extraction: {flexible_count}")
    print(f"   OpenAI fallback: {openai_count}")
    
    # Show detailed results for interesting cases
    print(f"\n📋 Detailed Results:")
    for result in results:
        if 'error' not in result:
            filename = result['filename'][:30] + "..." if len(result['filename']) > 30 else result['filename']
            conv_type = result['classification']['type']
            confidence = result['classification']['confidence']
            method = result['extraction_method']
            items = result['inspection_items']
            fields = result['vehicle_fields']
            
            print(f"   {filename:<35} | {conv_type:<12} | {confidence:.2f} | {method:<15} | {items:2d} items | {fields:2d} fields")

async def test_specific_transcript():
    """Test extraction with a specific transcript file (for debugging)."""
    print("\n🎯 Testing Specific Transcript (for debugging)")
    print("="*60)
    
    # You can specify a particular file here for detailed testing
    test_file = "transcript_20250303_151949_MZed24c6176db135021051e0306897bcbe.json"
    filepath = os.path.join("transcripts", test_file)
    
    if not os.path.exists(filepath):
        print(f"❌ Test file not found: {filepath}")
        return
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            transcript_data = json.load(f)
        
        transcript = transcript_data.get('transcript', '')
        structured_conv = transcript_data.get('structured_conversation', [])
        
        print(f"📄 Testing with: {test_file}")
        print(f"📄 Transcript length: {len(transcript)} chars")
        print(f"💬 Structured conversation items: {len(structured_conv)}")
        
        # Show transcript preview
        print(f"\n📄 Transcript preview:")
        print(transcript[:500] + "..." if len(transcript) > 500 else transcript)
        
        # Test the full extraction process
        print(f"\n🧪 Running full extraction test...")
        result = await extract_and_send_inspection_data(transcript, structured_conv)
        
        print(f"\n📊 Final result: {'✅ SUCCESS' if result else '❌ FAILED'}")
        
    except Exception as e:
        print(f"❌ Error testing specific transcript: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Check if OpenAI API key is available
    openai_key = os.getenv("OPENAI_API_KEY")
    if not openai_key:
        print("❌ ERROR: OPENAI_API_KEY not found in environment variables")
        print("Please set your OpenAI API key in the .env file")
        sys.exit(1)
    
    print(f"🔑 OpenAI API key found (length: {len(openai_key)})")
    
    # Run the tests
    asyncio.run(test_real_transcripts())
    
    # Optionally test a specific transcript
    # asyncio.run(test_specific_transcript())
    
    print("\n🎉 Real transcript testing completed!")
