import pytest
import sys
import os

# Add the parent directory to the path so we can import main
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure pytest-asyncio
pytest_plugins = ["asyncio"]

# Set the default fixture loop scope to function
def pytest_addoption(parser):
    parser.addini('asyncio_default_fixture_loop_scope', default='function',
                 help='default scope for event loop fixtures')
