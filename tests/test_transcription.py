import pytest
import asyncio
import json
import sys
import os
from unittest.mock import AsyncMock, patch, MagicMock

# Add the parent directory to the path so we can import main
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))  
import main
from fastapi.testclient import TestClient

# Constants for testing
TEST_CALL_SID = "test_transcription_call_sid"
TEST_TRANSCRIPT_SEGMENTS = ["Hello", "how are you", "today?"]

class TestTranscription:
    """Test cases for conversation transcription functionality."""
    
    @pytest.mark.asyncio
    async def test_transcription_aggregation(self):
        """Test that transcription segments are properly aggregated."""
        # Create a list to store transcriptions
        user_transcriptions = []
        full_conversation_transcript = ""
        
        # Add some test transcription segments
        user_transcriptions.extend(TEST_TRANSCRIPT_SEGMENTS)
        
        # Define the aggregate_transcriptions function
        async def aggregate_transcriptions():
            nonlocal user_transcriptions, full_conversation_transcript
            
            if user_transcriptions:
                # Join all transcriptions with a space
                current_segment = " ".join(user_transcriptions)
                
                # Add to the full transcript with a newline if not empty
                if full_conversation_transcript:
                    full_conversation_transcript += "\n" + current_segment
                else:
                    full_conversation_transcript = current_segment
                
                # Clear the current transcriptions after aggregating
                user_transcriptions = []
                
                return current_segment
            return None
        
        # Call the function to aggregate transcriptions
        segment = await aggregate_transcriptions()
        
        # Verify that the transcriptions were aggregated correctly
        assert segment == "Hello how are you today?"
        assert full_conversation_transcript == "Hello how are you today?"
        assert len(user_transcriptions) == 0
        
        # Add more transcription segments
        user_transcriptions.extend(["I'm doing well", "thank you"])
        
        # Call the function again to aggregate the new segments
        segment = await aggregate_transcriptions()
        
        # Verify that the new segments were added to the full transcript
        assert segment == "I'm doing well thank you"
        assert full_conversation_transcript == "Hello how are you today?\nI'm doing well thank you"
        assert len(user_transcriptions) == 0
    
    @pytest.mark.asyncio
    async def test_transcript_extraction_from_events(self):
        """Test that transcripts are correctly extracted from different event types."""
        # Test response.done event with transcript
        response_done_event = {
            "type": "response.done",
            "response": {
                "output": [
                    {
                        "content": [
                            {
                                "type": "audio",
                                "transcript": "This is a transcript from response.done"
                            }
                        ]
                    }
                ]
            }
        }
        
        # Test input_audio_buffer.committed event with transcript
        input_buffer_event = {
            "type": "input_audio_buffer.committed",
            "transcript": "This is a transcript from input_audio_buffer.committed"
        }
        
        # Test audio_transcript.delta event with transcript
        transcript_delta_event = {
            "type": "response.audio_transcript.delta",
            "delta": {
                "text": "This is a transcript from audio_transcript.delta"
            }
        }
        
        # Function to extract transcript from events
        def extract_transcript(response):
            transcript = None
            
            # Handle different transcript event types
            if response.get("type") == "response.done" and isinstance(response.get("response"), dict) and response.get("response", {}).get("output"):
                for item in response["response"]["output"]:
                    if isinstance(item, dict) and "content" in item:
                        for content in item.get("content", []):
                            if isinstance(content, dict) and content.get("type") == "audio" and content.get("transcript"):
                                transcript = content["transcript"]
            elif response.get("type") == "input_audio_buffer.committed" and response.get("transcript"):
                transcript = response["transcript"]
            elif response.get("type") == "response.audio_transcript.delta" and isinstance(response.get("delta"), dict) and response.get("delta", {}).get("text"):
                transcript = response["delta"]["text"]
            
            return transcript
        
        # Extract transcripts from each event type
        transcript1 = extract_transcript(response_done_event)
        transcript2 = extract_transcript(input_buffer_event)
        transcript3 = extract_transcript(transcript_delta_event)
        
        # Verify that transcripts were extracted correctly
        assert transcript1 == "This is a transcript from response.done"
        assert transcript2 == "This is a transcript from input_audio_buffer.committed"
        assert transcript3 == "This is a transcript from audio_transcript.delta"
    
    @pytest.mark.asyncio
    async def test_speech_stopped_event_handling(self):
        """Test that speech stopped events trigger transcription aggregation."""
        # Create a mock for the aggregate_transcriptions function
        aggregate_transcriptions_mock = AsyncMock()
        
        # Create a mock for the time.time function
        with patch('time.time', return_value=1000.0):
            # Define the function to handle speech stopped events
            async def handle_speech_stopped_event(response):
                if response.get('type') == main.SPEECH_STOPPED_EVENT:
                    print("Speech stopped detected, aggregating transcriptions")
                    await aggregate_transcriptions_mock()
                    # Reset the last speech timestamp
                    return True
                return False
            
            # Test with a speech stopped event
            result = await handle_speech_stopped_event({"type": main.SPEECH_STOPPED_EVENT})
            
            # Verify that the aggregate_transcriptions function was called
            assert result is True
            aggregate_transcriptions_mock.assert_called_once()
            
            # Test with a different event type
            aggregate_transcriptions_mock.reset_mock()
            result = await handle_speech_stopped_event({"type": "some_other_event"})
            
            # Verify that the aggregate_transcriptions function was not called
            assert result is False
            aggregate_transcriptions_mock.assert_not_called()

    @pytest.mark.asyncio
    async def test_transcript_done_event_handling(self):
        """Test that transcript done events trigger transcription aggregation."""
        # Create a mock for the aggregate_transcriptions function
        aggregate_transcriptions_mock = AsyncMock()
        
        # Define the function to handle transcript done events
        async def handle_transcript_done_event(response):
            if response.get('type') == main.AUDIO_TRANSCRIPT_DONE_EVENT:
                print("Transcript done event received, finalizing transcript segment")
                await aggregate_transcriptions_mock()
                return True
            return False
        
        # Test with a transcript done event
        result = await handle_transcript_done_event({"type": main.AUDIO_TRANSCRIPT_DONE_EVENT})
        
        # Verify that the aggregate_transcriptions function was called
        assert result is True
        aggregate_transcriptions_mock.assert_called_once()
        
        # Test with a different event type
        aggregate_transcriptions_mock.reset_mock()
        result = await handle_transcript_done_event({"type": "some_other_event"})
        
        # Verify that the aggregate_transcriptions function was not called
        assert result is False
        aggregate_transcriptions_mock.assert_not_called()
