import pytest
import asyncio
import json
import base64
import time
import sys
import os
from unittest.mock import AsyncMock, patch, MagicMock
import httpx

# Add the parent directory to the path so we can import main
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))  
import main
from fastapi.testclient import TestClient
from fastapi.websockets import WebSocketDisconnect

# Constants for testing
TEST_CALL_SID = "test_integration_call_sid"

class DummyWebSocket:
    """Mock WebSocket class for testing."""
    def __init__(self):
        self.sent_messages = []
        self.open = True
        self.closed = False
    
    async def send(self, message):
        self.sent_messages.append(message)
    
    async def close(self):
        self.open = False
        self.closed = True
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc, tb):
        pass

class TestFunctionalIntegration:
    """Functional tests that integrate AI hangup, transcription, and upload."""
    
    @pytest.mark.asyncio
    async def test_silence_detection_and_hangup_with_transcript_upload(self):
        """
        Test the complete flow:
        1. Detect prolonged silence
        2. Send a warning
        3. Detect continued silence
        4. Hang up the call
        5. Upload the transcript
        """
        # Create mock WebSocket instances
        twilio_ws = AsyncMock()
        openai_ws = DummyWebSocket()
        
        # Create variables for testing
        stream_sid = TEST_CALL_SID
        call_start_time = time.time() - 60  # 1 minute ago
        last_speech_timestamp = time.time() - 40  # 40 seconds ago
        silence_warning_sent = False
        silence_warning_count = 0
        user_transcriptions = ["Hello", "how are you"]
        full_conversation_transcript = "Hello how are you"
        
        # Create a mock for the httpx.AsyncClient
        mock_client = AsyncMock()
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_client.post.return_value = mock_response
        
        # Mock the httpx.AsyncClient context manager
        class MockAsyncClientContextManager:
            async def __aenter__(self):
                return mock_client
            
            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass
        
        # Define the aggregate_transcriptions function
        async def aggregate_transcriptions():
            nonlocal user_transcriptions, full_conversation_transcript
            
            if user_transcriptions:
                # Join all transcriptions with a space
                current_segment = " ".join(user_transcriptions)
                
                # Add to the full transcript with a newline if not empty
                if full_conversation_transcript:
                    full_conversation_transcript += "\n" + current_segment
                else:
                    full_conversation_transcript = current_segment
                
                # Clear the current transcriptions after aggregating
                user_transcriptions = []
        
        # Define the upload_transcript function
        async def upload_transcript():
            nonlocal full_conversation_transcript
            
            # Ensure we have the final aggregated transcript
            await aggregate_transcriptions()
            
            if not full_conversation_transcript:
                print("No transcript to upload.")
                return False
                
            try:
                print(f"Uploading transcript (length: {len(full_conversation_transcript)})")
                
                # Prepare the payload
                payload = {
                    "transcript": full_conversation_transcript,
                    "call_id": stream_sid,
                    "timestamp": time.time(),
                    "metadata": {
                        "duration": time.time() - call_start_time,
                        "silence_warnings_sent": silence_warning_count,
                        "transcript_segments": len(full_conversation_transcript.split('\n'))
                    }
                }
                
                # Send the transcript to our endpoint
                with patch('httpx.AsyncClient', return_value=MockAsyncClientContextManager()):
                    async with httpx.AsyncClient() as client:
                        response = await client.post(
                            "http://localhost:8001/transcript",
                            json=payload,
                            headers={"Content-Type": "application/json"}
                        )
                        
                        if response.status_code == 200:
                            print("Transcript uploaded successfully")
                            return True
                        else:
                            print(f"Failed to upload transcript: {response.status_code}")
                            return False
            except Exception as e:
                print(f"Error uploading transcript: {e}")
                return False
        
        # Define the send_silence_warning function
        async def send_silence_warning():
            nonlocal silence_warning_sent, silence_warning_count
            try:
                # Create a message to send to the user
                silence_message = {
                    "type": "conversation.item.create",
                    "item": {
                        "type": "message",
                        "role": "assistant",
                        "content": [
                            {
                                "type": "text",
                                "text": "I notice there's been silence for a while. Are you still there? If I don't hear from you in the next few seconds, I'll have to end our call."
                            }
                        ]
                    }
                }
                await openai_ws.send(json.dumps(silence_message))
                
                # Increment the silence warning counter
                silence_warning_count += 1
                silence_warning_sent = True
                return True
            except Exception as e:
                print(f"Error sending silence warning: {e}")
                return False
        
        # Define the hang_up_call function
        async def hang_up_call(message="Thank you for your time today. Goodbye!"):
            try:
                # Send a final message before hanging up
                hangup_message = {
                    "type": "conversation.item.create",
                    "item": {
                        "type": "message",
                        "role": "assistant",
                        "content": [
                            {
                                "type": "text",
                                "text": message
                            }
                        ]
                    }
                }
                await openai_ws.send(json.dumps(hangup_message))
                
                # Upload the transcript before hanging up
                await upload_transcript()
                
                # Send Twilio hangup command
                hangup_response = {"event": "twiml", "streamSid": stream_sid, "twiml": "<Response><Hangup/></Response>"}
                await twilio_ws.send_json(hangup_response)
                return True
            except Exception as e:
                print(f"Error hanging up call: {e}")
                return False
        
        # Define the check_silence function
        async def check_silence():
            nonlocal silence_warning_sent, last_speech_timestamp
            try:
                # If we're still silent after 30 seconds, send a warning
                current_time = time.time()
                if current_time - last_speech_timestamp > 30 and not silence_warning_sent:
                    print("Prolonged silence detected. Sending warning to user.")
                    await send_silence_warning()
                    
                    # Wait another 10 seconds for a response (simulated)
                    last_speech_timestamp = last_speech_timestamp  # Keep the same timestamp to simulate no response
                    
                    # If still silent, hang up the call
                    if current_time - last_speech_timestamp > 40:  # 30s initial + 10s after warning
                        print("No response after silence warning. Hanging up the call.")
                        await hang_up_call("Since I haven't heard from you, I'll end our call now. Feel free to call back when you're ready to continue. Goodbye!")
                        return True
                return False
            except Exception as e:
                print(f"Error in check_silence: {e}")
                return False
        
        # Execute the check_silence function
        with patch('httpx.AsyncClient', return_value=MockAsyncClientContextManager()):
            result = await check_silence()
        
        # Verify that the silence warning was sent
        assert silence_warning_sent is True
        assert silence_warning_count == 1
        
        # Verify that the hangup message was sent to OpenAI
        assert len(openai_ws.sent_messages) == 2  # Warning message and hangup message
        
        # Verify that the transcript was uploaded
        mock_client.post.assert_called_once()
        
        # Verify that the hangup command was sent to Twilio
        twilio_ws.send_json.assert_called_once()
        
        # Verify that the function returned True for success
        assert result is True
    
    @pytest.mark.asyncio
    async def test_farewell_detection_and_hangup_with_transcript_upload(self):
        """
        Test the complete flow:
        1. Detect a farewell phrase
        2. Send a confirmation
        3. Receive confirmation
        4. Hang up the call
        5. Upload the transcript
        """
        # Create mock WebSocket instances
        twilio_ws = AsyncMock()
        openai_ws = DummyWebSocket()
        
        # Create variables for testing
        stream_sid = TEST_CALL_SID
        call_start_time = time.time() - 120  # 2 minutes ago
        goodbye_confirmation_sent = False
        user_transcriptions = ["Hello", "thanks for your help", "goodbye"]
        full_conversation_transcript = "Hello\nthanks for your help\ngoodbye"
        
        # Create a mock for the httpx.AsyncClient
        mock_client = AsyncMock()
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_client.post.return_value = mock_response
        
        # Mock the httpx.AsyncClient context manager
        class MockAsyncClientContextManager:
            async def __aenter__(self):
                return mock_client
            
            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass
        
        # Define the aggregate_transcriptions function
        async def aggregate_transcriptions():
            nonlocal user_transcriptions, full_conversation_transcript
            
            if user_transcriptions:
                # Join all transcriptions with a space
                current_segment = " ".join(user_transcriptions)
                
                # Add to the full transcript with a newline if not empty
                if full_conversation_transcript:
                    full_conversation_transcript += "\n" + current_segment
                else:
                    full_conversation_transcript = current_segment
                
                # Clear the current transcriptions after aggregating
                user_transcriptions = []
        
        # Define the upload_transcript function
        async def upload_transcript():
            nonlocal full_conversation_transcript
            
            # Ensure we have the final aggregated transcript
            await aggregate_transcriptions()
            
            if not full_conversation_transcript:
                print("No transcript to upload.")
                return False
                
            try:
                print(f"Uploading transcript (length: {len(full_conversation_transcript)})")
                
                # Prepare the payload
                payload = {
                    "transcript": full_conversation_transcript,
                    "call_id": stream_sid,
                    "timestamp": time.time(),
                    "metadata": {
                        "duration": time.time() - call_start_time,
                        "goodbye_detected": goodbye_confirmation_sent,
                        "transcript_segments": len(full_conversation_transcript.split('\n'))
                    }
                }
                
                # Send the transcript to our endpoint
                with patch('httpx.AsyncClient', return_value=MockAsyncClientContextManager()):
                    async with httpx.AsyncClient() as client:
                        response = await client.post(
                            "http://localhost:8001/transcript",
                            json=payload,
                            headers={"Content-Type": "application/json"}
                        )
                        
                        if response.status_code == 200:
                            print("Transcript uploaded successfully")
                            return True
                        else:
                            print(f"Failed to upload transcript: {response.status_code}")
                            return False
            except Exception as e:
                print(f"Error uploading transcript: {e}")
                return False
        
        # Define the send_goodbye_confirmation function
        async def send_goodbye_confirmation():
            nonlocal goodbye_confirmation_sent
            try:
                # Create a message to confirm the user wants to end the call
                confirmation_message = {
                    "type": "conversation.item.create",
                    "item": {
                        "type": "message",
                        "role": "assistant",
                        "content": [
                            {
                                "type": "text",
                                "text": "It sounds like you want to end our call. Is that correct? Please say yes if you'd like to hang up now."
                            }
                        ]
                    }
                }
                await openai_ws.send(json.dumps(confirmation_message))
                goodbye_confirmation_sent = True
                return True
            except Exception as e:
                print(f"Error sending goodbye confirmation: {e}")
                return False
        
        # Define the hang_up_call function
        async def hang_up_call(message="Thank you for your time today. Goodbye!"):
            try:
                # Send a final message before hanging up
                hangup_message = {
                    "type": "conversation.item.create",
                    "item": {
                        "type": "message",
                        "role": "assistant",
                        "content": [
                            {
                                "type": "text",
                                "text": message
                            }
                        ]
                    }
                }
                await openai_ws.send(json.dumps(hangup_message))
                
                # Upload the transcript before hanging up
                await upload_transcript()
                
                # Send Twilio hangup command
                hangup_response = {"event": "twiml", "streamSid": stream_sid, "twiml": "<Response><Hangup/></Response>"}
                await twilio_ws.send_json(hangup_response)
                return True
            except Exception as e:
                print(f"Error hanging up call: {e}")
                return False
        
        # Define the check_for_goodbye_phrases function
        async def check_for_goodbye_phrases(transcript):
            nonlocal goodbye_confirmation_sent
            try:
                # Convert transcript to lowercase for case-insensitive matching
                transcript_lower = transcript.lower()
                
                # Check for farewell phrases
                for phrase in main.FAREWELL_PHRASES:
                    if phrase in transcript_lower:
                        print(f"Farewell phrase detected: '{phrase}'")
                        
                        # If not already in confirmation mode, send confirmation
                        if not goodbye_confirmation_sent:
                            await send_goodbye_confirmation()
                            
                            # Simulate receiving confirmation from user
                            await handle_goodbye_confirmation("yes")
                        return True
                return False
            except Exception as e:
                print(f"Error in check_for_goodbye_phrases: {e}")
                return False
        
        # Define the handle_goodbye_confirmation function
        async def handle_goodbye_confirmation(response):
            try:
                # Check if the response is affirmative
                if response.lower() in ["yes", "correct", "yeah", "yep", "sure"]:
                    print("User confirmed goodbye. Hanging up the call.")
                    await hang_up_call("Thank you for confirming. I'll end our call now. Have a great day!")
                    return True
                else:
                    print("User did not confirm goodbye. Continuing the call.")
                    return False
            except Exception as e:
                print(f"Error handling goodbye confirmation: {e}")
                return False
        
        # Execute the check_for_goodbye_phrases function with a transcript containing a farewell phrase
        with patch('httpx.AsyncClient', return_value=MockAsyncClientContextManager()):
            result = await check_for_goodbye_phrases("goodbye")
        
        # Verify that the goodbye confirmation was sent
        assert goodbye_confirmation_sent is True
        
        # Verify that the hangup message was sent to OpenAI
        assert len(openai_ws.sent_messages) == 2  # Confirmation message and hangup message
        
        # Verify that the transcript was uploaded
        mock_client.post.assert_called_once()
        
        # Verify that the hangup command was sent to Twilio
        twilio_ws.send_json.assert_called_once()
        
        # Verify that the function returned True for success
        assert result is True
