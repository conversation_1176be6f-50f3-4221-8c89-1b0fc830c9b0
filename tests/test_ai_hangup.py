import pytest
import asyncio
import json
import time
import sys
import os
from unittest.mock import AsyncMock, patch, MagicMock

# Add the parent directory to the path so we can import main
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))  
import main
from fastapi.testclient import TestClient
from fastapi.websockets import WebSocketDisconnect

# Constants for testing
TEST_CALL_SID = "test_hangup_call_sid"
SILENCE_THRESHOLD = 30  # seconds
FAREWELL_PHRASE = "goodbye"

class DummyWebSocket:
    """Mock WebSocket class for testing."""
    def __init__(self):
        self.sent_messages = []
        self.open = True
        self.closed = False
    
    async def send(self, message):
        self.sent_messages.append(message)
    
    async def close(self):
        self.open = False
        self.closed = True
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc, tb):
        pass

class TestAIHangup:
    """Test cases for AI-initiated hangup functionality."""
    
    @pytest.mark.asyncio
    async def test_silence_detection(self):
        """Test that prolonged silence triggers a warning and then hangup."""
        # Create mock WebSocket instances
        twilio_ws = AsyncMock()
        openai_ws = DummyWebSocket()
        
        # Mock time.time to simulate elapsed time
        with patch('time.time') as mock_time:
            # Set initial time
            mock_time.return_value = 1000.0
            
            # Create a mock for the send_silence_warning function
            send_silence_warning_mock = AsyncMock()
            
            # Create a mock for the hang_up_call function
            hang_up_call_mock = AsyncMock()
            
            # Create the check_silence function with our mocks
            async def check_silence():
                nonlocal mock_time
                # Simulate waiting for 30 seconds
                await asyncio.sleep(0.01)  # Use a small value for testing
                
                # Update mock time to simulate 30 seconds passing
                mock_time.return_value = 1030.0
                
                # Call the send_silence_warning mock
                await send_silence_warning_mock()
                
                # Simulate waiting for another 10 seconds
                await asyncio.sleep(0.01)  # Use a small value for testing
                
                # Update mock time to simulate 10 more seconds passing
                mock_time.return_value = 1040.0
                
                # Call the hang_up_call mock
                await hang_up_call_mock("Since I haven't heard from you, I'll end our call now. Feel free to call back when you're ready to continue. Goodbye!")
            
            # Execute the check_silence function
            await check_silence()
            
            # Verify that the silence warning was sent
            send_silence_warning_mock.assert_called_once()
            
            # Verify that the call was hung up after the warning
            hang_up_call_mock.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_farewell_phrase_detection(self):
        """Test that farewell phrases are detected and trigger confirmation."""
        # Create a mock for the openai_ws
        openai_ws = DummyWebSocket()
        
        # Create a mock for the send_goodbye_confirmation function
        with patch('json.dumps', return_value='{"mocked":"json"}') as mock_dumps:
            # Create the check_for_goodbye_phrases function
            async def check_for_goodbye_phrases(transcript):
                # Convert transcript to lowercase for case-insensitive matching
                transcript_lower = transcript.lower()
                
                # Check for farewell phrases
                for phrase in main.FAREWELL_PHRASES:
                    if phrase in transcript_lower:
                        # Send confirmation
                        confirmation_message = {
                            "type": "conversation.item.create",
                            "item": {
                                "type": "message",
                                "role": "assistant",
                                "content": [
                                    {
                                        "type": "text",
                                        "text": "It sounds like you want to end our call. Is that correct? Please say yes if you'd like to hang up now."
                                    }
                                ]
                            }
                        }
                        await openai_ws.send(json.dumps(confirmation_message))
                        return True
                return False
            
            # Test with a farewell phrase
            result = await check_for_goodbye_phrases("Goodbye, thank you for your help")
            
            # Verify that the confirmation message was sent
            assert result is True
            assert len(openai_ws.sent_messages) == 1
            
            # Test with a non-farewell phrase
            openai_ws.sent_messages = []
            result = await check_for_goodbye_phrases("Can you help me with something else?")
            
            # Verify that no confirmation message was sent
            assert result is False
            assert len(openai_ws.sent_messages) == 0
    
    @pytest.mark.asyncio
    async def test_hang_up_call(self):
        """Test that the hang_up_call function sends the correct messages."""
        # Create mock WebSocket instances
        twilio_ws = AsyncMock()
        openai_ws = DummyWebSocket()
        
        # Mock the upload_transcript function
        upload_transcript_mock = AsyncMock()
        
        # Create the hang_up_call function with our mocks
        async def hang_up_call(message="Thank you for your time today. Goodbye!"):
            # Send a final message before hanging up
            hangup_message = {
                "type": "conversation.item.create",
                "item": {
                    "type": "message",
                    "role": "assistant",
                    "content": [
                        {
                            "type": "text",
                            "text": message
                        }
                    ]
                }
            }
            await openai_ws.send(json.dumps(hangup_message))
            
            # Wait a moment for the message to be processed
            await asyncio.sleep(0.01)  # Use a small value for testing
            
            # Upload the transcript before hanging up
            await upload_transcript_mock()
            
            # Send Twilio hangup command
            hangup_response = {"event": "twiml", "streamSid": TEST_CALL_SID, "twiml": "<Response><Hangup/></Response>"}
            await twilio_ws.send_json(hangup_response)
        
        # Execute the hang_up_call function
        await hang_up_call("Custom goodbye message")
        
        # Verify that the final message was sent to OpenAI
        assert len(openai_ws.sent_messages) == 1
        
        # Verify that the transcript was uploaded
        upload_transcript_mock.assert_called_once()
        
        # Verify that the hangup command was sent to Twilio
        twilio_ws.send_json.assert_called_once()
