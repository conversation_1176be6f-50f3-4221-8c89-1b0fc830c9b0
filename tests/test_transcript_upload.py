import pytest
import asyncio
import json
import time
import sys
import os
from unittest.mock import AsyncMock, patch, MagicMock
import httpx

# Add the parent directory to the path so we can import main
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))  
import main
from fastapi.testclient import TestClient

# Constants for testing
TEST_CALL_SID = "test_upload_call_sid"
TEST_FULL_TRANSCRIPT = "This is a test transcript.\nIt has multiple lines.\nFor testing purposes."

class TestTranscriptUpload:
    """Test cases for transcript upload functionality."""
    
    @pytest.mark.asyncio
    async def test_upload_transcript_success(self):
        """Test that the transcript is successfully uploaded to the endpoint."""
        # Create variables for testing
        full_conversation_transcript = TEST_FULL_TRANSCRIPT
        stream_sid = TEST_CALL_SID
        call_start_time = time.time() - 120  # 2 minutes ago
        silence_warning_count = 1
        goodbye_confirmation_sent = True
        
        # Create a mock for the aggregate_transcriptions function
        aggregate_transcriptions_mock = AsyncMock()
        
        # Create a mock for the httpx.AsyncClient
        mock_client = AsyncMock()
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_client.post.return_value = mock_response
        
        # Mock the httpx.AsyncClient context manager
        class MockAsyncClientContextManager:
            async def __aenter__(self):
                return mock_client
            
            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass
        
        # Define the upload_transcript function with our mocks
        async def upload_transcript():
            nonlocal full_conversation_transcript
            
            # Ensure we have the final aggregated transcript
            await aggregate_transcriptions_mock()
            
            if not full_conversation_transcript:
                print("No transcript to upload.")
                return False
                
            try:
                print(f"Uploading transcript (length: {len(full_conversation_transcript)})")
                
                # Prepare the payload
                payload = {
                    "transcript": full_conversation_transcript,
                    "call_id": stream_sid,
                    "timestamp": time.time(),
                    "metadata": {
                        "duration": time.time() - call_start_time,
                        "silence_warnings_sent": silence_warning_count,
                        "goodbye_detected": goodbye_confirmation_sent,
                        "transcript_segments": len(full_conversation_transcript.split('\n'))
                    }
                }
                
                # Send the transcript to our endpoint
                with patch('httpx.AsyncClient', return_value=MockAsyncClientContextManager()):
                    async with httpx.AsyncClient() as client:
                        response = await client.post(
                            "http://localhost:8001/transcript",
                            json=payload,
                            headers={"Content-Type": "application/json"}
                        )
                        
                        if response.status_code == 200:
                            print("Transcript uploaded successfully")
                            return True
                        else:
                            print(f"Failed to upload transcript: {response.status_code}")
                            return False
            except Exception as e:
                print(f"Error uploading transcript: {e}")
                return False
        
        # Execute the upload_transcript function
        with patch('httpx.AsyncClient', return_value=MockAsyncClientContextManager()):
            result = await upload_transcript()
        
        # Verify that the aggregate_transcriptions function was called
        aggregate_transcriptions_mock.assert_called_once()
        
        # Verify that the httpx client post method was called with the correct arguments
        mock_client.post.assert_called_once()
        args, kwargs = mock_client.post.call_args
        assert args[0] == "http://localhost:8001/transcript"
        assert kwargs["headers"] == {"Content-Type": "application/json"}
        
        # Verify that the payload contains the correct data
        payload = kwargs["json"]
        assert payload["transcript"] == TEST_FULL_TRANSCRIPT
        assert payload["call_id"] == TEST_CALL_SID
        assert "timestamp" in payload
        assert "metadata" in payload
        assert payload["metadata"]["silence_warnings_sent"] == 1
        assert payload["metadata"]["goodbye_detected"] is True
        assert payload["metadata"]["transcript_segments"] == 3
        
        # Verify that the function returned True for success
        assert result is True
    
    @pytest.mark.asyncio
    async def test_upload_transcript_failure(self):
        """Test that the function handles upload failures gracefully."""
        # Create variables for testing
        full_conversation_transcript = TEST_FULL_TRANSCRIPT
        stream_sid = TEST_CALL_SID
        
        # Create a mock for the aggregate_transcriptions function
        aggregate_transcriptions_mock = AsyncMock()
        
        # Create a mock for the httpx.AsyncClient
        mock_client = AsyncMock()
        mock_response = AsyncMock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        mock_client.post.return_value = mock_response
        
        # Mock the httpx.AsyncClient context manager
        class MockAsyncClientContextManager:
            async def __aenter__(self):
                return mock_client
            
            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass
        
        # Define the upload_transcript function with our mocks
        async def upload_transcript():
            nonlocal full_conversation_transcript
            
            # Ensure we have the final aggregated transcript
            await aggregate_transcriptions_mock()
            
            if not full_conversation_transcript:
                print("No transcript to upload.")
                return False
                
            try:
                # Send the transcript to our endpoint
                with patch('httpx.AsyncClient', return_value=MockAsyncClientContextManager()):
                    async with httpx.AsyncClient() as client:
                        response = await client.post(
                            "http://localhost:8001/transcript",
                            json={"transcript": full_conversation_transcript, "call_id": stream_sid},
                            headers={"Content-Type": "application/json"}
                        )
                        
                        if response.status_code == 200:
                            print("Transcript uploaded successfully")
                            return True
                        else:
                            print(f"Failed to upload transcript: {response.status_code}")
                            return False
            except Exception as e:
                print(f"Error uploading transcript: {e}")
                return False
        
        # Execute the upload_transcript function
        with patch('httpx.AsyncClient', return_value=MockAsyncClientContextManager()):
            result = await upload_transcript()
        
        # Verify that the aggregate_transcriptions function was called
        aggregate_transcriptions_mock.assert_called_once()
        
        # Verify that the httpx client post method was called
        mock_client.post.assert_called_once()
        
        # Verify that the function returned False for failure
        assert result is False
    
    @pytest.mark.asyncio
    async def test_upload_empty_transcript(self):
        """Test that the function handles empty transcripts correctly."""
        # Create variables for testing
        full_conversation_transcript = ""
        
        # Create a mock for the aggregate_transcriptions function
        aggregate_transcriptions_mock = AsyncMock()
        
        # Define the upload_transcript function with our mocks
        async def upload_transcript():
            nonlocal full_conversation_transcript
            
            # Ensure we have the final aggregated transcript
            await aggregate_transcriptions_mock()
            
            if not full_conversation_transcript:
                print("No transcript to upload.")
                return False
                
            # This part should not be reached with an empty transcript
            return True
        
        # Execute the upload_transcript function
        result = await upload_transcript()
        
        # Verify that the aggregate_transcriptions function was called
        aggregate_transcriptions_mock.assert_called_once()
        
        # Verify that the function returned False for empty transcript
        assert result is False
    
    @pytest.mark.asyncio
    async def test_upload_transcript_exception(self):
        """Test that the function handles exceptions during upload."""
        # Create variables for testing
        full_conversation_transcript = TEST_FULL_TRANSCRIPT
        
        # Create a mock for the aggregate_transcriptions function
        aggregate_transcriptions_mock = AsyncMock()
        
        # Create a mock for the httpx.AsyncClient that raises an exception
        mock_client = AsyncMock()
        mock_client.post.side_effect = Exception("Connection error")
        
        # Mock the httpx.AsyncClient context manager
        class MockAsyncClientContextManager:
            async def __aenter__(self):
                return mock_client
            
            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass
        
        # Define the upload_transcript function with our mocks
        async def upload_transcript():
            nonlocal full_conversation_transcript
            
            # Ensure we have the final aggregated transcript
            await aggregate_transcriptions_mock()
            
            try:
                # Send the transcript to our endpoint
                with patch('httpx.AsyncClient', return_value=MockAsyncClientContextManager()):
                    async with httpx.AsyncClient() as client:
                        response = await client.post(
                            "http://localhost:8001/transcript",
                            json={"transcript": full_conversation_transcript},
                            headers={"Content-Type": "application/json"}
                        )
                return True
            except Exception as e:
                print(f"Error uploading transcript: {e}")
                return False
        
        # Execute the upload_transcript function
        with patch('httpx.AsyncClient', return_value=MockAsyncClientContextManager()):
            result = await upload_transcript()
        
        # Verify that the aggregate_transcriptions function was called
        aggregate_transcriptions_mock.assert_called_once()
        
        # Verify that the function returned False for exception
        assert result is False
