# This workflow will install Python dependencies, run tests and lint with a single version of Python
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python

name: Voice Stream API Tests

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "*" ]

permissions:
  contents: read

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.13
      uses: actions/setup-python@v3
      with:
        python-version: "3.13"
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 pytest pytest-asyncio httpx fastapi websockets python-dotenv pytest-cov
        if [ -f requirements-test.txt ]; then pip install -r requirements-test.txt; fi
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
        pip install -e .
    - name: Lint with flake8
      run: |
        # stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    - name: Test AI-Initiated Hangup
      run: |
        pytest tests/test_ai_hangup.py -v
    - name: Test Conversation Transcription
      run: |
        pytest tests/test_transcription.py -v
    - name: Test Transcription Upload
      run: |
        pytest tests/test_transcript_upload.py -v
    - name: Run Functional Integration Tests
      run: |
        pytest tests/test_functional_integration.py -v
    - name: Run All Tests
      run: |
        pytest
    - name: Run Tests with Coverage
      run: |
        pytest --cov=main --cov-config=.coveragerc tests/ --cov-report=xml
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        fail_ci_if_error: false
        verbose: true
      env:
        CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
