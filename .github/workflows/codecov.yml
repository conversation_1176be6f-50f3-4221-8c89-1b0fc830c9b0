name: Codecov Badge Update

on:
  push:
    branches: [ "main" ]

jobs:
  codecov:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python 3.13
        uses: actions/setup-python@v3
        with:
          python-version: "3.13"
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pytest pytest-asyncio httpx fastapi websockets python-dotenv pytest-cov
          if [ -f requirements-test.txt ]; then pip install -r requirements-test.txt; fi
          if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
          pip install -e .
      - name: Run Tests with Coverage
        run: |
          pytest --cov=main --cov-config=.coveragerc tests/ --cov-report=xml
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          fail_ci_if_error: false
          verbose: true
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
