#!/usr/bin/env python
"""
This module converts rendered HTML to PDF files.
"""
import os
import logging
from typing import Optional
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Default output directory for PDFs
DEFAULT_OUTPUT_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'reports')
os.makedirs(DEFAULT_OUTPUT_DIR, exist_ok=True)


def generate_pdf_from_html(
    html_content: str,
    output_file: Optional[str] = None,
    page_size: str = 'Letter'
) -> str:
    """
    Generate a PDF file from HTML content.
    
    Args:
        html_content: HTML content to convert to PDF
        output_file: Path where the PDF will be saved (optional)
        page_size: Page size for the PDF ('Letter', 'A4', etc.)
        
    Returns:
        Path to the generated PDF file
    """
    try:
        # If no output file specified, create one with a timestamp
        if not output_file:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(DEFAULT_OUTPUT_DIR, f"inspection_report_{timestamp}.pdf")
        
        # Create directories if they don't exist
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Try different PDF generation libraries in order of preference
        pdf_generated = False
        
        # Try WeasyPrint first
        try:
            import weasyprint
            weasyprint.HTML(string=html_content).write_pdf(output_file)
            pdf_generated = True
            logger.info(f"PDF generated using WeasyPrint: {output_file}")
        except ImportError:
            logger.warning("WeasyPrint not available, trying pdfkit...")
        
        # Try pdfkit next
        if not pdf_generated:
            try:
                import pdfkit
                pdfkit.from_string(
                    html_content,
                    output_file,
                    options={
                        'page-size': page_size,
                        'margin-top': '0.75in',
                        'margin-right': '0.75in',
                        'margin-bottom': '0.75in',
                        'margin-left': '0.75in',
                        'encoding': 'UTF-8',
                        'no-outline': None
                    }
                )
                pdf_generated = True
                logger.info(f"PDF generated using pdfkit: {output_file}")
            except ImportError:
                logger.warning("pdfkit not available, trying xhtml2pdf...")
        
        # Try xhtml2pdf as a last resort
        if not pdf_generated:
            try:
                from xhtml2pdf import pisa
                with open(output_file, "wb") as f:
                    pisa.CreatePDF(html_content, dest=f)
                pdf_generated = True
                logger.info(f"PDF generated using xhtml2pdf: {output_file}")
            except ImportError:
                logger.error("No PDF generation library available. Install WeasyPrint, pdfkit, or xhtml2pdf.")
                raise ImportError("No PDF generation library available. Install WeasyPrint, pdfkit, or xhtml2pdf.")
        
        if not pdf_generated:
            raise RuntimeError("Failed to generate PDF.")
            
        return output_file
    
    except Exception as e:
        logger.error(f"Error generating PDF: {e}")
        raise


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Generate PDF from HTML")
    parser.add_argument("--html", required=True, help="Path to HTML file or HTML content")
    parser.add_argument("--output", help="Output PDF file path")
    parser.add_argument("--page-size", default="Letter", help="Page size (Letter, A4, etc.)")
    args = parser.parse_args()
    
    try:
        # Check if the input is a file path or HTML content
        if os.path.isfile(args.html):
            with open(args.html, 'r', encoding='utf-8') as f:
                html_content = f.read()
            logger.info(f"Read HTML from file: {args.html}")
        else:
            html_content = args.html
            logger.info("Using provided HTML content")
        
        output_file = generate_pdf_from_html(
            html_content,
            output_file=args.output,
            page_size=args.page_size
        )
        
        print(f"PDF generated: {output_file}")
    
    except Exception as e:
        logger.error(f"Error running PDF generator: {e}")
        raise