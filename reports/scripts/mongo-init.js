// MongoDB initialization script
db = db.getSiblingDB('inspection_reports');

// Create collections
db.createCollection('inspections');

// Create indexes for better performance
db.inspections.createIndex({ "report_generation_datetime": -1 });
db.inspections.createIndex({ "vehicle_owner": 1 });
db.inspections.createIndex({ "unit_number": 1 });
db.inspections.createIndex({ "car_type": 1 });
db.inspections.createIndex({ "inspection_date_vehicle": -1 });

// Insert sample data for testing
db.inspections.insertMany([
  {
    "_id": ObjectId(),
    "report_title": "Vehicle Inspection Report",
    "vehicle_info_title": "Vehicle Information",
    "checklist_title": "Inspection Checklist",
    "signoff_title": "Inspector Sign-off",
    "vehicle_owner": "<PERSON>",
    "client_email": "<EMAIL>",
    "unit_number": "UNIT-12345",
    "car_type": "Light-Duty Vehicle",
    "odometer_reading": "75,450 mi",
    "make_model_year": "Ford F-150 / 2020",
    "inspection_date_vehicle": "2025-06-18",
    "report_generation_datetime": "2025-06-18 14:30:25",
    "service_level": "Standard",
    "checklist_items": [
      {
        "item": "Door locks",
        "status": "OK",
        "notes": "All door locks functional and secure"
      },
      {
        "item": "Brakes",
        "status": "GOOD",
        "notes": "Brake pads have good thickness, no grinding"
      },
      {
        "item": "Tires",
        "status": "FAIR",
        "notes": "Front tires showing some wear, rear tires good"
      }
    ],
    "inspector_name": "Mike Johnson",
    "signature_url": "https://via.placeholder.com/105x30?text=Signature",
    "signoff_date": "2025-06-18",
    "signoff_location": "Denver, CO",
    "download_button_text": "Download PDF",
    "footer_line1": "Report generated by WFS Inspection System.",
    "footer_support_email": "<EMAIL>"
  },
  {
    "_id": ObjectId(),
    "report_title": "Vehicle Inspection Report",
    "vehicle_info_title": "Vehicle Information", 
    "checklist_title": "Inspection Checklist",
    "signoff_title": "Inspector Sign-off",
    "vehicle_owner": "Sarah Davis",
    "client_email": "<EMAIL>",
    "unit_number": "UNIT-67890",
    "car_type": "Power-Unit/Tractor",
    "odometer_reading": "245,670 mi",
    "make_model_year": "Kenworth T680 / 2019",
    "inspection_date_vehicle": "2025-06-18",
    "report_generation_datetime": "2025-06-18 15:45:12",
    "service_level": "Premium",
    "checklist_items": [
      {
        "item": "Air brakes",
        "status": "OK",
        "notes": "Air pressure holding, no leaks detected"
      },
      {
        "item": "Engine oil",
        "status": "GOOD",
        "notes": "Oil level good, no contamination"
      }
    ],
    "inspector_name": "Tom Wilson",
    "signature_url": "https://via.placeholder.com/105x30?text=Signature",
    "signoff_date": "2025-06-18",
    "signoff_location": "Remote Inspection",
    "download_button_text": "Download PDF",
    "footer_line1": "Report generated by WFS Inspection System.",
    "footer_support_email": "<EMAIL>"
  }
]);

print('Database initialized with sample inspection data');