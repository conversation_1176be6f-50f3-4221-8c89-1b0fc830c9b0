const { MongoClient } = require('mongodb');
require('dotenv').config();

const MONGODB_URI = process.env.MONGODB_URI || '*******************************************************************************';
const DATABASE_NAME = process.env.MONGO_INITDB_DATABASE || 'inspection_reports';
const ENABLE_SAMPLE_DATA = process.env.ENABLE_SAMPLE_DATA !== 'false';

async function initializeDatabase() {
  let client;
  
  try {
    console.log('🔄 Connecting to MongoDB...');
    client = new MongoClient(MONGODB_URI);
    await client.connect();
    
    const db = client.db(DATABASE_NAME);
    console.log(`✅ Connected to MongoDB (Database: ${DATABASE_NAME})`);

    // Drop existing collection if it exists
    try {
      await db.collection('inspections').drop();
      console.log('🗑️ Dropped existing inspections collection');
    } catch (error) {
      // Collection doesn't exist, which is fine
    }

    // Create collection
    await db.createCollection('inspections');
    console.log('📁 Created inspections collection');

    // Create indexes for better performance
    await db.collection('inspections').createIndex({ "report_generation_datetime": -1 });
    await db.collection('inspections').createIndex({ "vehicle_owner": 1 });
    await db.collection('inspections').createIndex({ "unit_number": 1 });
    await db.collection('inspections').createIndex({ "car_type": 1 });
    await db.collection('inspections').createIndex({ "inspection_date_vehicle": -1 });
    console.log('🔍 Created database indexes');

    // Generate sample data with various dates
    const sampleInspections = [
      {
        "report_title": "Vehicle Inspection Report",
        "vehicle_info_title": "Vehicle Information",
        "checklist_title": "Inspection Checklist",
        "signoff_title": "Inspector Sign-off",
        "vehicle_owner": "John Smith",
        "client_email": "<EMAIL>",
        "unit_number": "UNIT-12345",
        "car_type": "Light-Duty Vehicle",
        "odometer_reading": "75,450 mi",
        "make_model_year": "Ford F-150 / 2020",
        "vehicle_make": "Ford",
        "vehicle_model": "F-150",
        "vehicle_year": "2020",
        "inspection_date_vehicle": new Date().toISOString().split('T')[0], // Today
        "report_generation_datetime": new Date().toISOString().slice(0, 19).replace('T', ' '),
        "service_level": "Standard",
        "checklist_items": [
          { "item": "Door locks", "status": "OK", "notes": "All door locks functional and secure" },
          { "item": "Brakes", "status": "GOOD", "notes": "Brake pads have good thickness, no grinding" },
          { "item": "Tires", "status": "FAIR", "notes": "Front tires showing some wear, rear tires good" },
          { "item": "Engine oil", "status": "OK", "notes": "Oil level good, color acceptable" },
          { "item": "Lights", "status": "GOOD", "notes": "All lights working properly" }
        ],
        "inspector_name": "Mike Johnson",
        "signature_url": "https://via.placeholder.com/105x30?text=Signature",
        "signoff_date": new Date().toISOString().split('T')[0],
        "signoff_location": "Denver, CO",
        "download_button_text": "Download PDF",
        "footer_line1": "Report generated by WFS Inspection System.",
        "footer_support_email": "<EMAIL>"
      },
      {
        "report_title": "Vehicle Inspection Report",
        "vehicle_info_title": "Vehicle Information",
        "checklist_title": "Inspection Checklist",
        "signoff_title": "Inspector Sign-off",
        "vehicle_owner": "Sarah Davis",
        "client_email": "<EMAIL>",
        "unit_number": "UNIT-67890",
        "car_type": "Power-Unit/Tractor",
        "odometer_reading": "245,670 mi",
        "make_model_year": "Kenworth T680 / 2019",
        "vehicle_make": "Kenworth",
        "vehicle_model": "T680",
        "vehicle_year": "2019",
        "inspection_date_vehicle": new Date(Date.now() - 86400000).toISOString().split('T')[0], // Yesterday
        "report_generation_datetime": new Date(Date.now() - 86400000).toISOString().slice(0, 19).replace('T', ' '),
        "service_level": "Premium",
        "checklist_items": [
          { "item": "Air brakes", "status": "OK", "notes": "Air pressure holding, no leaks detected" },
          { "item": "Engine oil", "status": "GOOD", "notes": "Oil level good, no contamination" },
          { "item": "Transmission", "status": "OK", "notes": "Shifting smoothly, no leaks" },
          { "item": "Suspension", "status": "POOR", "notes": "Air bags showing wear, needs attention" }
        ],
        "inspector_name": "Tom Wilson",
        "signature_url": "https://via.placeholder.com/105x30?text=Signature",
        "signoff_date": new Date(Date.now() - 86400000).toISOString().split('T')[0],
        "signoff_location": "Remote Inspection",
        "download_button_text": "Download PDF",
        "footer_line1": "Report generated by WFS Inspection System.",
        "footer_support_email": "<EMAIL>"
      },
      {
        "report_title": "Vehicle Inspection Report",
        "vehicle_info_title": "Vehicle Information",
        "checklist_title": "Inspection Checklist",
        "signoff_title": "Inspector Sign-off",
        "vehicle_owner": "Mike Rodriguez",
        "client_email": "<EMAIL>",
        "unit_number": "UNIT-55432",
        "car_type": "Trailer",
        "odometer_reading": "N/A",
        "make_model_year": "Great Dane Dry Van / 2021",
        "vehicle_make": "Great Dane",
        "vehicle_model": "Dry Van",
        "vehicle_year": "2021",
        "inspection_date_vehicle": new Date(Date.now() - 172800000).toISOString().split('T')[0], // 2 days ago
        "report_generation_datetime": new Date(Date.now() - 172800000).toISOString().slice(0, 19).replace('T', ' '),
        "service_level": "Standard",
        "checklist_items": [
          { "item": "Brake system", "status": "OK", "notes": "Service brakes operating correctly" },
          { "item": "Tires", "status": "GOOD", "notes": "All tires in good condition, proper pressure" },
          { "item": "Lights", "status": "OK", "notes": "All marker and brake lights functional" },
          { "item": "Landing gear", "status": "GOOD", "notes": "Raises and lowers smoothly" },
          { "item": "Kingpin", "status": "OK", "notes": "No abnormal wear detected" }
        ],
        "inspector_name": "Lisa Chen",
        "signature_url": "https://via.placeholder.com/105x30?text=Signature",
        "signoff_date": new Date(Date.now() - 172800000).toISOString().split('T')[0],
        "signoff_location": "Phoenix, AZ",
        "download_button_text": "Download PDF",
        "footer_line1": "Report generated by WFS Inspection System.",
        "footer_support_email": "<EMAIL>"
      },
      {
        "report_title": "Vehicle Inspection Report",
        "vehicle_info_title": "Vehicle Information",
        "checklist_title": "Inspection Checklist",
        "signoff_title": "Inspector Sign-off",
        "vehicle_owner": "Emma Thompson",
        "client_email": "<EMAIL>",
        "unit_number": "UNIT-98765",
        "car_type": "Liftgate/Forklift",
        "odometer_reading": "3,245 hrs",
        "make_model_year": "Hyster H80FT / 2018",
        "vehicle_make": "Hyster",
        "vehicle_model": "H80FT",
        "vehicle_year": "2018",
        "inspection_date_vehicle": new Date(Date.now() - 604800000).toISOString().split('T')[0], // 1 week ago
        "report_generation_datetime": new Date(Date.now() - 604800000).toISOString().slice(0, 19).replace('T', ' '),
        "service_level": "Premium",
        "checklist_items": [
          { "item": "Hydraulic system", "status": "OK", "notes": "No leaks, operates smoothly" },
          { "item": "Platform condition", "status": "GOOD", "notes": "Surface intact, no damage" },
          { "item": "Safety chains", "status": "OK", "notes": "Chains intact, latches functional" },
          { "item": "Warning systems", "status": "GOOD", "notes": "All warning lights and alarms working" },
          { "item": "Emergency stop", "status": "OK", "notes": "Emergency stop functions properly" }
        ],
        "inspector_name": "David Park",
        "signature_url": "https://via.placeholder.com/105x30?text=Signature",
        "signoff_date": new Date(Date.now() - 604800000).toISOString().split('T')[0],
        "signoff_location": "Seattle, WA",
        "download_button_text": "Download PDF",
        "footer_line1": "Report generated by WFS Inspection System.",
        "footer_support_email": "<EMAIL>"
      },
      {
        "report_title": "Vehicle Inspection Report",
        "vehicle_info_title": "Vehicle Information",
        "checklist_title": "Inspection Checklist",
        "signoff_title": "Inspector Sign-off",
        "vehicle_owner": "Robert Johnson",
        "client_email": "<EMAIL>",
        "unit_number": "UNIT-77123",
        "car_type": "Hy-Rail/Heavy-Duty",
        "odometer_reading": "128,990 mi",
        "make_model_year": "Ford F-750 Hy-Rail / 2020",
        "vehicle_make": "Ford",
        "vehicle_model": "F-750 Hy-Rail",
        "vehicle_year": "2020",
        "inspection_date_vehicle": new Date(Date.now() - 1209600000).toISOString().split('T')[0], // 2 weeks ago
        "report_generation_datetime": new Date(Date.now() - 1209600000).toISOString().slice(0, 19).replace('T', ' '),
        "service_level": "Standard",
        "checklist_items": [
          { "item": "Hy-rail gear", "status": "OK", "notes": "Deploy and retract mechanisms working" },
          { "item": "Rail brake", "status": "GOOD", "notes": "Brake engages and releases properly" },
          { "item": "Guide wheels", "status": "FAIR", "notes": "Some wear on flanges, monitor closely" },
          { "item": "Outriggers", "status": "OK", "notes": "Deploy and hold load correctly" },
          { "item": "Hydraulic system", "status": "GOOD", "notes": "No leaks, proper pressure" }
        ],
        "inspector_name": "Amanda White",
        "signature_url": "https://via.placeholder.com/105x30?text=Signature",
        "signoff_date": new Date(Date.now() - 1209600000).toISOString().split('T')[0],
        "signoff_location": "Chicago, IL",
        "download_button_text": "Download PDF",
        "footer_line1": "Report generated by WFS Inspection System.",
        "footer_support_email": "<EMAIL>"
      }
    ];

    // Insert sample data (if enabled)
    if (ENABLE_SAMPLE_DATA) {
      const result = await db.collection('inspections').insertMany(sampleInspections);
      console.log(`📋 Inserted ${result.insertedCount} sample inspection reports`);
    } else {
      console.log('⏭️  Skipping sample data insertion (ENABLE_SAMPLE_DATA=false)');
    }

    console.log('🎉 Database initialization completed successfully!');
    console.log('');
    console.log('🚀 You can now start the application with: npm start');
    console.log('🌐 Dashboard will be available at: http://localhost:5003');
    
  } catch (error) {
    console.error('❌ Error initializing database:', error);
    process.exit(1);
  } finally {
    if (client) {
      await client.close();
    }
  }
}

initializeDatabase();