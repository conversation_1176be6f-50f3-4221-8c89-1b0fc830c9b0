const express = require('express');
const { MongoClient } = require('mongodb');
const puppeteer = require('puppeteer');
const cors = require('cors');
const path = require('path');
const twilio = require('twilio');
require('dotenv').config();

const app = express();

// Environment variables with defaults
const PORT = process.env.PORT || 5003;
const MONGODB_URI = process.env.MONGODB_URI || '*******************************************************************************';
const NODE_ENV = process.env.NODE_ENV || 'development';
const CORS_ORIGINS = process.env.CORS_ORIGINS || '*';
const DEBUG_MODE = process.env.DEBUG_MODE === 'true';

// Middleware
const corsOptions = {
  origin: CORS_ORIGINS === '*' ? true : CORS_ORIGINS.split(',').map(origin => origin.trim()),
  credentials: true
};

app.use(cors(corsOptions));
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Request logging middleware
if (process.env.ENABLE_REQUEST_LOGGING !== 'false') {
  app.use((req, res, next) => {
    const timestamp = new Date().toISOString();
    console.log(`${timestamp} - ${req.method} ${req.path}`);
    next();
  });
}

// MongoDB connection
let db;
let client;

async function connectToDatabase() {
  try {
    const mongoOptions = {
      connectTimeoutMS: parseInt(process.env.MONGODB_CONNECT_TIMEOUT) || 30000,
      serverSelectionTimeoutMS: parseInt(process.env.MONGODB_SERVER_SELECTION_TIMEOUT) || 5000,
      maxPoolSize: parseInt(process.env.MONGODB_MAX_POOL_SIZE) || 10,
      minPoolSize: parseInt(process.env.MONGODB_MIN_POOL_SIZE) || 5,
    };

    if (DEBUG_MODE) {
      console.log('🔄 Connecting to MongoDB with URI:', MONGODB_URI.replace(/\/\/.*@/, '//***:***@'));
      console.log('🔄 MongoDB options:', mongoOptions);
    }

    client = new MongoClient(MONGODB_URI, mongoOptions);
    await client.connect();
    
    // Extract database name from URI or use default
    const dbName = process.env.MONGO_INITDB_DATABASE || 'inspection_reports';
    db = client.db(dbName);
    
    console.log(`✅ Connected to MongoDB (Database: ${dbName})`);
    
    if (DEBUG_MODE) {
      const stats = await db.stats();
      console.log('📊 Database stats:', { collections: stats.collections, dataSize: stats.dataSize });
    }
  } catch (error) {
    console.error('❌ Error connecting to MongoDB:', error);
    if (DEBUG_MODE) {
      console.error('🔍 Connection details:', {
        uri: MONGODB_URI.replace(/\/\/.*@/, '//***:***@'),
        nodeEnv: NODE_ENV
      });
    }
    process.exit(1);
  }
}

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Twilio token endpoint for phone functionality
app.get('/token', (req, res) => {
  try {
    // Generate unique session identifier to prevent conversation caching
    const sessionId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const AccessToken = twilio.jwt.AccessToken;
    const VoiceGrant = AccessToken.VoiceGrant;
    
    const token = new AccessToken(
      process.env.TWILIO_ACCOUNT_SID,
      process.env.TWILIO_API_KEY,
      process.env.TWILIO_API_SECRET,
      {
        identity: sessionId // Unique identity per call to prevent session reuse
      }
    );

    const voiceGrant = new VoiceGrant({
      outgoingApplicationSid: process.env.TWILIO_TWIML_APP_SID,
      incomingAllow: true
    });

    token.addGrant(voiceGrant);

    console.log(`🔑 Generated token for session: ${sessionId}`);
    res.send({ 
      token: token.toJwt(),
      sessionId: sessionId // Include session ID for debugging
    });
  } catch (err) {
    console.error('❌ Token generation failed:', err.message);
    res.status(500).send({ error: err.message });
  }
});

// API endpoint to get all inspections
app.get('/api/inspections', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const skip = (page - 1) * limit;

    const inspections = await db.collection('inspections')
      .find({})
      .sort({ report_generation_datetime: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    const total = await db.collection('inspections').countDocuments();

    res.json({
      inspections,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching inspections:', error);
    res.status(500).json({ error: 'Failed to fetch inspections' });
  }
});

// API endpoint to get a single inspection
app.get('/api/inspections/:id', async (req, res) => {
  try {
    const { ObjectId } = require('mongodb');
    const inspection = await db.collection('inspections')
      .findOne({ _id: new ObjectId(req.params.id) });

    if (!inspection) {
      return res.status(404).json({ error: 'Inspection not found' });
    }

    res.json(inspection);
  } catch (error) {
    console.error('Error fetching inspection:', error);
    res.status(500).json({ error: 'Failed to fetch inspection' });
  }
});

// Debug endpoint to check data and template rendering
app.get('/api/inspections/:id/debug', async (req, res) => {
  try {
    const { ObjectId } = require('mongodb');
    const inspection = await db.collection('inspections')
      .findOne({ _id: new ObjectId(req.params.id) });

    if (!inspection) {
      return res.status(404).json({ error: 'Inspection not found' });
    }

    res.json({
      inspection_data: inspection,
      sample_replacements: {
        report_title: inspection.report_title,
        vehicle_owner: inspection.vehicle_owner,
        unit_number: inspection.unit_number,
        inspector_name: inspection.inspector_name
      }
    });

  } catch (error) {
    console.error('Error in debug endpoint:', error);
    res.status(500).json({ error: 'Debug failed' });
  }
});

// API endpoint to generate and serve PDF
app.get('/api/inspections/:id/pdf', async (req, res) => {
  try {
    const { ObjectId } = require('mongodb');
    const inspection = await db.collection('inspections')
      .findOne({ _id: new ObjectId(req.params.id) });

    if (!inspection) {
      return res.status(404).json({ error: 'Inspection not found' });
    }

    // Generate PDF using Puppeteer
    const puppeteerOptions = {
      headless: process.env.PUPPETEER_HEADLESS !== 'false' ? 'new' : false,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    };

    if (DEBUG_MODE) {
      console.log('🔄 Launching Puppeteer with options:', puppeteerOptions);
    }

    const browser = await puppeteer.launch(puppeteerOptions);

    const page = await browser.newPage();
    
    // Load the report template
    const fs = require('fs');
    const templatePath = path.join(__dirname, 'report_template.html');
    let template = fs.readFileSync(templatePath, 'utf8');

    // Simple template replacement with N/A fallbacks
    template = template.replace(/\{\{\s*data\.([^}]+)\s*\}\}/g, (match, field) => {
      const value = inspection[field];
      if (value !== undefined && value !== null && value !== '') {
        return value;
      } else {
        return 'N/A';
      }
    });

    // Handle checklist items loop
    if (inspection.checklist_items && inspection.checklist_items.length > 0) {
      const getStatusClass = (status) => {
        const statusLower = (status || '').toLowerCase();
        // Green for OK status
        if (statusLower === 'ok') return 'status-ok';
        // Yellow for non-critical issues
        if (['good', 'fair', 'acceptable', 'minor'].includes(statusLower)) return 'status-good';
        // Red for critical issues that need attention
        if (['poor', 'bad', 'repair', 'replace', 'fix', 'critical', 'failed', 'needs attention'].includes(statusLower)) return 'status-poor';
        // Gray for N/A
        if (['na', 'n/a', 'not applicable', ''].includes(statusLower)) return 'status-na';
        // Default to good (yellow) for unknown statuses
        return 'status-good';
      };

      const checklistRows = inspection.checklist_items.map(item => `
        <tr>
          <td>${item.item || 'N/A'}</td>
          <td class="${getStatusClass(item.status || 'N/A')}">${item.status || 'N/A'}</td>
          <td>${item.notes || 'N/A'}</td>
        </tr>
      `).join('');
      
      template = template.replace(
        /\{% for item in data\.checklist_items %\}.*?\{% endfor %\}/s,
        checklistRows
      );
    } else {
      // Handle case where there are no checklist items
      const emptyChecklistRow = `
        <tr>
          <td colspan="3" style="text-align: center; font-style: italic; color: var(--gray);">No inspection items available</td>
        </tr>
      `;
      
      template = template.replace(
        /\{% for item in data\.checklist_items %\}.*?\{% endfor %\}/s,
        emptyChecklistRow
      );
    }

    await page.setContent(template, { waitUntil: 'networkidle0' });
    
    const pdf = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '1cm',
        right: '1cm',
        bottom: '1cm',
        left: '1cm'
      }
    });

    await browser.close();

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `inline; filename="inspection-${inspection.unit_number}-${inspection.inspection_date_vehicle}.pdf"`);
    res.send(pdf);

  } catch (error) {
    console.error('Error generating PDF:', error);
    res.status(500).json({ error: 'Failed to generate PDF' });
  }
});

// Helper function to get nested object values
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}


// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    database: db ? 'connected' : 'disconnected'
  });
});

// Start server
async function startServer() {
  console.log('🚀 Starting Reports Dashboard...');
  console.log(`📦 Environment: ${NODE_ENV}`);
  console.log(`🐛 Debug mode: ${DEBUG_MODE ? 'enabled' : 'disabled'}`);
  
  await connectToDatabase();
  
  app.listen(PORT, () => {
    console.log(`🌐 Reports Dashboard running on http://localhost:${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/health`);
    console.log(`📋 API docs: http://localhost:${PORT}/api/inspections`);
    
    if (DEBUG_MODE) {
      console.log(`🔧 CORS origins: ${CORS_ORIGINS}`);
      console.log(`🗄️  Database: ${process.env.MONGO_INITDB_DATABASE || 'inspection_reports'}`);
    }
    
    console.log('✅ Server startup completed successfully!');
  });
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  if (client) {
    await client.close();
  }
  process.exit(0);
});

startServer().catch(console.error);