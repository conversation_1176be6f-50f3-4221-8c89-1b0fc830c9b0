#!/bin/bash

echo "🚀 Starting Inspection Reports Dashboard"
echo "========================================"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose is not installed. Please install it first."
    exit 1
fi

echo "📦 Starting containers..."
docker-compose up -d

echo "⏳ Waiting for MongoDB to be ready..."
sleep 10

echo "🗄️ Initializing database with sample data..."
docker-compose exec -T reports-app npm run init-db

echo ""
echo "✅ Dashboard is ready!"
echo "🌐 Open your browser and go to: http://localhost:5003"
echo "📊 Health check: http://localhost:5003/health"
echo "🔍 API endpoint: http://localhost:5003/api/inspections"
echo ""
echo "📝 To view logs: docker-compose logs -f reports-app"
echo "🛑 To stop: docker-compose down"
echo ""
echo "🎉 Happy inspecting!"