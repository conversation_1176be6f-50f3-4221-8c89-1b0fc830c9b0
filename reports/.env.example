# Reports Dashboard Environment Configuration
# Copy this file to .env and update the values for your environment

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
# Port for the reports dashboard server
PORT=5003

# Node environment (development, production, test)
NODE_ENV=development

# =============================================================================
# MONGODB CONFIGURATION
# =============================================================================
# MongoDB connection URI
# Format: mongodb://[username:password@]host:port/database[?options]

# For Docker Compose (container-to-container communication)
MONGODB_URI=*****************************************************************************

# For standalone/local development (connecting to external MongoDB)
# MONGODB_URI=*******************************************************************************

# For MongoDB Atlas (cloud)
# MONGODB_URI=mongodb+srv://username:<EMAIL>/inspection_reports?retryWrites=true&w=majority

# For local MongoDB without authentication
# MONGODB_URI=mongodb://localhost:27017/inspection_reports

# =============================================================================
# MONGODB CREDENTIALS (for Docker Compose setup)
# =============================================================================
# MongoDB root username (used in docker-compose.yml)
MONGO_INITDB_ROOT_USERNAME=admin

# MongoDB root password (used in docker-compose.yml)
MONGO_INITDB_ROOT_PASSWORD=password123

# MongoDB database name
MONGO_INITDB_DATABASE=inspection_reports

# =============================================================================
# MONGODB CONNECTION OPTIONS
# =============================================================================
# Connection timeout in milliseconds
MONGODB_CONNECT_TIMEOUT=30000

# Server selection timeout in milliseconds
MONGODB_SERVER_SELECTION_TIMEOUT=5000

# Maximum number of connections in the pool
MONGODB_MAX_POOL_SIZE=10

# Minimum number of connections in the pool
MONGODB_MIN_POOL_SIZE=5

# =============================================================================
# PDF GENERATION CONFIGURATION
# =============================================================================
# Puppeteer options for PDF generation
PUPPETEER_HEADLESS=true

# PDF generation timeout in milliseconds
PDF_GENERATION_TIMEOUT=30000

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Allowed origins for CORS (comma-separated for multiple origins)
# Use * for all origins in development (not recommended for production)
CORS_ORIGINS=*

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Log level (error, warn, info, verbose, debug)
LOG_LEVEL=info

# Enable/disable request logging
ENABLE_REQUEST_LOGGING=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# API rate limiting (requests per minute)
RATE_LIMIT_REQUESTS_PER_MINUTE=100

# Enable/disable API authentication (if implemented)
ENABLE_API_AUTH=false

# API key for authentication (if enabled)
# API_KEY=your-secret-api-key-here

# =============================================================================
# TWILIO CONFIGURATION (for phone functionality)
# =============================================================================
# Twilio Account SID
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Twilio API Key
TWILIO_API_KEY=SKxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Twilio API Secret
TWILIO_API_SECRET=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Twilio TwiML Application SID
TWILIO_TWIML_APP_SID=APxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
# Enable hot reload in development
ENABLE_HOT_RELOAD=true

# Enable debug mode for additional logging
DEBUG_MODE=false

# Enable sample data generation on startup
ENABLE_SAMPLE_DATA=true

# =============================================================================
# PRODUCTION CONFIGURATION
# =============================================================================
# Trust proxy (for production behind reverse proxy)
TRUST_PROXY=false

# Helmet security options
ENABLE_HELMET_SECURITY=false

# Enable compression middleware
ENABLE_COMPRESSION=false

# =============================================================================
# EXAMPLE CONFIGURATIONS FOR DIFFERENT ENVIRONMENTS
# =============================================================================

# DEVELOPMENT SETUP (local MongoDB with Docker Compose):
# PORT=5003
# NODE_ENV=development
# MONGODB_URI=*******************************************************************************
# DEBUG_MODE=true
# ENABLE_SAMPLE_DATA=true

# PRODUCTION SETUP (MongoDB Atlas):
# PORT=5003
# NODE_ENV=production
# MONGODB_URI=mongodb+srv://username:<EMAIL>/inspection_reports?retryWrites=true&w=majority
# ENABLE_HELMET_SECURITY=true
# ENABLE_COMPRESSION=true
# CORS_ORIGINS=https://yourdomain.com
# DEBUG_MODE=false
# ENABLE_SAMPLE_DATA=false

# STANDALONE LOCAL SETUP (local MongoDB instance):
# PORT=5003
# NODE_ENV=development
# MONGODB_URI=mongodb://localhost:27017/inspection_reports
# DEBUG_MODE=false
# ENABLE_SAMPLE_DATA=true