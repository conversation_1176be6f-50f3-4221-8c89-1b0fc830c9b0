# CLAUDE.md - Reports Dashboard Module

This file provides guidance when working with the Reports Dashboard module of the Voice Assistant system.

## Project Overview

This is the **Reports Dashboard Module** for the Voice Assistant Vehicle Inspection system. It provides:
- **Real-time inspection reports dashboard** with live data polling
- **PDF generation** for professional inspection reports
- **MongoDB integration** for inspection data storage
- **Twilio phone interface** for making calls to AI assistant
- **Unified WorkForce AI Assistant interface** combining phone and dashboard functionality

## Technical Stack

- **Node.js/Express** server for dashboard and API endpoints
- **MongoDB** for inspection data storage with client-centric architecture
- **Puppeteer** for PDF generation with professional templates
- **Twilio SDK** for phone call functionality and token management
- **Tailwind CSS** for responsive UI styling
- **Docker** containerization for deployment

## Architecture

### Database Schema
- **Collection**: `inspections` 
- **Key Fields**: 
  - `vehicle_owner`, `unit_number`, `car_type`
  - `vehicle_make`, `vehicle_model`, `vehicle_year` (separated from `make_model_year`)
  - `checklist_items` with color-coded status system
  - `inspector_name`, `inspection_date_vehicle`

### API Endpoints
- `GET /` - Main dashboard interface
- `GET /token` - Twilio authentication token for phone calls
- `GET /api/inspections` - List all inspections with pagination
- `GET /api/inspections/:id` - Get single inspection
- `GET /api/inspections/:id/pdf` - Generate and serve PDF report
- `GET /health` - Health check endpoint

### Features Implemented
- **Color-coded Status System**: OK (green), Good/Fair (yellow), Poor/Critical (red), N/A (gray)
- **Professional PDF Templates**: Clean vehicle inspection reports with proper titles
- **Real-time Dashboard**: Live polling every 5 seconds for new inspections
- **Phone Integration**: Full Twilio calling functionality embedded in dashboard
- **Responsive Design**: Works on desktop and mobile devices

## Configuration

### Environment Variables (.env)
```
PORT=5003
MONGODB_URI=*******************************************************************************
NODE_ENV=development
DEBUG_MODE=false

# Twilio Configuration
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_API_KEY=SKxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_API_SECRET=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_TWIML_APP_SID=APxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

### Docker Setup
- **Container**: reports-dashboard (port 5003)
- **Database**: reports-mongodb (port 27018)
- **Network**: reports_reports-network

## Development Workflow

### Quick Start
```bash
# Install dependencies
npm install

# Start development
npm run dev

# Initialize database with sample data  
npm run init-db

# Build and run with Docker
docker compose up -d
```

### Database Management
```bash
# Initialize with sample data
docker exec reports-dashboard npm run init-db

# Access MongoDB shell
make mongo-shell

# View logs
docker logs reports-dashboard
```

## Template System

### PDF Template (`report_template.html`)
- **Professional styling** with WorkForce Services branding
- **Separated vehicle fields**: Maker, Model, Year, Inspection Type
- **Color-coded checklist** items with proper CSS classes
- **N/A fallbacks** for missing data to prevent template exposure

### Template Variables
```javascript
// Vehicle Information
vehicle_owner, client_email, unit_number, odometer_reading
vehicle_make, vehicle_model, vehicle_year, car_type
inspection_date_vehicle, report_generation_datetime

// Checklist
checklist_items: [{ item, status, notes }]

// Inspector Sign-off  
inspector_name, signoff_date, signoff_location
```

## Integration Points

### Voice Assistant Integration
- Receives inspection data from main FastAPI application
- Data saved to both local database and external endpoints
- Real-time dashboard updates when new inspections arrive

### Phone Interface Integration
- **Twilio token endpoint**: `/token` generates fresh session tokens
- **Call controls**: DEV/QA/PROD environment buttons
- **Real-time status**: Call progress indicators and logging
- **Unified interface**: Phone + dashboard in single application

## Deployment Notes

### Production Considerations
- **MongoDB Atlas**: Update MONGODB_URI for cloud database
- **Environment**: Set NODE_ENV=production
- **Security**: Enable CORS restrictions, rate limiting
- **SSL**: Configure HTTPS for production deployment

### Scaling
- **Load balancing**: Multiple container instances behind proxy
- **Database**: MongoDB replica sets for high availability
- **Caching**: Redis for session management and caching

## Development History

### Major Features Added
1. **KAN-35**: PDF generation fixes and color-coded status system
2. **KAN-35**: Environment configuration and Docker containerization  
3. **KAN-35**: Comprehensive Makefile for project management
4. **KAN-35**: Voice assistant integration with reports database
5. **KAN-35**: Phone interface merger for unified WorkForce AI Assistant
6. **KAN-35**: Vehicle information template enhancements with separated fields

### Current State
- ✅ Unified phone + dashboard interface
- ✅ Professional PDF report generation
- ✅ Real-time data synchronization
- ✅ Complete Docker deployment setup
- ✅ Comprehensive environment configuration
- ✅ Color-coded status classification system

## Future Considerations for Module Separation

When decoupling this module from the main voice assistant project:

1. **Database Migration**: Export inspection data and schema
2. **API Integration**: Update endpoints to connect with separated voice assistant
3. **Authentication**: Implement proper API authentication between modules
4. **Configuration**: Separate environment variables and configurations
5. **Deployment**: Independent Docker deployment and CI/CD pipeline

## Working Principles

- **Data Integrity**: Always validate inspection data before PDF generation
- **Error Handling**: Graceful fallbacks for missing or malformed data
- **Performance**: Optimize database queries and PDF generation
- **User Experience**: Maintain real-time updates and responsive design
- **Code Quality**: Follow existing patterns and maintain consistent styling