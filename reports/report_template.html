<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>Vehicle Inspection Report</title>
<link href="https://fonts.googleapis.com/css?family=Montserrat:400,700&display=swap" rel="stylesheet">
<style>
    :root {
        --primary: #102a43;
        --secondary: #243b53;
        --accent: #486581;
        --bg-gradient: linear-gradient(120deg, #f0f4f8 0%, #d9e2ec 100%);
        --success: #2cb67d;
        --warning: #ff8906;
        --danger: #e63946;
        --white: #fff;
        --gray: #bfc9d1;
        --light: #f0f4f8;
        --font-main: 'Montserrat', Arial, sans-serif;
    }
    body {
        background: var(--bg-gradient);
        font-family: var(--font-main);
        margin: 0;
        padding: 0;
        color: var(--secondary);
    }
    .container {
        background: var(--white);
        max-width: 950px;
        margin: 40px auto;
        box-shadow: 0 6px 40px 0 rgba(32, 56, 88, 0.20);
        border-radius: 20px;
        padding: 38px 44px 28px 44px;
        position: relative; 
    }
    .header-section {
        display: flex;
        justify-content: space-between;
        align-items: flex-start; 
        margin-bottom: 28px;
    }
    .title {
        font-weight: 900;
        font-size: 2.1rem;
        color: var(--primary);
        letter-spacing: -1px;
        margin: 0; 
    }
    .logo-container img {
        max-height: 60px; 
        width: auto;
    }
    .section-title {
        margin: 36px 0 20px;
        font-size: 1.18rem;
        color: var(--primary);
        border-left: 5px solid var(--accent);
        padding-left: 12px;
        letter-spacing: 1px;
        font-weight: 700;
    }
    .info-table, .checklist-table, .signoff-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 8px;
    }
    .info-table th, .checklist-table th, .signoff-table th {
        text-align: left;
        color: var(--accent);
        font-size: 1rem;
        font-weight: 600;
        padding-right: 16px;
    }
    .info-table td, .checklist-table td, .signoff-table td {
        font-size: 1rem;
        padding: 6px 12px 6px 0;
        color: var(--secondary);
        vertical-align: top;
    }
    .checklist-table {
        margin-bottom: 16px;
        border-radius: 12px;
        overflow: hidden;
        background: var(--light);
    }
    .checklist-table th,
    .checklist-table td {
        padding: 10px 12px;
    }
    .checklist-table tr {
        background: var(--white);
        transition: background 0.15s;
    }
    .checklist-table tr:hover {
        background: #edf2fb;
    }
    .checklist-table .status-ok { color: var(--success); font-weight: bold; }
    .checklist-table .status-good { color: var(--warning); font-weight: bold; }
    .checklist-table .status-fair { color: var(--warning); font-weight: bold; }
    .checklist-table .status-poor { color: var(--danger); font-weight: bold; }
    .checklist-table .status-repair { color: var(--danger); font-weight: bold; }
    .checklist-table .status-replace { color: var(--danger); font-weight: bold; }
    .checklist-table .status-fix { color: var(--danger); font-weight: bold; }
    .checklist-table .status-na { color: var(--gray); font-style: italic; }
    .signoff-table td, .signoff-table th {
        padding-top: 12px;
        padding-bottom: 12px;
    }
    .button {
        display: inline-block;
        background: var(--accent);
        color: var(--white);
        padding: 7px 22px;
        border-radius: 6px;
        text-decoration: none;
        font-weight: 700;
        font-size: 1rem;
        box-shadow: 0 2px 6px 0 rgba(72, 101, 129, 0.12);
        margin-top: 10px;
        transition: background 0.2s;
    }
    .button:hover { background: var(--primary);}
    .footer {
        text-align: right;
        font-size: 0.9rem;
        color: var(--gray);
        margin-top: 26px;
    }
    @media (max-width: 700px) {
        .container {padding: 20px 24px;}
        .header-section { flex-direction: column-reverse; align-items: center; }
        .logo-container { margin-bottom: 15px; }
        .title {font-size: 1.5rem; text-align: center;}
        .section-title { font-size: 1.1rem; }
    }
    @media print {
      body {
        background: var(--white); 
      }
      .container {
        box-shadow: none;
        margin: 0;
        max-width: 100%;
        border-radius: 0;
      }
    }
</style>
</head>
<body>
<div class="container">
    <div class="header-section">
        <div class="title">Vehicle Inspection Report</div>
        <div class="logo-container">
            <img src="https://i0.wp.com/wfservices.com/wp-content/uploads/2021/10/800x196.png" alt="WorkForce Services Logo">
        </div>
    </div>

    <div class="section-title">Vehicle Information</div>
    <table class="info-table">
        <tr><th>Client Name</th><td>{{ data.vehicle_owner }}</td><th>Email</th><td>{{ data.client_email }}</td></tr>
        <tr><th>Unit Number</th><td>{{ data.unit_number }}</td><th>Odometer Reading</th><td>{{ data.odometer_reading }}</td></tr>
        <tr><th>Maker</th><td>{{ data.vehicle_make }}</td><th>Model</th><td>{{ data.vehicle_model }}</td></tr>
        <tr><th>Year</th><td>{{ data.vehicle_year }}</td><th>Inspection Type</th><td>{{ data.car_type }}</td></tr>
        <tr><th>Inspection Date</th><td>{{ data.inspection_date_vehicle }}</td><th>Report Generated</th><td>{{ data.report_generation_datetime }}</td></tr>
    </table>

    <div class="section-title">Inspection Checklist</div>
    <table class="checklist-table">
        <tr><th>Item</th><th>Status</th><th>Notes</th></tr>
        {% for item in data.checklist_items %}
        <tr>
            <td>{{ item.item }}</td>
            <td class="{{ item.status_css_class }}">{{ item.status }}</td>
            <td>{{ item.notes }}</td>
        </tr>
        {% endfor %}
    </table>

    <div class="section-title">Inspector Sign-off</div>
    <table class="signoff-table">
        <tr>
            <th>Inspector Name</th>
            <td>{{ data.inspector_name }}</td>
            <th>Signature</th>
            <td style="border-bottom:1px solid var(--gray);height:30px;padding:5px 0;font-style:italic;">Inspector</td>
        </tr>
        <tr>
            <th>Date</th>
            <td>{{ data.signoff_date }}</td>
            <th>Location</th>
            <td>{{ data.signoff_location }}</td>
        </tr>
    </table>
    <div class="footer">
        Report generated by WorkForce AI Assistant<br>
        For support, contact <a href="mailto:<EMAIL>" style="color: #486581; text-decoration:underline;"><EMAIL></a>
    </div>
</div>
</body>
</html>
