# Vehicle Inspection Reports Dashboard - Makefile
# Simplifies common development and deployment tasks

.PHONY: help setup start stop restart logs logs-follow clean build dev test init-db reset-db backup restore status health check-env lint format

# Default target
help: ## Show this help message
	@echo "Vehicle Inspection Reports Dashboard - Available Commands:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "Examples:"
	@echo "  make setup     # Complete setup with Docker Compose"
	@echo "  make dev       # Start in development mode"
	@echo "  make logs      # View application logs"
	@echo "  make reset-db  # Reset database with fresh sample data"

# =============================================================================
# SETUP AND INSTALLATION
# =============================================================================

setup: ## Complete setup: build, start services, and initialize database
	@echo "🚀 Setting up Vehicle Inspection Reports Dashboard..."
	@if [ ! -f .env ]; then cp .env.example .env && echo "📄 Created .env file from template"; fi
	docker compose up -d
	@echo "⏳ Waiting for services to start..."
	@sleep 10
	$(MAKE) init-db
	@echo "✅ Setup completed! Dashboard available at http://localhost:5003"

check-env: ## Check if .env file exists and create from template if needed
	@if [ ! -f .env ]; then \
		echo "📄 Creating .env file from template..."; \
		cp .env.example .env; \
		echo "✅ .env file created. Please review and modify as needed."; \
	else \
		echo "✅ .env file exists"; \
	fi

install: ## Install Node.js dependencies (for standalone setup)
	@echo "📦 Installing Node.js dependencies..."
	npm install
	@echo "✅ Dependencies installed"

# =============================================================================
# DOCKER COMPOSE OPERATIONS
# =============================================================================

start: ## Start all services with Docker Compose
	@echo "🚀 Starting services..."
	docker compose up -d
	@echo "✅ Services started"
	$(MAKE) status

stop: ## Stop all services
	@echo "🛑 Stopping services..."
	docker compose down
	@echo "✅ Services stopped"

restart: ## Restart all services
	@echo "🔄 Restarting services..."
	docker compose restart
	@echo "✅ Services restarted"

restart-app: ## Restart only the reports application
	@echo "🔄 Restarting reports application..."
	docker compose restart reports-app
	@echo "✅ Reports application restarted"

restart-db: ## Restart only the MongoDB database
	@echo "🔄 Restarting MongoDB..."
	docker compose restart mongodb
	@echo "✅ MongoDB restarted"

build: ## Build Docker images
	@echo "🔨 Building Docker images..."
	docker compose build
	@echo "✅ Images built"

rebuild: ## Rebuild Docker images without cache
	@echo "🔨 Rebuilding Docker images..."
	docker compose build --no-cache
	@echo "✅ Images rebuilt"

# =============================================================================
# DEVELOPMENT
# =============================================================================

dev: ## Start in development mode (standalone)
	@echo "🛠️  Starting in development mode..."
	@$(MAKE) check-env
	npm run dev

dev-docker: ## Start with Docker and enable debug mode
	@echo "🛠️  Starting in development mode with Docker..."
	@if [ ! -f .env ]; then cp .env.example .env; fi
	@echo "DEBUG_MODE=true" >> .env
	@echo "NODE_ENV=development" >> .env
	docker compose up -d
	@echo "✅ Development mode started with debugging enabled"

# =============================================================================
# LOGGING AND MONITORING
# =============================================================================

logs: ## Show recent logs from all services
	docker compose logs --tail=50

logs-follow: ## Follow logs from all services in real-time
	docker compose logs -f

logs-app: ## Show recent logs from reports application only
	docker compose logs --tail=50 reports-app

logs-app-follow: ## Follow logs from reports application in real-time
	docker compose logs -f reports-app

logs-db: ## Show recent logs from MongoDB only
	docker compose logs --tail=50 mongodb

logs-db-follow: ## Follow logs from MongoDB in real-time
	docker compose logs -f mongodb

status: ## Show status of all services
	@echo "📊 Service Status:"
	docker compose ps
	@echo ""
	@echo "🌐 Application URLs:"
	@echo "  Dashboard:    http://localhost:5003"
	@echo "  Health Check: http://localhost:5003/health"
	@echo "  API:          http://localhost:5003/api/inspections"

health: ## Check application health
	@echo "🏥 Checking application health..."
	@curl -s http://localhost:5003/health | jq '.' || echo "❌ Health check failed"

# =============================================================================
# DATABASE OPERATIONS
# =============================================================================

init-db: ## Initialize database with sample data
	@echo "🗄️  Initializing database..."
	docker compose exec reports-app npm run init-db
	@echo "✅ Database initialized"

reset-db: ## Reset database (drop and recreate with sample data)
	@echo "🗄️  Resetting database..."
	docker compose exec reports-app npm run init-db
	@echo "✅ Database reset completed"

mongo-shell: ## Connect to MongoDB shell
	@echo "🐚 Connecting to MongoDB shell..."
	docker exec -it reports-mongodb mongosh -u admin -p password123

mongo-stats: ## Show MongoDB database statistics
	@echo "📊 MongoDB Statistics:"
	@docker exec reports-mongodb mongosh -u admin -p password123 --eval "db.stats()" inspection_reports

backup-db: ## Backup database to ./backups directory
	@echo "💾 Creating database backup..."
	@mkdir -p backups
	@BACKUP_FILE="backups/inspection_reports_$(shell date +%Y%m%d_%H%M%S).archive"; \
	docker exec reports-mongodb mongodump --username admin --password password123 --authenticationDatabase admin --db inspection_reports --archive > $$BACKUP_FILE; \
	echo "✅ Database backed up to $$BACKUP_FILE"

restore-db: ## Restore database from backup (set BACKUP_FILE=path/to/backup.archive)
	@if [ -z "$(BACKUP_FILE)" ]; then \
		echo "❌ Please specify BACKUP_FILE=path/to/backup.archive"; \
		exit 1; \
	fi
	@echo "📥 Restoring database from $(BACKUP_FILE)..."
	@docker exec -i reports-mongodb mongorestore --username admin --password password123 --authenticationDatabase admin --db inspection_reports --archive < $(BACKUP_FILE)
	@echo "✅ Database restored from $(BACKUP_FILE)"

# =============================================================================
# TESTING AND QUALITY
# =============================================================================

test: ## Run tests (if available)
	@echo "🧪 Running tests..."
	npm test 2>/dev/null || echo "⚠️  No tests configured"

test-api: ## Test API endpoints
	@echo "🧪 Testing API endpoints..."
	@echo "Testing health endpoint..."
	@curl -s http://localhost:5003/health | jq '.status' || echo "❌ Health endpoint failed"
	@echo "Testing inspections endpoint..."
	@curl -s http://localhost:5003/api/inspections | jq '.pagination.total' || echo "❌ Inspections endpoint failed"
	@echo "✅ API tests completed"

test-pdf: ## Test PDF generation
	@echo "🧪 Testing PDF generation..."
	@FIRST_ID=$$(curl -s http://localhost:5003/api/inspections | jq -r '.inspections[0]._id'); \
	if [ "$$FIRST_ID" != "null" ] && [ "$$FIRST_ID" != "" ]; then \
		curl -s "http://localhost:5003/api/inspections/$$FIRST_ID/pdf" -o /tmp/test_report.pdf; \
		if [ -f /tmp/test_report.pdf ] && [ -s /tmp/test_report.pdf ]; then \
			echo "✅ PDF generation test passed"; \
		else \
			echo "❌ PDF generation test failed"; \
		fi; \
	else \
		echo "❌ No inspection data found for PDF test"; \
	fi

lint: ## Run linting (if configured)
	@echo "🔍 Running linter..."
	npm run lint 2>/dev/null || echo "⚠️  No linter configured"

format: ## Format code (if configured)
	@echo "✨ Formatting code..."
	npm run format 2>/dev/null || echo "⚠️  No formatter configured"

# =============================================================================
# CLEANUP
# =============================================================================

clean: ## Clean up Docker containers, images, and volumes
	@echo "🧹 Cleaning up..."
	docker compose down -v
	docker system prune -f
	@echo "✅ Cleanup completed"

clean-all: ## Remove everything including Docker images
	@echo "🧹 Removing all Docker resources..."
	docker compose down -v --rmi all
	docker system prune -af
	@echo "✅ Complete cleanup finished"

clean-logs: ## Clear Docker logs
	@echo "🧹 Clearing Docker logs..."
	@docker compose down
	@docker system prune -f
	@docker compose up -d
	@echo "✅ Logs cleared"

# =============================================================================
# DEPLOYMENT
# =============================================================================

deploy-dev: ## Deploy to development environment
	@echo "🚀 Deploying to development..."
	@$(MAKE) check-env
	@$(MAKE) build
	@$(MAKE) start
	@$(MAKE) init-db
	@echo "✅ Development deployment completed"

deploy-prod: ## Deploy to production environment
	@echo "🚀 Deploying to production..."
	@if [ ! -f .env ]; then echo "❌ .env file required for production"; exit 1; fi
	@grep -q "NODE_ENV=production" .env || echo "⚠️  Consider setting NODE_ENV=production in .env"
	@$(MAKE) build
	@$(MAKE) start
	@echo "✅ Production deployment completed"

# =============================================================================
# UTILITIES
# =============================================================================

open: ## Open dashboard in default browser
	@echo "🌐 Opening dashboard..."
	@which xdg-open > /dev/null && xdg-open http://localhost:5003 || \
	 which open > /dev/null && open http://localhost:5003 || \
	 echo "Please open http://localhost:5003 in your browser"

env-example: ## Show environment variables example
	@echo "📄 Environment Variables Example:"
	@cat .env.example

ports: ## Show used ports
	@echo "🔌 Used Ports:"
	@echo "  Application: 5003"
	@echo "  MongoDB:     27018 (external), 27017 (internal)"

disk-usage: ## Show Docker disk usage
	@echo "💾 Docker Disk Usage:"
	@docker system df

containers: ## Show all containers status
	@echo "📦 Container Status:"
	@docker ps -a --filter "name=reports"

# =============================================================================
# TROUBLESHOOTING
# =============================================================================

troubleshoot: ## Run basic troubleshooting checks
	@echo "🔧 Running troubleshooting checks..."
	@echo ""
	@echo "1. Checking Docker Compose:"
	@docker compose version || echo "❌ Docker Compose not available"
	@echo ""
	@echo "2. Checking services:"
	@$(MAKE) status
	@echo ""
	@echo "3. Checking application health:"
	@$(MAKE) health
	@echo ""
	@echo "4. Checking recent logs:"
	@docker compose logs --tail=10 reports-app
	@echo ""
	@echo "✅ Troubleshooting checks completed"

reset: ## Complete reset: stop, clean, and setup again
	@echo "🔄 Performing complete reset..."
	$(MAKE) stop
	$(MAKE) clean
	$(MAKE) setup
	@echo "✅ Complete reset finished"

# =============================================================================
# DOCUMENTATION
# =============================================================================

docs: ## Show documentation links
	@echo "📚 Documentation:"
	@echo "  README:       ./README.md"
	@echo "  Environment:  ./.env.example"
	@echo "  API:          http://localhost:5003/api/inspections"
	@echo "  Health:       http://localhost:5003/health"

version: ## Show version information
	@echo "📋 Version Information:"
	@echo "  Node.js:      $$(node --version 2>/dev/null || echo 'Not installed')"
	@echo "  NPM:          $$(npm --version 2>/dev/null || echo 'Not installed')"
	@echo "  Docker:       $$(docker --version 2>/dev/null || echo 'Not installed')"
	@echo "  Docker Compose: $$(docker compose version 2>/dev/null || echo 'Not installed')"