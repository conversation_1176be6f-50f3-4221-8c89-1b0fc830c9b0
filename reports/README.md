# Vehicle Inspection Reports Dashboard

A modern web dashboard for viewing and managing vehicle inspection reports with real-time updates and PDF generation capabilities.

## Features

- 📊 Real-time dashboard with live polling every 5 seconds
- 📋 Comprehensive inspection reports viewing with color-coded status
- 📄 PDF generation and download with professional formatting
- 🔍 Search and filter capabilities
- 📱 Responsive design with Tailwind CSS
- 🐳 Docker containerization with MongoDB
- 🗄️ MongoDB integration with flexible configuration
- 🎨 Color-coded checklist items (Green=OK, Yellow=Fair/Good, Red=Critical)

## Quick Start

### Option 1: Using Makefile (Easiest)

```bash
# Complete setup with one command
make setup

# Or step by step
make check-env    # Create .env file if needed
make start        # Start all services
make init-db      # Initialize with sample data
make open         # Open dashboard in browser
```

### Option 2: Using Docker Compose (Manual)

1. **Clone and navigate to the reports directory**
```bash
cd reports/
```

2. **Create environment file**
```bash
cp .env.example .env
# Edit .env with your configuration if needed
```

3. **Start the application**
```bash
docker compose up -d
```

4. **Initialize database with sample data**
```bash
docker compose exec reports-app npm run init-db
```

5. **Access the dashboard**
Open `http://localhost:5003` in your browser

### Option 3: Standalone Setup

#### Using Makefile
```bash
make install      # Install dependencies
make check-env    # Create .env file
# Edit .env with your MongoDB connection
make dev          # Start in development mode
```

#### Manual Commands
1. **Install dependencies**
```bash
npm install
```

2. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your MongoDB connection details
```

3. **Ensure MongoDB is running**
- Local MongoDB: `mongodb://localhost:27017/inspection_reports`
- Docker MongoDB: `docker run -d -p 27017:27017 mongo:7.0`
- MongoDB Atlas: Use your Atlas connection string

4. **Initialize database (optional)**
```bash
npm run init-db
```

5. **Start the application**
```bash
npm start
```

6. **Access the dashboard**
Open `http://localhost:5003` in your browser

## Environment Configuration

Copy `.env.example` to `.env` and configure:

### Essential Variables
```bash
# Server Configuration
PORT=5003
NODE_ENV=development

# MongoDB Configuration
MONGODB_URI=*******************************************************************************

# For MongoDB Atlas
# MONGODB_URI=mongodb+srv://username:<EMAIL>/inspection_reports

# For local MongoDB without auth
# MONGODB_URI=mongodb://localhost:27017/inspection_reports
```

### Advanced Configuration
```bash
# CORS Configuration
CORS_ORIGINS=*  # Use specific domains in production

# Debug and Logging
DEBUG_MODE=false
ENABLE_REQUEST_LOGGING=true

# MongoDB Options
MONGODB_CONNECT_TIMEOUT=30000
MONGODB_MAX_POOL_SIZE=10

# PDF Generation
PUPPETEER_HEADLESS=true

# Sample Data
ENABLE_SAMPLE_DATA=true
```

## Environment Examples

### Development with Docker Compose
```bash
PORT=5003
NODE_ENV=development
MONGODB_URI=*******************************************************************************
DEBUG_MODE=true
ENABLE_SAMPLE_DATA=true
```

### Production with MongoDB Atlas
```bash
PORT=5003
NODE_ENV=production
MONGODB_URI=mongodb+srv://username:<EMAIL>/inspection_reports?retryWrites=true&w=majority
DEBUG_MODE=false
ENABLE_SAMPLE_DATA=false
CORS_ORIGINS=https://yourdomain.com
```

### Local Development
```bash
PORT=5003
NODE_ENV=development
MONGODB_URI=mongodb://localhost:27017/inspection_reports
DEBUG_MODE=false
ENABLE_SAMPLE_DATA=true
```

## Makefile Commands

The project includes a comprehensive Makefile to simplify common tasks:

### Essential Commands
```bash
make help          # Show all available commands
make setup         # Complete setup: build, start, and initialize
make start         # Start all services
make stop          # Stop all services
make restart       # Restart all services
make status        # Show service status and URLs
```

### Development Commands
```bash
make dev           # Start in development mode (standalone)
make dev-docker    # Start with Docker and debug mode
make logs          # Show recent logs
make logs-follow   # Follow logs in real-time
make health        # Check application health
```

### Database Commands
```bash
make init-db       # Initialize database with sample data
make reset-db      # Reset database completely
make backup-db     # Backup database to ./backups/
make restore-db    # Restore from backup (set BACKUP_FILE=path)
make mongo-shell   # Connect to MongoDB shell
```

### Testing Commands
```bash
make test-api      # Test API endpoints
make test-pdf      # Test PDF generation
make troubleshoot  # Run troubleshooting checks
```

### Utility Commands
```bash
make clean         # Clean up Docker resources
make open          # Open dashboard in browser
make version       # Show version information
make reset         # Complete reset and setup
```

### Examples
```bash
# Quick start
make setup

# Development workflow
make dev-docker
make logs-follow   # In another terminal

# Database operations
make backup-db
make reset-db
make restore-db BACKUP_FILE=backups/inspection_reports_20231201_120000.archive

# Troubleshooting
make troubleshoot
make logs-app
make health
```

## API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/` | Dashboard homepage |
| `GET` | `/api/inspections` | Get all inspections with pagination |
| `GET` | `/api/inspections/:id` | Get specific inspection details |
| `GET` | `/api/inspections/:id/pdf` | Generate and download PDF report |
| `GET` | `/health` | Health check endpoint |

## Color-Coded Status System

The dashboard uses an intelligent color-coding system for inspection items:

- 🟢 **Green (OK)**: Items in perfect condition
- 🟡 **Yellow (GOOD/FAIR)**: Non-critical issues, monitoring recommended
- 🔴 **Red (POOR/REPAIR/REPLACE)**: Critical issues requiring immediate attention
- ⚫ **Gray (N/A)**: Not applicable items

### GET /api/inspections
Get all inspection reports with pagination
- Query params: `page`, `limit`
- Returns: `{ inspections: [], pagination: {} }`

### GET /api/inspections/:id
Get a specific inspection report
- Returns: Single inspection object

### GET /api/inspections/:id/pdf
Generate and download PDF report
- Returns: PDF file stream

### GET /health
Health check endpoint
- Returns: `{ status, timestamp, database }`

## Environment Variables

```bash
# MongoDB Configuration
MONGODB_URI=*******************************************************************************

# Server Configuration
PORT=5003
NODE_ENV=development

# Application Settings
POLLING_INTERVAL=5000
MAX_REPORTS_PER_PAGE=50
```

## Database Schema

The application uses MongoDB with the following structure:

```javascript
{
  "_id": ObjectId,
  "report_title": "Vehicle Inspection Report",
  "vehicle_info_title": "Vehicle Information",
  "checklist_title": "Inspection Checklist", 
  "signoff_title": "Inspector Sign-off",
  "vehicle_owner": "Client Name",
  "client_email": "<EMAIL>",
  "unit_number": "UNIT-12345",
  "car_type": "Light-Duty Vehicle",
  "odometer_reading": "75,450 mi",
  "make_model_year": "Ford F-150 / 2020",
  "inspection_date_vehicle": "2025-06-18",
  "report_generation_datetime": "2025-06-18 14:30:25",
  "service_level": "Standard",
  "checklist_items": [
    {
      "item": "Brakes",
      "status": "OK",
      "notes": "Brake pads in good condition"
    }
  ],
  "inspector_name": "Inspector Name",
  "signature_url": "https://via.placeholder.com/105x30?text=Signature",
  "signoff_date": "2025-06-18",
  "signoff_location": "Denver, CO",
  "download_button_text": "Download PDF",
  "footer_line1": "Report generated by WFS Inspection System.",
  "footer_support_email": "<EMAIL>"
}
```

## Development

### Development Workflow

#### Using Makefile (Recommended)
```bash
# Start development environment
make dev-docker        # Start with Docker and debugging
make logs-follow       # Follow logs in real-time (separate terminal)

# Common development tasks
make restart-app       # Restart app after code changes
make reset-db         # Reset database with fresh data
make test-api         # Test API endpoints
make mongo-shell      # Access database directly

# Troubleshooting
make troubleshoot     # Run diagnostic checks
make health          # Check application health
make status          # Show service status
```

#### Manual Commands
```bash
# Development with Docker
docker compose up -d
docker compose logs -f reports-app

# Database operations
docker compose exec reports-app npm run init-db
docker exec -it reports-mongodb mongosh -u admin -p password123

# Restart services
docker compose restart reports-app
docker compose restart mongodb

# View logs
docker compose logs --tail=50 reports-app
docker compose logs --tail=50 mongodb

# Health checks
curl http://localhost:5003/health
curl http://localhost:5003/api/inspections
```

### Adding New Reports

To add new inspection reports, insert documents into the `inspections` collection:

```javascript
db.inspections.insertOne({
  // ... inspection data following the schema above
});
```

### Customizing the UI

- **Frontend**: Modify `/public/index.html`
- **PDF Template**: Update `/report_template.html` 
- **API**: Extend `/server.js`

### Database Indexes

The following indexes are created for optimal performance:
- `report_generation_datetime` (descending)
- `vehicle_owner` (ascending)
- `unit_number` (ascending) 
- `car_type` (ascending)
- `inspection_date_vehicle` (descending)

## Production Deployment

1. **Build the Docker image:**
   ```bash
   docker build -t inspection-reports .
   ```

2. **Deploy with production settings:**
   ```bash
   docker-compose -f docker-compose.yml up -d
   ```

3. **Monitor logs:**
   ```bash
   docker-compose logs -f reports-app
   ```

## Troubleshooting

### Quick Diagnostics

#### Using Makefile
```bash
make troubleshoot    # Run comprehensive troubleshooting checks
make status          # Check service status
make health          # Test application health
make logs            # View recent logs
make test-api        # Test API endpoints
```

#### Manual Commands
```bash
# Check services
docker compose ps
docker compose logs --tail=20 reports-app

# Test application
curl http://localhost:5003/health
curl http://localhost:5003/api/inspections

# Check MongoDB
docker exec reports-mongodb mongosh -u admin -p password123 --eval "db.stats()"
```

### Common Issues

#### MongoDB Connection Issues
- **Quick fix**: `make restart-db` or `docker compose restart mongodb`
- **Check connection**: `make mongo-shell` or verify `.env` file
- **Reset database**: `make reset-db`

#### PDF Generation Issues
- **Test PDF**: `make test-pdf`
- **Check logs**: `make logs-app`
- **Restart app**: `make restart-app`

#### Port Conflicts
- **Check ports**: `make ports` or change PORT in `.env`
- **Alternative**: Use different MongoDB port in `MONGODB_URI`

#### Performance Issues
- **Monitor**: `make mongo-stats`
- **Reset**: `make reset` for complete fresh start
- **Clean**: `make clean` to free up Docker resources

## Support

For technical support or questions:
- Email: <EMAIL>
- Check logs: `docker-compose logs reports-app`
- Health check: http://localhost:5003/health