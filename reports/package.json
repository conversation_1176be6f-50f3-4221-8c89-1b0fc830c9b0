{"name": "inspection-reports-dashboard", "version": "1.0.0", "description": "Real-time inspection reports dashboard with PDF generation", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/init-db.js"}, "dependencies": {"express": "^4.18.2", "mongodb": "^6.3.0", "puppeteer": "^21.6.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "twilio": "^5.6.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["inspection", "reports", "dashboard", "real-time"], "author": "Workforce Services", "license": "MIT"}