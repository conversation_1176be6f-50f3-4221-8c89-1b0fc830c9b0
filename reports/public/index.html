<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WorkForce AI Assistant - Vehicle Inspections</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#102a43',
                        secondary: '#243b53',
                        accent: '#486581',
                        success: '#2cb67d',
                        warning: '#ff8906',
                        danger: '#e63946',
                    }
                }
            }
        }
    </script>
    <style>
        .inspection-card {
            transition: all 0.2s ease;
        }
        .inspection-card:hover {
            transform: translateY(-2px);
        }
        .status-ok { color: #2cb67d; font-weight: bold; }
        .status-good { color: #2cb67d; font-weight: bold; }
        .status-fair { color: #ff8906; font-weight: bold; }
        .status-poor { color: #e63946; font-weight: bold; }
        .status-fail { color: #e63946; font-weight: bold; }
        .status-na { color: #6b7280; font-style: italic; }
        
        .loading-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
        }
        
        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-primary">WorkForce AI Assistant</h1>
                    <span class="ml-3 px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full" id="status-indicator">
                        ● Live
                    </span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-500" id="last-updated">
                        Last updated: Never
                    </span>
                    <button 
                        onclick="refreshData()" 
                        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent"
                    >
                        <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                        </svg>
                        Refresh
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- Phone Interface Section -->
        <div class="bg-white rounded-lg shadow mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                    </svg>
                    Make a Call to AI Assistant
                </h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Call Controls -->
                    <div class="space-y-4">
                        <!-- Call Buttons -->
                        <div class="space-y-3">
                            <div class="flex space-x-3">
                                <button class="flex-1 h-12 px-4 py-2 bg-gray-400 text-white rounded-lg font-medium cursor-not-allowed opacity-60" id="devBtn" disabled>
                                    <div class="flex items-center justify-center">
                                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.58.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.58.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                                        </svg>
                                        DEV
                                    </div>
                                </button>
                                <button class="flex-1 h-12 px-4 py-2 bg-warning text-white rounded-lg font-medium hover:bg-yellow-600 transition-colors" id="qaBtn" data-number="+17274966533">
                                    <div class="flex items-center justify-center">
                                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.58.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.58.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                                        </svg>
                                        QA
                                    </div>
                                </button>
                                <button class="flex-1 h-12 px-4 py-2 bg-gray-400 text-white rounded-lg font-medium cursor-not-allowed opacity-60" id="prodBtn" disabled>
                                    <div class="flex items-center justify-center">
                                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.58.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.58.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                                        </svg>
                                        PROD
                                    </div>
                                </button>
                            </div>
                            
                            <!-- Hang Up Button -->
                            <button class="w-full h-12 px-4 py-2 bg-danger text-white rounded-lg font-medium hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" id="hangupBtn" disabled>
                                <div class="flex items-center justify-center">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 9c-1.6 0-3.15.25-4.6.72v3.1c0 .39-.23.74-.56.9-.98.46-1.97 1.02-2.92 1.68-.24.16-.54.25-.84.25-.83 0-1.5-.67-1.5-1.5v-4.27c0-.46.21-.89.56-1.15C2.97 8.2 7.27 7 12 7s9.03 1.2 9.94 1.73c.35.26.56.69.56 1.15v4.27c0 .83-.67 1.5-1.5 1.5-.3 0-.6-.09-.84-.25-.95-.66-1.94-1.22-2.92-1.68-.33-.16-.56-.51-.56-.9v-3.1C15.15 9.25 13.6 9 12 9z"/>
                                    </svg>
                                    Hang Up
                                </div>
                            </button>
                        </div>
                    </div>

                    <!-- Call Status -->
                    <div class="space-y-4">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="text-sm font-medium text-gray-700 mb-3">Call Status</h3>
                            <div class="space-y-2" id="statusSteps">
                                <div class="flex items-center text-sm" id="step-sdk">
                                    <div class="w-2 h-2 rounded-full bg-gray-300 mr-3"></div>
                                    <span class="text-gray-600">Loading Twilio SDK...</span>
                                </div>
                                <div class="flex items-center text-sm" id="step-token">
                                    <div class="w-2 h-2 rounded-full bg-gray-300 mr-3"></div>
                                    <span class="text-gray-600">Fetching authentication token...</span>
                                </div>
                                <div class="flex items-center text-sm" id="step-device">
                                    <div class="w-2 h-2 rounded-full bg-gray-300 mr-3"></div>
                                    <span class="text-gray-600">Initializing Twilio Device...</span>
                                </div>
                                <div class="flex items-center text-sm" id="step-call">
                                    <div class="w-2 h-2 rounded-full bg-gray-300 mr-3"></div>
                                    <span class="text-gray-600">Connecting call...</span>
                                </div>
                                <div class="flex items-center text-sm" id="step-connected">
                                    <div class="w-2 h-2 rounded-full bg-gray-300 mr-3"></div>
                                    <span class="text-gray-600">Call connected and active</span>
                                </div>
                            </div>
                        </div>

                        <!-- Call Log -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="text-sm font-medium text-gray-700 mb-3">Call Log</h3>
                            <div class="h-24 overflow-y-auto text-xs font-mono text-gray-600 bg-white rounded border p-2" id="logArea">
                                Twilio SDK ready for calls...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-primary rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Reports</p>
                        <p class="text-lg font-semibold text-gray-900" id="total-count">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-success rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Today</p>
                        <p class="text-lg font-semibold text-gray-900" id="today-count">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-warning rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">This Week</p>
                        <p class="text-lg font-semibold text-gray-900" id="week-count">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-accent rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">This Month</p>
                        <p class="text-lg font-semibold text-gray-900" id="month-count">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reports Grid -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">Recent Inspections</h2>
            </div>
            
            <!-- Loading State -->
            <div id="loading-state" class="p-6">
                <div class="grid grid-cols-1 gap-4">
                    <div class="loading-shimmer h-24 rounded-lg"></div>
                    <div class="loading-shimmer h-24 rounded-lg"></div>
                    <div class="loading-shimmer h-24 rounded-lg"></div>
                </div>
            </div>

            <!-- Reports List -->
            <div id="reports-container" class="hidden">
                <div id="reports-list" class="divide-y divide-gray-200">
                    <!-- Reports will be populated here -->
                </div>
            </div>

            <!-- Empty State -->
            <div id="empty-state" class="hidden p-12 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                <h3 class="mt-4 text-sm font-medium text-gray-900">No inspection reports</h3>
                <p class="mt-2 text-sm text-gray-500">Get started by conducting your first vehicle inspection.</p>
            </div>
        </div>
    </main>

    <script>
        let pollingInterval;
        let lastFetchTime = null;
        
        // Twilio phone functionality
        let connection = null;
        let deviceReady = false;
        let logElement;

        // Initialize the app
        document.addEventListener('DOMContentLoaded', function() {
            fetchReports();
            startPolling();
            initializePhoneInterface();
        });

        // Start polling for new data
        function startPolling() {
            pollingInterval = setInterval(fetchReports, 5000); // Poll every 5 seconds
        }

        // Stop polling
        function stopPolling() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }
        }

        // Fetch reports from API
        async function fetchReports() {
            try {
                updateStatus('fetching');
                
                const response = await fetch('/api/inspections');
                const data = await response.json();
                
                updateReportsList(data.inspections);
                updateStats(data.inspections, data.pagination.total);
                updateStatus('success');
                
                lastFetchTime = new Date();
                updateLastUpdatedTime();
                
            } catch (error) {
                console.error('Error fetching reports:', error);
                updateStatus('error');
            }
        }

        // Update the reports list
        function updateReportsList(inspections) {
            const container = document.getElementById('reports-list');
            const loadingState = document.getElementById('loading-state');
            const reportsContainer = document.getElementById('reports-container');
            const emptyState = document.getElementById('empty-state');

            loadingState.classList.add('hidden');

            if (inspections.length === 0) {
                reportsContainer.classList.add('hidden');
                emptyState.classList.remove('hidden');
                return;
            }

            emptyState.classList.add('hidden');
            reportsContainer.classList.remove('hidden');

            container.innerHTML = inspections.map(inspection => `
                <div class="inspection-card p-6 hover:bg-gray-50 cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-4">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-accent rounded-lg flex items-center justify-center">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                        </svg>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <h3 class="text-lg font-medium text-gray-900">${inspection.vehicle_owner}</h3>
                                        <span class="px-2 py-1 text-xs rounded-full ${getVehicleTypeColor(inspection.car_type)}">
                                            ${inspection.car_type}
                                        </span>
                                    </div>
                                    <div class="mt-1 text-sm text-gray-500">
                                        <span class="font-medium">Unit:</span> ${inspection.unit_number} • 
                                        <span class="font-medium">Vehicle:</span> ${inspection.make_model_year} • 
                                        <span class="font-medium">Odometer:</span> ${inspection.odometer_reading}
                                    </div>
                                    <div class="mt-1 text-sm text-gray-500">
                                        <span class="font-medium">Inspected:</span> ${inspection.inspection_date_vehicle} • 
                                        <span class="font-medium">Report:</span> ${inspection.report_generation_datetime} • 
                                        <span class="font-medium">Inspector:</span> ${inspection.inspector_name}
                                    </div>
                                    <div class="mt-2 flex items-center space-x-2">
                                        ${getChecklistSummary(inspection.checklist_items)}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <button 
                                onclick="viewReport('${inspection._id}')" 
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent transition-colors"
                            >
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                </svg>
                                View PDF
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Get vehicle type color
        function getVehicleTypeColor(type) {
            const colors = {
                'Light-Duty Vehicle': 'bg-blue-100 text-blue-800',
                'Power-Unit/Tractor': 'bg-green-100 text-green-800',
                'Trailer': 'bg-yellow-100 text-yellow-800',
                'Liftgate/Forklift': 'bg-purple-100 text-purple-800',
                'Hy-Rail/Heavy-Duty': 'bg-red-100 text-red-800',
                'General DOT Commercial': 'bg-gray-100 text-gray-800'
            };
            return colors[type] || 'bg-gray-100 text-gray-800';
        }

        // Get checklist summary with status indicators
        function getChecklistSummary(items) {
            if (!items || items.length === 0) return '<span class="text-gray-400">No checklist items</span>';
            
            const statusCounts = items.reduce((acc, item) => {
                const status = item.status.toLowerCase();
                acc[status] = (acc[status] || 0) + 1;
                return acc;
            }, {});

            return Object.entries(statusCounts).map(([status, count]) => 
                `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium status-${status}">
                    ${count} ${status.toUpperCase()}
                </span>`
            ).join('');
        }

        // Update statistics
        function updateStats(inspections, total) {
            document.getElementById('total-count').textContent = total;

            const today = new Date().toISOString().split('T')[0];
            const todayCount = inspections.filter(i => i.inspection_date_vehicle === today).length;
            document.getElementById('today-count').textContent = todayCount;

            const weekAgo = new Date();
            weekAgo.setDate(weekAgo.getDate() - 7);
            const weekCount = inspections.filter(i => new Date(i.inspection_date_vehicle) >= weekAgo).length;
            document.getElementById('week-count').textContent = weekCount;

            const monthAgo = new Date();
            monthAgo.setMonth(monthAgo.getMonth() - 1);
            const monthCount = inspections.filter(i => new Date(i.inspection_date_vehicle) >= monthAgo).length;
            document.getElementById('month-count').textContent = monthCount;
        }

        // Update status indicator
        function updateStatus(status) {
            const indicator = document.getElementById('status-indicator');
            
            switch(status) {
                case 'fetching':
                    indicator.innerHTML = '⟳ Updating...';
                    indicator.className = 'ml-3 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full';
                    break;
                case 'success':
                    indicator.innerHTML = '● Live';
                    indicator.className = 'ml-3 px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full';
                    break;
                case 'error':
                    indicator.innerHTML = '● Error';
                    indicator.className = 'ml-3 px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full';
                    break;
            }
        }

        // Update last updated time
        function updateLastUpdatedTime() {
            if (lastFetchTime) {
                const timeString = lastFetchTime.toLocaleTimeString();
                document.getElementById('last-updated').textContent = `Last updated: ${timeString}`;
            }
        }

        // View report PDF in new tab
        function viewReport(inspectionId) {
            window.open(`/api/inspections/${inspectionId}/pdf`, '_blank');
        }

        // Manual refresh
        function refreshData() {
            fetchReports();
        }

        // Handle page visibility changes
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                stopPolling();
            } else {
                startPolling();
                fetchReports(); // Immediate fetch when page becomes visible
            }
        });

        // Phone Interface Functions
        function initializePhoneInterface() {
            logElement = document.getElementById("logArea");
            
            // Load Twilio SDK
            const script = document.createElement('script');
            script.src = 'https://media.twiliocdn.com/sdk/js/client/releases/1.14.0/twilio.min.js';
            script.onload = function() {
                if (typeof Twilio !== 'undefined') {
                    setupPhoneEventHandlers();
                    updateStepStatus('step-sdk', 'completed', 'Twilio SDK ready for calls');
                    log("✅ Twilio SDK loaded successfully.");
                }
            };
            script.onerror = function() {
                log("❌ Failed to load Twilio SDK from CDN");
                updateStepStatus('step-sdk', 'error', 'Failed to load Twilio SDK');
            };
            document.head.appendChild(script);
        }

        function setupPhoneEventHandlers() {
            // DEV call button handler (disabled)
            document.getElementById("devBtn").addEventListener("click", () => {
                log("⚠️ DEV environment is currently not available");
            });

            // QA call button handler
            document.getElementById("qaBtn").addEventListener("click", () => {
                const qaNumber = document.getElementById("qaBtn").dataset.number;
                log(`🧪 Calling QA environment: ${qaNumber}`);
                makeCall(qaNumber);
            });

            // PROD call button handler (disabled)
            document.getElementById("prodBtn").addEventListener("click", () => {
                log("⚠️ PROD environment is currently not available");
            });

            // Hangup button handler
            document.getElementById("hangupBtn").addEventListener("click", endCall);
        }

        const log = (msg) => {
            if (!logElement) logElement = document.getElementById("logArea");
            const now = new Date();
            const timeString = now.toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
                second: "2-digit",
            });
            logElement.textContent += `[${timeString}] ${msg}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${timeString}] ${msg}`);
        };

        function updateStepStatus(stepId, status, message = null) {
            const step = document.getElementById(stepId);
            if (!step) return;
            
            const dot = step.querySelector('div');
            const text = step.querySelector('span');
            
            // Update dot color based on status
            switch(status) {
                case 'pending':
                    dot.className = 'w-2 h-2 rounded-full bg-gray-300 mr-3';
                    break;
                case 'active':
                    dot.className = 'w-2 h-2 rounded-full bg-yellow-500 mr-3';
                    break;
                case 'completed':
                    dot.className = 'w-2 h-2 rounded-full bg-green-500 mr-3';
                    break;
                case 'error':
                    dot.className = 'w-2 h-2 rounded-full bg-red-500 mr-3';
                    break;
            }
            
            // Update message if provided
            if (message && text) {
                text.textContent = message;
            }
        }

        function resetStatusFlow() {
            const steps = ['step-sdk', 'step-token', 'step-device', 'step-call', 'step-connected'];
            steps.forEach(stepId => {
                updateStepStatus(stepId, 'pending');
            });
            
            // Reset default messages
            updateStepStatus('step-sdk', 'pending', 'Loading Twilio SDK...');
            updateStepStatus('step-token', 'pending', 'Fetching authentication token...');
            updateStepStatus('step-device', 'pending', 'Initializing Twilio Device...');
            updateStepStatus('step-call', 'pending', 'Connecting call...');
            updateStepStatus('step-connected', 'pending', 'Call connected and active');
        }

        function resetCallState() {
            connection = null;
            // Reset button states
            document.getElementById("hangupBtn").disabled = true;
            document.getElementById("qaBtn").disabled = false;
        }

        async function setupAudioDevices() {
            log("🎤 Requesting microphone permissions...");
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    audio: true, 
                    video: false 
                });
                
                log("✅ Microphone access granted");
                stream.getTracks().forEach(track => track.stop());
                
                if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
                    const devices = await navigator.mediaDevices.enumerateDevices();
                    const audioInputs = devices.filter(d => d.kind === 'audioinput');
                    const audioOutputs = devices.filter(d => d.kind === 'audiooutput');
                    
                    log(`🎧 Found ${audioInputs.length} microphone(s) and ${audioOutputs.length} speaker(s)`);
                }
                
                return true;
            } catch (err) {
                log(`❌ Microphone access denied: ${err.message}`);
                throw err;
            }
        }

        async function makeCall(numberToCall) {
            if (typeof Twilio === 'undefined') {
                updateStepStatus('step-sdk', 'error', 'Twilio SDK not loaded');
                log("❌ Twilio SDK not loaded. Please refresh the page and try again.");
                return;
            }

            if (connection) {
                log("⚠️ Already in an active call. Please end it first.");
                return;
            }

            // Reset status flow for new call
            resetStatusFlow();
            updateStepStatus('step-sdk', 'completed', 'Twilio SDK ready');
            
            // Always fetch a new token
            deviceReady = false;
            updateStepStatus('step-token', 'active', 'Fetching fresh authentication token...');
            log("🔄 Fetching fresh token for new session...");
            
            try {
                const response = await fetch("/token");
                if (!response.ok) {
                    throw new Error(`Token fetch failed: ${response.status} ${response.statusText}`);
                }
                const data = await response.json();
                if (!data.token) {
                    throw new Error("Token not found in server response.");
                }
                updateStepStatus('step-token', 'completed', 'Authentication token received');
                updateStepStatus('step-device', 'active', 'Setting up Twilio Device...');
                log("✅ Token fetched.");
                if (data.sessionId) {
                    log(`🔑 Session ID: ${data.sessionId}`);
                }

                // Setup device
                Twilio.Device.setup(data.token, {
                    debug: true,
                    enableRingingState: true
                });
                
                Twilio.Device.ready(() => {
                    deviceReady = true;
                    updateStepStatus('step-device', 'completed', 'Twilio Device initialized');
                    log("✅ Twilio Device is ready.");
                    
                    setupAudioDevices().then(() => {
                        initiateCall(numberToCall);
                    }).catch((err) => {
                        log(`⚠️ Audio setup warning: ${err.message}`);
                        initiateCall(numberToCall);
                    });
                });

                Twilio.Device.error((error) => {
                    updateStepStatus('step-call', 'error', `Device error: ${error.message}`);
                    log(`❌ Device setup error: ${error.message}`);
                    deviceReady = false;
                });

                Twilio.Device.connect((conn) => {
                    updateStepStatus('step-call', 'completed', 'Call initiated successfully');
                    updateStepStatus('step-connected', 'active', 'Call connected and active');
                    log("🔗 Call connected!");
                    connection = conn;
                    
                    conn.on('ringing', () => {
                        log("📞 Call is ringing...");
                    });
                    
                    conn.on('accept', () => {
                        log("✅ Call accepted by remote party");
                    });
                    
                    conn.on('cancel', () => {
                        log("🚫 Call was cancelled");
                    });
                    
                    conn.on('reject', () => {
                        log("❌ Call was rejected");
                    });
                    
                    conn.on('error', (error) => {
                        log(`❌ Call error: ${error.message}`);
                    });
                });

                Twilio.Device.disconnect(() => {
                    updateStepStatus('step-connected', 'completed', 'Call ended');
                    log("📴 Call ended.");
                    resetCallState();
                    
                    setTimeout(() => {
                        resetStatusFlow();
                        updateStepStatus('step-sdk', 'completed', 'Twilio SDK ready for next call');
                    }, 2000);
                });

                Twilio.Device.offline(() => {
                    log("📶 Device is offline.");
                    deviceReady = false;
                });

            } catch (err) {
                if (err.message.includes("Token fetch failed")) {
                    updateStepStatus('step-token', 'error', 'Failed to fetch authentication token');
                } else {
                    updateStepStatus('step-device', 'error', `Setup error: ${err.message}`);
                }
                log(`❌ Error setting up call: ${err.message}`);
                deviceReady = false;
            }
        }
        
        function initiateCall(numberToCall) {
            updateStepStatus('step-call', 'active', `Connecting call to ${numberToCall}...`);
            
            log(`📟 Device status before call: ${Twilio.Device.status()}`);
            
            const params = { To: numberToCall };
            log(`📞 Calling ${params.To}...`);
            
            try {
                connection = Twilio.Device.connect(params);
                log(`✅ Call connection initiated successfully`);
            } catch (err) {
                log(`❌ Failed to initiate call: ${err.message}`);
                updateStepStatus('step-call', 'error', `Failed to initiate call: ${err.message}`);
                return;
            }
            
            // Enable hangup button
            document.getElementById("hangupBtn").disabled = false;
            document.getElementById("qaBtn").disabled = true;
        }

        function endCall() {
            if (connection) {
                log("🔚 Ending active call...");
                try {
                    Twilio.Device.disconnectAll();
                } catch (e) {
                    log("⚠️ Error ending call, forcing cleanup...");
                }
                
                document.getElementById("hangupBtn").disabled = true;
                document.getElementById("qaBtn").disabled = false;
            } else {
                log("⚠️ No active call to end.");
            }
        }
    </script>
</body>
</html>