version: '3.8'

services:
  mongodb:
    image: mongo:7.0
    container_name: reports-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: inspection_reports
    ports:
      - "27018:27017"
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - reports-network

  reports-app:
    build: .
    container_name: reports-dashboard
    restart: unless-stopped
    ports:
      - "5003:5003"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=*****************************************************************************
      - PORT=5003
    depends_on:
      - mongodb
    networks:
      - reports-network
    volumes:
      - ./public:/app/public
      - ./views:/app/views

volumes:
  mongodb_data:

networks:
  reports-network:
    driver: bridge