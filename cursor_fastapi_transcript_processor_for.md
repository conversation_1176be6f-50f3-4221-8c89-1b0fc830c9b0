# FastAPI Transcript Processor for MongoDB
_Exported on 5/20/2025 at 18:37:55 GMT-3 from Cursor (0.50.5)_

---

**User**

can you create in the @voice-assistant /trasncrip-processor anothe main.py file that uses fastapi and exposes the transcript sent by @main.py to save it in a docker container of a mongodb which the collection name is the clients name?

---

**<PERSON><PERSON>or**

I'll create a transcript-processor service with FastAPI that receives transcripts from the main voice assistant service and saves them to a MongoDB collection. Let me implement this solution.

Let me create the transcript-processor service that will receive transcripts from the main application and store them in MongoDB.

Now, I'll create the main.py file for our transcript-processor service:

Now, let's create a requirements.txt file for the transcript-processor:

Let's create a Dockerfile for the transcript-processor service:

Let's create a docker-compose.yml file at the root of the project that includes both the transcript-processor and MongoDB:

Let's create the docker-compose.yml file:

Let's create a .env.example file for the transcript-processor service that includes MongoDB connection details:

Let's update the main voice assistant application to include the client name when sending transcripts to the transcript-processor service:

```python
        # Prepare the payload with both raw transcript and structured conversation
                payload = {
                    "transcript": full_conversation_transcript,
                    "structured_conversation": structured_conversation,
                    "call_id": stream_sid,
                    "timestamp": time.time(),
                    "metadata": {
                        "duration": time.time() - call_start_time,
                        "silence_warnings_sent": silence_warning_count,
                        "goodbye_detected": goodbye_confirmation_sent,
                        "transcript_segments": len(structured_conversation)
                    }
                }

->

        # Prepare the payload with both raw transcript and structured conversation
                payload = {
                    "transcript": full_conversation_transcript,
                    "structured_conversation": structured_conversation,
                    "call_id": stream_sid,
                    "timestamp": time.time(),
                    "client_name": extract_client_name_from_transcript(full_conversation_transcript),
                    "metadata": {
                        "duration": time.time() - call_start_time,
                        "silence_warnings_sent": silence_warning_count,
                        "goodbye_detected": goodbye_confirmation_sent,
                        "transcript_segments": len(structured_conversation)
                    }
                }
```

Let's add the function to extract the client name from the transcript:

```python
# Create a list to track all tasks we create
    tasks = []
    openai_ws = None

->

# Create a list to track all tasks we create
    tasks = []
    openai_ws = None
    
    # Try to extract client name from the conversation
    def extract_client_name_from_transcript(transcript_text):
        """
        Extract the client name from transcript text.
        Returns a default name if none found.
        """
        try:
            # Look for patterns like "customer name is [name]" or "client's name: [name]"
            import re
            client_patterns = [
                r"customer(?:'s)? name (?:is|was) ([A-Za-z\s]+)",
                r"client(?:'s)? name (?:is|was) ([A-Za-z\s]+)",
                r"(?:name|customer|client)(?:'s)? ([A-Za-z\s]+)",
                r"customer: ([A-Za-z\s]+)"
            ]
            
            for pattern in client_patterns:
                matches = re.findall(pattern, transcript_text, re.IGNORECASE)
                if matches:
                    # Clean up the extracted name
                    client_name = matches[0].strip()
                    # Avoid capturing "is" or "was" if they're part of the match
                    if client_name.lower() in ["is", "was"]:
                        continue
                    # Return if we have a valid name longer than 2 characters
                    if len(client_name) > 2:
                        return client_name
            
            # Default if no client name found
            return "unknown_client"
        except Exception as e:
            print(f"Error extracting client name: {e}")
            return "unknown_client"
```

Let's create a simple README for the transcript-processor:

Let's create a basic testing script for the transcript-processor:

---

**User**

please , rollback everything until the last prompt

---

**Cursor**

I'll rollback the changes we made in the previous session.

Now I'll delete the files we created in the transcript-processor directory and docker-compose.yml file:

```python
# Create a list to track all tasks we create
    tasks = []
    openai_ws = None
    
    # Try to extract client name from the conversation
    def extract_client_name_from_transcript(transcript_text):
        """
        Extract the client name from transcript text.
        Returns a default name if none found.
        """
        try:
            # Look for patterns like "customer name is [name]" or "client's name: [name]"
            import re
            client_patterns = [
                r"customer(?:'s)? name (?:is|was) ([A-Za-z\s]+)",
                r"client(?:'s)? name (?:is|was) ([A-Za-z\s]+)",
                r"(?:name|customer|client)(?:'s)? ([A-Za-z\s]+)",
                r"customer: ([A-Za-z\s]+)"
            ]
            
            for pattern in client_patterns:
                matches = re.findall(pattern, transcript_text, re.IGNORECASE)
                if matches:
                    # Clean up the extracted name
                    client_name = matches[0].strip()
                    # Avoid capturing "is" or "was" if they're part of the match
                    if client_name.lower() in ["is", "was"]:
                        continue
                    # Return if we have a valid name longer than 2 characters
                    if len(client_name) > 2:
                        return client_name
            
            # Default if no client name found
            return "unknown_client"
        except Exception as e:
            print(f"Error extracting client name: {e}")
            return "unknown_client"

->

# Create a list to track all tasks we create
    tasks = []
    openai_ws = None
```

```python
                payload = {
                    "transcript": full_conversation_transcript,
                    "structured_conversation": structured_conversation,
                    "call_id": stream_sid,
                    "timestamp": time.time(),
                    "client_name": extract_client_name_from_transcript(full_conversation_transcript),
                    "metadata": {
                        "duration": time.time() - call_start_time,
                        "silence_warnings_sent": silence_warning_count,
                        "goodbye_detected": goodbye_confirmation_sent,
                        "transcript_segments": len(structured_conversation)
                    }

->

                payload = {
                    "transcript": full_conversation_transcript,
                    "structured_conversation": structured_conversation,
                    "call_id": stream_sid,
                    "timestamp": time.time(),
                    "metadata": {
                        "duration": time.time() - call_start_time,
                        "silence_warnings_sent": silence_warning_count,
                        "goodbye_detected": goodbye_confirmation_sent,
                        "transcript_segments": len(structured_conversation)
                    }
```

All changes have been successfully rolled back. I've:

1. Removed the client name extraction function from main.py
2. Removed the client_name field from the payload in main.py
3. Deleted all files we created in the transcript-processor directory
4. Removed the transcript-processor directory itself
5. Deleted the docker-compose.yml file we created

The system is now back to its state before our previous session.

---

**User**

awesome, please let's change @main.py to instead of send the transcript via http, to save it in local mongodb container, using a collection with an easy already parsed identifier like sid or date, hour mini, or caller id + hh:mm-dd/mm/yy

---

**Cursor**

I'll modify the main.py file to save transcripts locally to a MongoDB container instead of sending them via HTTP.

Now, let's update the requirements.txt file to add pymongo:

Let's add pymongo to the requirements.txt file:

```pip-requirements
aiohappyeyeballs==2.4.0
aiohttp==3.10.6
aiohttp-retry==2.8.3
aiosignal==1.3.1
annotated-types==0.7.0
anyio==4.6.0
async-timeout==4.0.3
attrs==24.2.0
certifi==2024.8.30
charset-normalizer==3.3.2
click==8.1.7
exceptiongroup==1.2.2
fastapi==0.115.0
frozenlist==1.4.1
h11==0.14.0
httpx==0.27.0
idna==3.10
multidict==6.1.0
pymongo==4.13.0
pydantic==2.9.2
pydantic_core==2.23.4
PyJWT==2.9.0
python-dotenv==1.0.1
requests==2.32.3
sniffio==1.3.1
starlette==0.38.6
twilio==9.3.2
typing_extensions==4.12.2
urllib3==2.2.3
uvicorn==0.30.6
websockets==13.1
yarl==1.12.1
```

Now, let's create a docker-compose.yml file to set up the MongoDB container:

Now, let's create a Dockerfile for our application:

Now let's modify the main.py file to use MongoDB instead of HTTP for transcript storage:

```python
import os
import sys
import json
import base64
import asyncio
import re
import websockets
import time
import httpx
from typing import List, Dict, Any
from fastapi import FastAPI, WebSocket, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.websockets import WebSocketDisconnect
from twilio.twiml.voice_response import VoiceResponse, Connect, Say, Stream, Hangup
from twilio.rest import Client
from dotenv import load_dotenv

load_dotenv()

# Ensure transcript directory exists
transcripts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'transcripts')
os.makedirs(transcripts_dir, exist_ok=True)

# Configuration
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
PORT = int(os.getenv('PORT', 10000))
host = os.getenv('HOST', 'voice.aiready.io')
TRANSCRIPT_UPLOAD_URL = os.getenv('TRANSCRIPT_UPLOAD_URL', 'http://api.aiready.io/api/voice_ready/call-status-callback')

->

import os
import sys
import json
import base64
import asyncio
import re
import websockets
import time
import httpx
from typing import List, Dict, Any
from fastapi import FastAPI, WebSocket, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.websockets import WebSocketDisconnect
from twilio.twiml.voice_response import VoiceResponse, Connect, Say, Stream, Hangup
from twilio.rest import Client
from dotenv import load_dotenv
from pymongo import MongoClient
from datetime import datetime

load_dotenv()

# Ensure transcript directory exists
transcripts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'transcripts')
os.makedirs(transcripts_dir, exist_ok=True)

# Configuration
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
PORT = int(os.getenv('PORT', 10000))
host = os.getenv('HOST', 'voice.aiready.io')

# MongoDB Configuration
MONGO_URI = os.getenv('MONGO_URI', '****************************************')
MONGO_DB = os.getenv('MONGO_DB', 'voice_assistant')
MONGO_COLLECTION = os.getenv('MONGO_COLLECTION', 'transcripts')
```

```python
        async def upload_transcript():
            """Upload the full conversation transcript to the specified endpoint."""
            nonlocal full_conversation_transcript, structured_conversation
            
            # Ensure we have the final aggregated transcript
            await aggregate_transcriptions()
            
            if not full_conversation_transcript:
                print("No transcript to upload.")
                return
                
            try:
                print(f"Uploading transcript (length: {len(full_conversation_transcript)})")
                
                # Prepare the payload with both raw transcript and structured conversation
                payload = {
                    "transcript": full_conversation_transcript,
                    "structured_conversation": structured_conversation,
                    "call_id": stream_sid,
                    "timestamp": time.time(),
                    "metadata": {
                        "duration": time.time() - call_start_time,
                        "silence_warnings_sent": silence_warning_count,
                        "goodbye_detected": goodbye_confirmation_sent,
                        "transcript_segments": len(structured_conversation)
                    }
                }
                
                # Send the transcript to the specified endpoint from environment variable
                async with httpx.AsyncClient() as client:
                    try:
                        print(f"Sending transcript to: {TRANSCRIPT_UPLOAD_URL}")
                        print(f"Payload summary: Transcript length: {len(payload['transcript'])}, Segments: {len(payload['structured_conversation'])}, Call ID: {payload['call_id']}")
                        
                        response = await client.post(
                            TRANSCRIPT_UPLOAD_URL,
                            json=payload,
                            headers={
                                "Content-Type": "application/json",
                                "Accept": "application/json"
                            },
                            timeout=30.0  # Set a reasonable timeout
                        )
                        
                        if response.status_code == 200:
                            print(f"Transcript uploaded successfully: {response.status_code}")
                            try:
                                response_data = response.json()
                                print(f"Response data: {response_data}")
                            except Exception as e:
                                print(f"Could not parse response JSON: {e}")
                        else:
                            print(f"Failed to upload transcript: Status {response.status_code}")
                            print(f"Response text: {response.text}")
                    except httpx.RequestError as e:
                        print(f"Request error uploading transcript: {e}")
                    except Exception as e:
                        print(f"Unexpected error in transcript upload: {e}")
                        
                # Also log the transcript locally for debugging
                try:
                    with open(f"transcripts/transcript_{stream_sid}_{int(time.time())}.json", "w") as f:
                        json.dump(payload, f, indent=2)
                    print(f"Saved transcript locally")
                except Exception as e:
                    print(f"Error saving transcript locally: {e}")
            except Exception as e:
                print(f"Error uploading transcript: {e}")

->

        async def save_transcript_to_mongodb():
            """Save the full conversation transcript to MongoDB."""
            nonlocal full_conversation_transcript, structured_conversation
            
            # Ensure we have the final aggregated transcript
            await aggregate_transcriptions()
            
            if not full_conversation_transcript:
                print("No transcript to save.")
                return
                
            try:
                print(f"Saving transcript to MongoDB (length: {len(full_conversation_transcript)})")
                
                # Generate a timestamp for easier querying
                current_time = datetime.now()
                timestamp_str = current_time.strftime("%Y%m%d_%H%M%S")
                
                # Create a collection name based on date
                collection_name = f"{MONGO_COLLECTION}_{current_time.strftime('%Y%m')}"
                
                # Prepare the document to store in MongoDB
                document = {
                    "transcript": full_conversation_transcript,
                    "structured_conversation": structured_conversation,
                    "call_id": stream_sid,
                    "timestamp": time.time(),
                    "date_time": current_time,
                    "formatted_time": timestamp_str,
                    # Create a unique identifier that's easy to query
                    "call_identifier": f"{stream_sid}_{timestamp_str}",
                    "metadata": {
                        "duration": time.time() - call_start_time,
                        "silence_warnings_sent": silence_warning_count,
                        "goodbye_detected": goodbye_confirmation_sent,
                        "transcript_segments": len(structured_conversation)
                    }
                }
                
                # Connect to MongoDB and save the transcript
                try:
                    # Create MongoDB client
                    mongo_client = MongoClient(MONGO_URI)
                    db = mongo_client[MONGO_DB]
                    collection = db[collection_name]
                    
                    # Insert the document
                    result = collection.insert_one(document)
                    
                    print(f"Transcript saved to MongoDB successfully. Document ID: {result.inserted_id}")
                    print(f"Collection: {collection_name}, Call Identifier: {document['call_identifier']}")
                    
                    # Close the MongoDB connection
                    mongo_client.close()
                except Exception as e:
                    print(f"Error saving to MongoDB: {e}")
                    
                    # Fallback to local file storage if MongoDB fails
                    try:
                        with open(f"transcripts/transcript_{document['call_identifier']}.json", "w") as f:
                            json.dump(document, f, indent=2)
                        print(f"Fallback: Saved transcript locally as transcript_{document['call_identifier']}.json")
                    except Exception as file_error:
                        print(f"Error saving transcript locally: {file_error}")
                
            except Exception as e:
                print(f"Error in save_transcript_to_mongodb: {e}")
```

Let's read the current content of the file first:

Now let's replace the `upload_transcript` function with our MongoDB version:

```python
        async def upload_transcript():
            """Upload the full conversation transcript to the specified endpoint."""
            nonlocal full_conversation_transcript, structured_conversation
            
            # Ensure we have the final aggregated transcript
            await aggregate_transcriptions()
            
            if not full_conversation_transcript:
                print("No transcript to upload.")
                return
                
            try:
                print(f"Uploading transcript (length: {len(full_conversation_transcript)})")
                
                # Prepare the payload with both raw transcript and structured conversation
                payload = {
                    "transcript": full_conversation_transcript,
                    "structured_conversation": structured_conversation,
                    "call_id": stream_sid,
                    "timestamp": time.time(),
                    "metadata": {
                        "duration": time.time() - call_start_time,
                        "silence_warnings_sent": silence_warning_count,
                        "goodbye_detected": goodbye_confirmation_sent,
                        "transcript_segments": len(structured_conversation)
                    }
                }
                
                # Send the transcript to the specified endpoint from environment variable
                async with httpx.AsyncClient() as client:
                    try:
                        print(f"Sending transcript to: {TRANSCRIPT_UPLOAD_URL}")
                        print(f"Payload summary: Transcript length: {len(payload['transcript'])}, Segments: {len(payload['structured_conversation'])}, Call ID: {payload['call_id']}")
                        
                        response = await client.post(
                            TRANSCRIPT_UPLOAD_URL,
                            json=payload,
                            headers={
                                "Content-Type": "application/json",
                                "Accept": "application/json"
                            },
                            timeout=30.0  # Set a reasonable timeout
                        )
                        
                        if response.status_code == 200:
                            print(f"Transcript uploaded successfully: {response.status_code}")
                            try:
                                response_data = response.json()
                                print(f"Response data: {response_data}")
                            except Exception as e:
                                print(f"Could not parse response JSON: {e}")
                        else:
                            print(f"Failed to upload transcript: Status {response.status_code}")
                            print(f"Response text: {response.text}")
                    except httpx.RequestError as e:
                        print(f"Request error uploading transcript: {e}")
                    except Exception as e:
                        print(f"Unexpected error in transcript upload: {e}")
                        
                # Also log the transcript locally for debugging
                try:
                    with open(f"transcripts/call_{stream_sid}_{int(time.time())}.json", "w") as f:
                        json.dump(payload, f, indent=2)
                    print(f"Saved transcript locally")
                except Exception as e:
                    print(f"Error saving transcript locally: {e}")
            except Exception as e:
                print(f"Error uploading transcript: {e}")

->

        async def upload_transcript():
            """Save the full conversation transcript to MongoDB."""
            nonlocal full_conversation_transcript, structured_conversation
            
            # Ensure we have the final aggregated transcript
            await aggregate_transcriptions()
            
            if not full_conversation_transcript:
                print("No transcript to save.")
                return
                
            try:
                print(f"Saving transcript to MongoDB (length: {len(full_conversation_transcript)})")
                
                # Generate a timestamp for easier querying
                current_time = datetime.now()
                timestamp_str = current_time.strftime("%Y%m%d_%H%M%S")
                
                # Create a collection name based on date and/or stream_sid
                collection_name = f"{MONGO_COLLECTION}_{current_time.strftime('%Y%m')}"
                
                # Prepare the document to store in MongoDB
                document = {
                    "transcript": full_conversation_transcript,
                    "structured_conversation": structured_conversation,
                    "call_id": stream_sid,
                    "timestamp": time.time(),
                    "date_time": current_time,
                    "formatted_time": timestamp_str,
                    # Create a unique identifier that's easy to query
                    "call_identifier": f"{stream_sid}_{timestamp_str}",
                    "metadata": {
                        "duration": time.time() - call_start_time,
                        "silence_warnings_sent": silence_warning_count,
                        "goodbye_detected": goodbye_confirmation_sent,
                        "transcript_segments": len(structured_conversation)
                    }
                }
                
                # Connect to MongoDB and save the transcript
                try:
                    # Create MongoDB client
                    mongo_client = MongoClient(MONGO_URI)
                    db = mongo_client[MONGO_DB]
                    collection = db[collection_name]
                    
                    # Insert the document
                    result = collection.insert_one(document)
                    
                    print(f"Transcript saved to MongoDB successfully. Document ID: {result.inserted_id}")
                    print(f"Collection: {collection_name}, Call Identifier: {document['call_identifier']}")
                    
                    # Close the MongoDB connection
                    mongo_client.close()
                except Exception as e:
                    print(f"Error saving to MongoDB: {e}")
                    
                    # Fallback to local file storage if MongoDB fails
                    try:
                        with open(f"transcripts/transcript_{document['call_identifier']}.json", "w") as f:
                            json.dump(document, f, indent=2)
                        print(f"Fallback: Saved transcript locally as transcript_{document['call_identifier']}.json")
                    except Exception as file_error:
                        print(f"Error saving transcript locally: {file_error}")
                
            except Exception as e:
                print(f"Error in upload_transcript: {e}")
```

Now let's create a simple helper script to manually test MongoDB connection:

Let's create a .env file with MongoDB connection information:

Let's update the README.md to include information about the MongoDB integration:

```markdown
# Voice Stream API

[![Tests](https://github.com/aiready-org/voice-stream-api/actions/workflows/main.yml/badge.svg)](https://github.com/aiready-org/voice-stream-api/actions/workflows/main.yml)
[![codecov](https://codecov.io/gh/aiready-org/voice-stream-api/branch/main/graph/badge.svg)](https://codecov.io/gh/aiready-org/voice-stream-api)
[![Python](https://img.shields.io/badge/python-3.13-blue.svg)](https://www.python.org/downloads/release/python-3132/)

A FastAPI application that provides voice streaming capabilities using OpenAI's Realtime API and Twilio Voice API. The application enables AI-initiated hangup, conversation transcription, and MongoDB transcript storage features.

## Features

1. **AI-Initiated Hangup**: Detects prolonged silence or farewell phrases and gracefully terminates calls.
2. **Conversation Transcription**: Aggregates transcriptions generated during the conversation.
3. **MongoDB Transcript Storage**: Stores call transcripts in MongoDB collections organized by date for easy retrieval.

## Setup and Run

### Local Development

```bash
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# For development, install the project in development mode
pip install -e .

python main.py
```

### Docker Deployment

The application can be deployed using Docker Compose, which includes both the voice assistant and MongoDB:

```bash
# Build and start the containers
docker-compose up -d

# View logs
docker-compose logs -f

# Stop the containers
docker-compose down
```

## Environment Configuration

Create a `.env` file with the following variables:

```
# OpenAI API Key
OPENAI_API_KEY=your_openai_api_key_here

# Server Configuration
PORT=10000
HOST=localhost

# MongoDB Configuration
MONGO_URI=**************************************
MONGO_DB=voice_assistant
MONGO_COLLECTION=transcripts
```

## Testing

The project includes comprehensive test suites for all major features. First, install the test dependencies:

```bash
pip install -r requirements-test.txt
```

Then run the tests:

```bash
# Run all tests
python -m pytest

# Run specific test suites
python -m pytest tests/test_ai_hangup.py
python -m pytest tests/test_transcription.py
python -m pytest tests/test_functional_integration.py

# Run tests with coverage report
python -m pytest --cov=. --cov-config=.coveragerc tests/
```

### Testing MongoDB Connection

A test script is provided to verify MongoDB connectivity:

```bash
python test_mongodb.py
```

### Test Structure

- **test_ai_hangup.py**: Tests for silence detection, farewell phrase detection, and call hangup.
- **test_transcription.py**: Tests for transcription aggregation and event handling.
- **test_functional_integration.py**: Integration tests combining multiple features.

## GitHub Actions

The project uses GitHub Actions for continuous integration. The workflow is defined in `.github/workflows/main.yml` and includes:

1. Runs linting with flake8
2. Executes test suites in separate steps:
   - AI-Initiated Hangup tests
   - Conversation Transcription tests
   - Functional Integration tests
3. Runs all tests together
4. Generates coverage reports and uploads them to Codecov

The workflow is triggered on pushes to the `main` branch and on pull requests to any branch.

## Ngrok Ingress

Setup ngrok and run for example `ngrok http --url=sharp-only-chimp.ngrok-free.app PORT` where `PORT` is uvicorn's port.

## Twilio Dev Phone

[Dev-Phone](https://www.twilio.com/docs/labs/dev-phone)

```
Current number: ****** 254 4652
```

## MongoDB Transcript Storage

Call transcripts are saved to MongoDB with the following structure:

- Collections are named `transcripts_YYYY-MM` to organize by month
- Each transcript includes:
  - Raw transcript text
  - Structured conversation with speaker roles
  - Call ID and timestamps
  - Unique call identifier in the format `{stream_sid}_{timestamp}`
  - Metadata including call duration and indicators

Transcripts can be queried using the call identifier, date range, or full-text search on transcript content.
```

Finally, we need to update the `hang_up_call` function to use our new upload function:

```python
                # Wait a moment for the message to be processed
                await asyncio.sleep(2)
                
                # Upload the transcript before hanging up
                await upload_transcript()

->

                # Wait a moment for the message to be processed
                await asyncio.sleep(2)
                
                # Save the transcript to MongoDB before hanging up
                await upload_transcript()
```

Let's make the application run the MongoDB test during startup to verify connectivity:

```python
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=PORT)

->

async def test_mongodb_connection():
    """Test MongoDB connection on startup."""
    try:
        # Create MongoDB client
        mongo_client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=5000)
        
        # Test connection by getting server info
        server_info = mongo_client.server_info()
        print(f"Connected to MongoDB server version: {server_info.get('version')}")
        print(f"Using database: {MONGO_DB}, collection base: {MONGO_COLLECTION}")
        
        # Close the connection
        mongo_client.close()
        return True
    except Exception as e:
        print(f"Warning: MongoDB connection failed: {e}")
        print("Transcripts will fallback to local file storage only")
        return False

if __name__ == "__main__":
    import uvicorn
    
    # Test MongoDB connection on startup
    import asyncio
    asyncio.run(test_mongodb_connection())
    
    # Start the FastAPI application
    uvicorn.run(app, host="0.0.0.0", port=PORT)
```

Now let's update our .env.example file:

Let's create an .env.example file:

Let's run the test script to verify our MongoDB connection setup works:

It seems like port 27017 is already in use. Let's modify our docker-compose.yml to use a different port for MongoDB:

```dockercompose
version: '3.8'

services:
  mongodb:
    image: mongo:latest
    ports:
      - "27018:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
    restart: always
    
  voice-assistant:
    build: .
    ports:
      - "10000:10000"
    environment:
      - MONGO_URI=**************************************
      - MONGO_DB=voice_assistant
      - MONGO_COLLECTION=transcripts
    depends_on:
      - mongodb
    volumes:
      - ./transcripts:/app/transcripts

volumes:
  mongodb_data:
```

Let's also update the .env file to use the new port:

Let's update the test_mongodb.py script to use the new port:

```python
#!/usr/bin/env python
import os
from pymongo import MongoClient
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

# MongoDB Configuration
MONGO_URI = os.getenv('MONGO_URI', '****************************************')
MONGO_DB = os.getenv('MONGO_DB', 'voice_assistant')
MONGO_COLLECTION = os.getenv('MONGO_COLLECTION', 'transcripts')

def test_mongodb_connection():
    """Test connecting to MongoDB and inserting a test document."""
    try:
        # Generate a timestamp for collection name
        current_time = datetime.now()
        timestamp_str = current_time.strftime("%Y%m%d_%H%M%S")
        collection_name = f"{MONGO_COLLECTION}_{current_time.strftime('%Y%m')}"
        
        print(f"Connecting to MongoDB at {MONGO_URI}")
        print(f"Using database: {MONGO_DB}, collection: {collection_name}")
        
        # Create MongoDB client
        mongo_client = MongoClient(MONGO_URI)
        
        # Test connection
        server_info = mongo_client.server_info()
        print(f"Connected to MongoDB server version: {server_info.get('version')}")
        
        # Get database
        db = mongo_client[MONGO_DB]
        
        # Get collection
        collection = db[collection_name]
        
        # Insert test document
        test_doc = {
            "test": True,
            "timestamp": datetime.now(),
            "message": "This is a test document",
            "test_id": f"test_{timestamp_str}"
        }
        
        result = collection.insert_one(test_doc)
        print(f"Test document inserted with ID: {result.inserted_id}")
        
        # Retrieve the document to confirm
        retrieved = collection.find_one({"test_id": test_doc["test_id"]})
        if retrieved:
            print(f"Successfully retrieved test document: {retrieved.get('test_id')}")
        else:
            print("Failed to retrieve test document")
            
        # Close the connection
        mongo_client.close()
        print("MongoDB connection closed")
        return True
        
    except Exception as e:
        print(f"Error testing MongoDB connection: {e}")
        return False

if __name__ == "__main__":
    if test_mongodb_connection():
        print("MongoDB connection test successful!")
    else:
        print("MongoDB connection test failed!")
```

I'll change the MongoDB port back to 27017 in all the relevant files.

```dockercompose
version: '3.8'

services:
  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
    restart: always
    
  voice-assistant:
    build: .
    ports:
      - "10000:10000"
    environment:
      - MONGO_URI=**************************************
      - MONGO_DB=voice_assistant
      - MONGO_COLLECTION=transcripts
    depends_on:
      - mongodb
    volumes:
      - ./transcripts:/app/transcripts

volumes:
  mongodb_data:
```

```python
#!/usr/bin/env python
import os
from pymongo import MongoClient
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

# MongoDB Configuration
MONGO_URI = os.getenv('MONGO_URI', '****************************************')
MONGO_DB = os.getenv('MONGO_DB', 'voice_assistant')
MONGO_COLLECTION = os.getenv('MONGO_COLLECTION', 'transcripts')

def test_mongodb_connection():
    """Test connecting to MongoDB and inserting a test document."""
    try:
        # Generate a timestamp for collection name
        current_time = datetime.now()
        timestamp_str = current_time.strftime("%Y%m%d_%H%M%S")
        collection_name = f"{MONGO_COLLECTION}_{current_time.strftime('%Y%m')}"
        
        print(f"Connecting to MongoDB at {MONGO_URI}")
        print(f"Using database: {MONGO_DB}, collection: {collection_name}")
        
        # Create MongoDB client
        mongo_client = MongoClient(MONGO_URI)
        
        # Test connection
        server_info = mongo_client.server_info()
        print(f"Connected to MongoDB server version: {server_info.get('version')}")
        
        # Get database
        db = mongo_client[MONGO_DB]
        
        # Get collection
        collection = db[collection_name]
        
        # Insert test document
        test_doc = {
            "test": True,
            "timestamp": datetime.now(),
            "message": "This is a test document",
            "test_id": f"test_{timestamp_str}"
        }
        
        result = collection.insert_one(test_doc)
        print(f"Test document inserted with ID: {result.inserted_id}")
        
        # Retrieve the document to confirm
        retrieved = collection.find_one({"test_id": test_doc["test_id"]})
        if retrieved:
            print(f"Successfully retrieved test document: {retrieved.get('test_id')}")
        else:
            print("Failed to retrieve test document")
            
        # Close the connection
        mongo_client.close()
        print("MongoDB connection closed")
        return True
        
    except Exception as e:
        print(f"Error testing MongoDB connection: {e}")
        return False

if __name__ == "__main__":
    if test_mongodb_connection():
        print("MongoDB connection test successful!")
    else:
        print("MongoDB connection test failed!")
```

Now let's try running the MongoDB container with the port set back to 27017:

It looks like port 27017 is already in use on your system. This suggests you already have a MongoDB instance running, which is good! Let's check if we can connect to it:

It seems like the MongoDB server is running, but our authentication details are incorrect. Let's try to connect without authentication first to see if it works:

```python
#!/usr/bin/env python
import os
from pymongo import MongoClient
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

# MongoDB Configuration
MONGO_URI = os.getenv('MONGO_URI', 'mongodb://localhost:27017')
MONGO_DB = os.getenv('MONGO_DB', 'voice_assistant')
MONGO_COLLECTION = os.getenv('MONGO_COLLECTION', 'transcripts')

def test_mongodb_connection():
    """Test connecting to MongoDB and inserting a test document."""
    try:
        # Generate a timestamp for collection name
        current_time = datetime.now()
        timestamp_str = current_time.strftime("%Y%m%d_%H%M%S")
        collection_name = f"{MONGO_COLLECTION}_{current_time.strftime('%Y%m')}"
        
        print(f"Connecting to MongoDB at {MONGO_URI}")
        print(f"Using database: {MONGO_DB}, collection: {collection_name}")
        
        # Create MongoDB client
        mongo_client = MongoClient(MONGO_URI)
        
        # Test connection
        server_info = mongo_client.server_info()
        print(f"Connected to MongoDB server version: {server_info.get('version')}")
        
        # Get database
        db = mongo_client[MONGO_DB]
        
        # Get collection
        collection = db[collection_name]
        
        # Insert test document
        test_doc = {
            "test": True,
            "timestamp": datetime.now(),
            "message": "This is a test document",
            "test_id": f"test_{timestamp_str}"
        }
        
        result = collection.insert_one(test_doc)
        print(f"Test document inserted with ID: {result.inserted_id}")
        
        # Retrieve the document to confirm
        retrieved = collection.find_one({"test_id": test_doc["test_id"]})
        if retrieved:
            print(f"Successfully retrieved test document: {retrieved.get('test_id')}")
        else:
            print("Failed to retrieve test document")
            
        # Close the connection
        mongo_client.close()
        print("MongoDB connection closed")
        return True
        
    except Exception as e:
        print(f"Error testing MongoDB connection: {e}")
        return False

if __name__ == "__main__":
    if test_mongodb_connection():
        print("MongoDB connection test successful!")
    else:
        print("MongoDB connection test failed!")
```

