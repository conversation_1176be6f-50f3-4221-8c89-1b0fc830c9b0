#!/usr/bin/env python
"""
System Test Script for Voice Assistant

This script tests the various endpoints of the voice assistant system
to ensure everything is working properly.
"""

import requests
import json
import sys
import time

# Configuration
VOICE_ASSISTANT_URL = "http://localhost:10500"
REPORTS_UI_URL = "http://localhost:10501"

def test_endpoint(url, expected_status=200, description=""):
    """Test an endpoint and return the result."""
    try:
        print(f"Testing {url}... ", end="")
        response = requests.get(url, timeout=5)
        
        if response.status_code == expected_status:
            print(f"SUCCESS ({response.status_code})")
            return True, response
        else:
            print(f"FAILED - Expected {expected_status}, got {response.status_code}")
            return False, response
    except Exception as e:
        print(f"ERROR - {str(e)}")
        return False, None

def main():
    """Run all tests."""
    print("\n========================================")
    print("Voice Assistant System Test")
    print("========================================\n")
    
    all_tests_passed = True
    
    # Test voice assistant endpoints
    print("\nTesting Voice Assistant API endpoints:")
    print("----------------------------------------")
    
    tests = [
        (f"{VOICE_ASSISTANT_URL}/health", 200, "Health check endpoint"),
        (f"{VOICE_ASSISTANT_URL}/endpoints", 200, "Endpoints list"),
        (f"{VOICE_ASSISTANT_URL}/", 200, "Root endpoint")
    ]
    
    for url, expected_status, description in tests:
        result, response = test_endpoint(url, expected_status, description)
        all_tests_passed = all_tests_passed and result
    
    # Test reports UI endpoints
    print("\nTesting Reports UI endpoints:")
    print("----------------------------------------")
    
    tests = [
        (f"{REPORTS_UI_URL}/health", 200, "Health check endpoint"),
        (f"{REPORTS_UI_URL}/reports/", 200, "Reports dashboard")
    ]
    
    for url, expected_status, description in tests:
        result, response = test_endpoint(url, expected_status, description)
        all_tests_passed = all_tests_passed and result
    
    # Print summary
    print("\n========================================")
    if all_tests_passed:
        print("✅ All tests PASSED!")
    else:
        print("❌ Some tests FAILED!")
    print("========================================\n")
    
    return 0 if all_tests_passed else 1

if __name__ == "__main__":
    sys.exit(main())