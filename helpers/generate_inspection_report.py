#!/usr/bin/env python
"""
End-to-end script to generate inspection reports from MongoDB transcripts.
"""
import os
import json
import logging
import argparse
from datetime import datetime
from pathlib import Path

from transcript_processor import extract_inspection_data, format_inspection_data_for_template
from template_renderer import render_inspection_data
from pdf_generator import generate_pdf_from_html

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Default directories
REPORTS_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'reports')
os.makedirs(REPORTS_DIR, exist_ok=True)


def generate_report(
    transcript_id=None,
    output_dir=REPORTS_DIR,
    save_intermediate=False,
    template_name=None
):
    """
    Generate an inspection report from a transcript.
    
    Args:
        transcript_id: ID of the transcript to process (uses most recent if None)
        output_dir: Directory to save the report
        save_intermediate: Whether to save intermediate files (JSON and HTML)
        template_name: Name of the template file to use
        
    Returns:
        Path to the generated PDF file
    """
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = f"inspection_report_{timestamp}"
        if transcript_id:
            base_filename = f"inspection_report_{transcript_id}_{timestamp}"
        
        # Make sure output directory exists
        os.makedirs(output_dir, exist_ok=True)
        
        # Step 1: Extract inspection data from the transcript
        logger.info(f"Extracting inspection data from transcript {transcript_id or 'most recent'}")
        inspection_data = extract_inspection_data(transcript_id)
        
        # Step 2: Format the data for the template
        logger.info("Formatting inspection data for template")
        template_data = format_inspection_data_for_template(inspection_data)
        
        # Save JSON if requested
        if save_intermediate:
            json_file = os.path.join(output_dir, f"{base_filename}.json")
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(template_data, f, indent=2)
            logger.info(f"Saved inspection data to {json_file}")
        
        # Step 3: Render the HTML
        logger.info(f"Rendering HTML using template {template_name or 'default'}")
        html_file = os.path.join(output_dir, f"{base_filename}.html") if save_intermediate else None
        rendered_html = render_inspection_data(
            template_data,
            template_name=template_name,
            output_file=html_file
        )
        
        # Step 4: Generate the PDF
        logger.info("Generating PDF from HTML")
        pdf_file = os.path.join(output_dir, f"{base_filename}.pdf")
        output_pdf = generate_pdf_from_html(rendered_html, output_file=pdf_file)
        
        logger.info(f"Report generation complete: {output_pdf}")
        return output_pdf
    
    except Exception as e:
        logger.error(f"Error generating report: {e}")
        raise


def main():
    parser = argparse.ArgumentParser(description="Generate inspection reports from transcripts")
    parser.add_argument("--transcript-id", help="ID of the transcript to process (uses most recent if not specified)")
    parser.add_argument("--output-dir", default=REPORTS_DIR, help=f"Output directory (default: {REPORTS_DIR})")
    parser.add_argument("--template", help="Template file to use")
    parser.add_argument("--save-intermediate", action="store_true", help="Save intermediate JSON and HTML files")
    args = parser.parse_args()
    
    try:
        pdf_path = generate_report(
            transcript_id=args.transcript_id,
            output_dir=args.output_dir,
            save_intermediate=args.save_intermediate,
            template_name=args.template
        )
        print(f"Report generated: {pdf_path}")
    except Exception as e:
        logger.error(f"Error in main: {e}")
        raise


if __name__ == "__main__":
    main()