import uvicorn
from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, Optional
import json
from datetime import datetime
import os

# Create the FastAPI app
app = FastAPI(title="Mock Transcript Server")

# Model for transcript data
class TranscriptData(BaseModel):
    transcript: str
    call_id: str
    timestamp: float
    metadata: Optional[Dict[str, Any]] = None

# In-memory storage for received transcripts
received_transcripts = []

# Directory for storing transcript files
TRANSCRIPT_DIR = "transcripts"
os.makedirs(TRANSCRIPT_DIR, exist_ok=True)

@app.post("/transcript", status_code=200)
async def receive_transcript(data: TranscriptData):
    """
    Endpoint to receive and store transcripts
    """
    # Add to in-memory storage
    received_transcripts.append(data.dict())
    
    # Create a unique filename based on timestamp and call_id
    timestamp_str = datetime.fromtimestamp(data.timestamp).strftime("%Y%m%d_%H%M%S")
    filename = f"{TRANSCRIPT_DIR}/transcript_{timestamp_str}_{data.call_id}.json"
    
    # Save to file
    with open(filename, "w") as f:
        json.dump(data.dict(), f, indent=2)
    
    print(f"Received transcript from call {data.call_id}, length: {len(data.transcript)}")
    print(f"Saved to file: {filename}")
    
    return {"status": "success", "message": "Transcript received and stored"}

@app.get("/transcripts", status_code=200)
async def list_transcripts():
    """
    Endpoint to list all received transcripts
    """
    return {
        "count": len(received_transcripts),
        "transcripts": [
            {
                "call_id": t["call_id"],
                "timestamp": t["timestamp"],
                "length": len(t["transcript"]),
                "preview": t["transcript"][:100] + "..." if len(t["transcript"]) > 100 else t["transcript"]
            }
            for t in received_transcripts
        ]
    }

@app.get("/transcript/{call_id}", status_code=200)
async def get_transcript(call_id: str):
    """
    Endpoint to get a specific transcript by call_id
    """
    for transcript in received_transcripts:
        if transcript["call_id"] == call_id:
            return transcript
    
    raise HTTPException(status_code=404, detail=f"Transcript for call {call_id} not found")

if __name__ == "__main__":
    # Run the server on port 8001 (different from the main app)
    print("Starting mock transcript server on http://localhost:8001")
    uvicorn.run(app, host="0.0.0.0", port=8001)
