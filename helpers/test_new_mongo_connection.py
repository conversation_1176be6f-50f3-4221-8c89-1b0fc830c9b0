#!/usr/bin/env python3
"""
Test script for the new MongoDB connection
"""

import os
import sys
from dotenv import load_dotenv
from pymongo import MongoClient
from client_data_manager import ClientDataManager

# Load environment variables
load_dotenv()

def test_mongodb_connection():
    """Test the new MongoDB connection."""
    print("🔗 Testing MongoDB Connection")
    print("=" * 50)
    
    # Get configuration
    mongo_uri = os.getenv('MONGO_URI')
    default_db = os.getenv('DEFAULT_CLIENT_DB', 'Client_Activities')
    
    print(f"MongoDB URI: {mongo_uri[:30]}...")  # Show partial URI for security
    print(f"Default Database: {default_db}")
    
    # Test basic connection
    try:
        print("\n📡 Testing basic MongoDB connection...")
        client = MongoClient(mongo_uri, serverSelectionTimeoutMS=10000)
        
        # Test server info
        server_info = client.server_info()
        print(f"✅ Connected successfully!")
        print(f"   MongoDB Version: {server_info.get('version', 'Unknown')}")
        
        # List databases
        db_list = client.list_database_names()
        print(f"   Available Databases: {len(db_list)}")
        for db_name in db_list[:5]:  # Show first 5 databases
            print(f"     • {db_name}")
        if len(db_list) > 5:
            print(f"     ... and {len(db_list) - 5} more")
        
        client.close()
        
    except Exception as e:
        print(f"❌ Basic connection failed: {e}")
        return False
    
    # Test Client Data Manager
    try:
        print(f"\n🏗️ Testing Client Data Manager...")
        manager = ClientDataManager(mongo_uri, default_db)
        
        if manager.connect():
            print("✅ Client Data Manager connected successfully!")
            
            # Test database operations
            stats = manager.get_database_stats()
            print(f"   Database: {stats.get('database_name', 'Unknown')}")
            print(f"   Total Collections: {stats.get('total_clients', 0)}")
            print(f"   Database Size: {stats.get('total_size_mb', 0)} MB")
            
            # List existing clients
            clients = manager.list_all_clients()
            print(f"   Existing Clients: {len(clients)}")
            if clients:
                print("   Client Emails:")
                for email in clients[:3]:  # Show first 3 clients
                    print(f"     • {email}")
                if len(clients) > 3:
                    print(f"     ... and {len(clients) - 3} more")
            
            manager.disconnect()
            
        else:
            print("❌ Client Data Manager connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Client Data Manager test failed: {e}")
        return False
    
    print(f"\n🎉 Connection test completed successfully!")
    return True

def test_environment_variables():
    """Test that all required environment variables are set."""
    print("\n🔧 Testing Environment Variables")
    print("=" * 50)
    
    required_vars = ['MONGO_URI', 'OPENAI_API_KEY']
    optional_vars = ['DEFAULT_CLIENT_DB', 'PORT', 'HOST']
    
    all_good = True
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            if var == 'MONGO_URI':
                print(f"✅ {var}: {value[:30]}...")  # Partial for security
            elif var == 'OPENAI_API_KEY':
                print(f"✅ {var}: {'*' * 20}...")  # Masked for security
            else:
                print(f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: Not set")
            all_good = False
    
    for var in optional_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {value}")
        else:
            print(f"⚠️ {var}: Not set (using default)")
    
    return all_good

if __name__ == "__main__":
    print("🚀 MongoDB Connection Test Suite")
    print("=" * 60)
    
    # Test environment variables first
    if not test_environment_variables():
        print("\n❌ Environment variables test failed. Please check your .env file.")
        sys.exit(1)
    
    # Test MongoDB connection
    if test_mongodb_connection():
        print("\n🎉 All tests passed! Your MongoDB connection is ready.")
        sys.exit(0)
    else:
        print("\n❌ Connection tests failed. Please check your MongoDB configuration.")
        sys.exit(1)