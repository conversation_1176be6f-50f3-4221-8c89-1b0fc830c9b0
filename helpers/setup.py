from setuptools import setup, find_packages

setup(
    name="voice-stream-api",
    version="0.1.0",
    description="A FastAPI application for voice streaming with AI capabilities",
    author="AIReady Organization",
    author_email="<EMAIL>",
    packages=find_packages(),
    install_requires=[
        "fastapi>=0.110.0",
        "uvicorn>=0.29.0",
        "websockets>=12.0",
        "python-dotenv>=1.0.1",
        "httpx>=0.27.0",
    ],
    extras_require={
        "dev": [
            "pytest>=8.3.5",
            "pytest-asyncio>=0.25.3",
            "pytest-cov>=4.1.0",
            "flake8>=7.0.0",
        ],
    },
    python_requires=">=3.10",
)
