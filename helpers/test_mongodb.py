#!/usr/bin/env python
import os
from pymongo import MongoClient
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

# MongoDB Configuration
MONGO_URI = os.getenv('MONGO_URI', '****************************************')
MONGO_DB = os.getenv('<PERSON><PERSON><PERSON><PERSON>_DB', 'voice_assistant')
MONGO_COLLECTION = os.getenv('MONGO_COLLECTION', 'transcripts')

def test_mongodb_connection():
    """Test connecting to MongoDB and inserting a test document."""
    try:
        # Generate a timestamp for collection name
        current_time = datetime.now()
        timestamp_str = current_time.strftime("%Y%m%d_%H%M%S")
        collection_name = f"{MONGO_COLLECTION}_{current_time.strftime('%Y%m')}"
        
        print(f"Connecting to MongoDB at {MONGO_URI}")
        print(f"Using database: {MONGO_DB}, collection: {collection_name}")
        
        # Create MongoDB client
        mongo_client = MongoClient(MONGO_URI)
        
        # Test connection
        server_info = mongo_client.server_info()
        print(f"Connected to MongoDB server version: {server_info.get('version')}")
        
        # Get database
        db = mongo_client[MONGO_DB]
        
        # Get collection
        collection = db[collection_name]
        
        # Insert test document
        test_doc = {
            "test": True,
            "timestamp": datetime.now(),
            "message": "This is a test document",
            "test_id": f"test_{timestamp_str}"
        }
        
        result = collection.insert_one(test_doc)
        print(f"Test document inserted with ID: {result.inserted_id}")
        
        # Retrieve the document to confirm
        retrieved = collection.find_one({"test_id": test_doc["test_id"]})
        if retrieved:
            print(f"Successfully retrieved test document: {retrieved.get('test_id')}")
        else:
            print("Failed to retrieve test document")
            
        # Close the connection
        mongo_client.close()
        print("MongoDB connection closed")
        return True
        
    except Exception as e:
        print(f"Error testing MongoDB connection: {e}")
        return False

if __name__ == "__main__":
    if test_mongodb_connection():
        print("MongoDB connection test successful!")
    else:
        print("MongoDB connection test failed!")