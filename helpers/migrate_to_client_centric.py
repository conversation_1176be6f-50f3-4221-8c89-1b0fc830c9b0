#!/usr/bin/env python3
"""
Migration Script: Legacy to Client-Centric Architecture
Migrates existing transcript data to the new email-based collection structure.
"""

import os
import sys
import json
import re
import time
from datetime import datetime
from pymongo import MongoClient
from client_data_manager import ClientDataManager
from dotenv import load_dotenv

load_dotenv()

# Configuration
MONGO_URI = os.getenv('MONGO_URI', '****************************************')
OLD_DB = os.getenv('OLD_DB', 'voice_assistant')  # Legacy database name
NEW_DB = os.getenv('DEFAULT_CLIENT_DB', 'Client_Activities')  # New client-centric database
OLD_COLLECTION_PREFIX = 'transcripts'

def extract_email_from_transcript(transcript_text: str, structured_conversation: list) -> str:
    """Extract email from transcript content."""
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    
    # First try structured conversation
    for item in structured_conversation:
        content = item.get("content", "")
        if "email" in content.lower() and "@" in content:
            emails = re.findall(email_pattern, content, re.IGNORECASE)
            if emails:
                return emails[0].lower()
    
    # Then try full transcript
    if "@" in transcript_text:
        emails = re.findall(email_pattern, transcript_text, re.IGNORECASE)
        if emails:
            return emails[0].lower()
    
    return None

def extract_client_info(structured_conversation: list) -> dict:
    """Extract client information from structured conversation."""
    client_info = {
        "name": "Unknown Customer",
        "work_order_number": None,
        "vehicle_info": "Unknown Vehicle",
        "vehicle_type": None,
        "inspector_name": None
    }
    
    for item in structured_conversation:
        content = item.get("content", "").lower()
        
        if "customer" in content and "name" in content and ":" in content:
            parts = content.split(":")
            if len(parts) > 1:
                client_info["name"] = parts[1].strip().title()
        
        if "work order" in content and ":" in content:
            parts = content.split(":")
            if len(parts) > 1:
                client_info["work_order_number"] = parts[1].strip()
        
        if "vehicle" in content and ("make" in content or "model" in content) and ":" in content:
            parts = content.split(":")
            if len(parts) > 1:
                client_info["vehicle_info"] = parts[1].strip().title()
        
        if "vehicle" in content and "type" in content:
            for vehicle_type in ["Light-Duty", "Power-Unit", "Tractor", "Trailer", "Liftgate", "Forklift", "Hy-Rail"]:
                if vehicle_type.lower() in content:
                    client_info["vehicle_type"] = vehicle_type
                    break
    
    return client_info

def migrate_legacy_data():
    """Migrate legacy transcript data to client-centric collections."""
    print("🚀 Starting migration to client-centric architecture...")
    
    # Connect to MongoDB
    try:
        mongo_client = MongoClient(MONGO_URI)
        old_db = mongo_client[OLD_DB]
        print(f"✅ Connected to legacy database: {OLD_DB}")
    except Exception as e:
        print(f"❌ Failed to connect to MongoDB: {e}")
        return False
    
    # Initialize client data manager
    try:
        client_manager = ClientDataManager(MONGO_URI, NEW_DB)
        if not client_manager.connect():
            print(f"❌ Failed to connect to new database: {NEW_DB}")
            return False
        print(f"✅ Connected to new database: {NEW_DB}")
    except Exception as e:
        print(f"❌ Failed to initialize client data manager: {e}")
        return False
    
    # Find all legacy transcript collections
    collection_names = old_db.list_collection_names()
    transcript_collections = [name for name in collection_names if name.startswith(OLD_COLLECTION_PREFIX)]
    
    print(f"📊 Found {len(transcript_collections)} legacy transcript collections")
    
    # Migration statistics
    stats = {
        "total_documents": 0,
        "migrated_documents": 0,
        "documents_with_email": 0,
        "documents_without_email": 0,
        "unique_clients": set(),
        "errors": []
    }
    
    # Process each collection
    for collection_name in transcript_collections:
        print(f"\n📁 Processing collection: {collection_name}")
        collection = old_db[collection_name]
        
        documents = list(collection.find())
        print(f"   Found {len(documents)} documents")
        
        for doc in documents:
            stats["total_documents"] += 1
            
            try:
                # Extract email and client info
                transcript = doc.get("transcript", "")
                structured_conversation = doc.get("structured_conversation", [])
                
                client_email = extract_email_from_transcript(transcript, structured_conversation)
                
                if client_email:
                    stats["documents_with_email"] += 1
                    stats["unique_clients"].add(client_email)
                    
                    # Extract additional client information
                    client_info = extract_client_info(structured_conversation)
                    
                    # Prepare transcript document for new structure
                    transcript_doc = {
                        "transcript": transcript,
                        "structured_conversation": structured_conversation,
                        "call_id": doc.get("call_id"),
                        "timestamp": doc.get("timestamp", time.time()),
                        "date_time": doc.get("date_time", datetime.now()),
                        "formatted_time": doc.get("formatted_time"),
                        "call_identifier": doc.get("call_identifier"),
                        "client_name": client_info["name"],
                        "work_order_number": client_info["work_order_number"],
                        "vehicle_info": client_info["vehicle_info"],
                        "metadata": doc.get("metadata", {}),
                        "legacy_collection": collection_name,
                        "legacy_id": str(doc.get("_id")),
                        "migrated_at": datetime.now()
                    }
                    
                    # Add transcript to client collection
                    success = client_manager.add_transcript(client_email, transcript_doc)
                    
                    if success:
                        stats["migrated_documents"] += 1
                        
                        # If there's inspection data, add it too
                        if doc.get("report_path") or doc.get("report_gridfs_id"):
                            inspection_doc = {
                                "call_identifier": doc.get("call_identifier"),
                                "report_path": doc.get("report_path"),
                                "report_gridfs_id": doc.get("report_gridfs_id"),
                                "customer_name": client_info["name"],
                                "vehicle_info": client_info["vehicle_info"],
                                "work_order_number": client_info["work_order_number"],
                                "vehicle_type": client_info["vehicle_type"] or "Unknown",
                                "inspector_name": client_info["inspector_name"] or "AI Assistant",
                                "inspection_date_vehicle": doc.get("date_time", datetime.now()).strftime("%Y-%m-%d"),
                                "legacy_collection": collection_name,
                                "legacy_id": str(doc.get("_id")),
                                "migrated_at": datetime.now()
                            }
                            client_manager.add_inspection(client_email, inspection_doc)
                        
                        print(f"   ✅ Migrated document for {client_email}")
                    else:
                        stats["errors"].append(f"Failed to migrate document {doc.get('_id')} for {client_email}")
                        print(f"   ❌ Failed to migrate document for {client_email}")
                else:
                    stats["documents_without_email"] += 1
                    print(f"   ⚠️  No email found in document {doc.get('call_identifier', doc.get('_id'))}")
                    
            except Exception as e:
                error_msg = f"Error processing document {doc.get('_id')}: {str(e)}"
                stats["errors"].append(error_msg)
                print(f"   ❌ {error_msg}")
    
    # Print migration summary
    print("\n" + "="*60)
    print("📊 MIGRATION SUMMARY")
    print("="*60)
    print(f"Total documents processed: {stats['total_documents']}")
    print(f"Documents migrated: {stats['migrated_documents']}")
    print(f"Documents with email: {stats['documents_with_email']}")
    print(f"Documents without email: {stats['documents_without_email']}")
    print(f"Unique clients created: {len(stats['unique_clients'])}")
    print(f"Errors encountered: {len(stats['errors'])}")
    
    if stats['unique_clients']:
        print(f"\n👥 Migrated clients:")
        for email in sorted(stats['unique_clients']):
            print(f"   • {email}")
    
    if stats['errors']:
        print(f"\n❌ Errors:")
        for error in stats['errors'][:10]:  # Show first 10 errors
            print(f"   • {error}")
        if len(stats['errors']) > 10:
            print(f"   ... and {len(stats['errors']) - 10} more errors")
    
    # Cleanup
    client_manager.disconnect()
    mongo_client.close()
    
    print(f"\n🎉 Migration completed!")
    return True

def verify_migration():
    """Verify the migration by checking client data."""
    print("\n🔍 Verifying migration...")
    
    try:
        client_manager = ClientDataManager(MONGO_URI, NEW_DB)
        if not client_manager.connect():
            print("❌ Failed to connect for verification")
            return False
        
        clients = client_manager.list_all_clients()
        print(f"✅ Found {len(clients)} clients in new database")
        
        for email in clients[:5]:  # Check first 5 clients
            profile = client_manager.get_client_profile(email)
            activities = client_manager.get_client_activities(email, limit=5)
            
            print(f"\n📧 {email}:")
            print(f"   Total calls: {profile.get('activity_summary', {}).get('total_calls', 0)}")
            print(f"   Total transcripts: {profile.get('activity_summary', {}).get('total_transcripts', 0)}")
            print(f"   Recent activities: {len(activities)}")
        
        client_manager.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

if __name__ == "__main__":
    print("🔄 Legacy to Client-Centric Migration Tool")
    print("="*50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "--verify":
        verify_migration()
    else:
        print("This will migrate your existing transcript data to the new client-centric structure.")
        print("⚠️  Make sure to backup your database before proceeding!")
        
        response = input("\nDo you want to proceed with the migration? (yes/no): ")
        if response.lower() in ['yes', 'y']:
            success = migrate_legacy_data()
            if success:
                verify_migration()
        else:
            print("Migration cancelled.")