#!/usr/bin/env python
"""
Test script to process a transcript and generate a PDF report.
"""
import os
import sys
import json
import asyncio
from scripts.generate_inspection_report import generate_inspection_report_from_transcript

async def test_transcript_processing(transcript_file_path):
    """Process a transcript file and generate a report."""
    print(f"Processing transcript file: {transcript_file_path}")
    
    # Load the transcript file
    with open(transcript_file_path, 'r') as f:
        transcript_data = json.load(f)
    
    # Extract the transcript content and call ID
    transcript_content = transcript_data.get("transcript", "")
    call_id = transcript_data.get("call_id", "unknown")
    
    if not transcript_content:
        print("Error: No transcript content found in the file")
        return False
    
    print(f"Transcript loaded with call ID: {call_id}")
    print(f"Transcript length: {len(transcript_content)} characters")
    
    # Generate the report
    pdf_path = await generate_inspection_report_from_transcript(
        transcript_content=transcript_content,
        call_identifier=call_id
    )
    
    if pdf_path:
        print(f"Successfully generated PDF report: {pdf_path}")
        return True
    else:
        print("Failed to generate PDF report")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python test_transcript_processing.py <transcript_file_path>")
        sys.exit(1)
    
    transcript_file = sys.argv[1]
    if not os.path.exists(transcript_file):
        print(f"Error: File not found - {transcript_file}")
        sys.exit(1)
    
    success = asyncio.run(test_transcript_processing(transcript_file))
    
    if success:
        print("Test completed successfully")
        sys.exit(0)
    else:
        print("Test failed")
        sys.exit(1) 