#!/usr/bin/env python
"""
This module extracts structured vehicle inspection data from transcripts in MongoDB
using OpenAI function calling.
"""
import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List

import openai
from pymongo import MongoClient
from pymongo.collection import Collection
from dotenv import load_dotenv

from function_schema import INSPECTION_FUNCTION_SCHEMA, FUNCTION_CALLING_MESSAGES

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Configuration
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
if not OPENAI_API_KEY:
    raise ValueError("Missing OpenAI API key. Please set OPENAI_API_KEY in your .env file.")

MONGO_URI = os.getenv('MONGO_URI', '****************************************')
MONG<PERSON>_DB = os.getenv('<PERSON><PERSON><PERSON><PERSON>_<PERSON>', 'voice_assistant')
MONGO_COLLECTION = os.getenv('MONGO_COLLECTION', 'transcripts')

# Initialize OpenAI client
client = openai.OpenAI(api_key=OPENAI_API_KEY)


def connect_to_mongodb() -> tuple[MongoClient, Any]:
    """Connect to MongoDB and return client and database objects."""
    try:
        mongo_client = MongoClient(MONGO_URI)
        db = mongo_client[MONGO_DB]
        logger.info(f"Connected to MongoDB at {MONGO_URI}")
        return mongo_client, db
    except Exception as e:
        logger.error(f"Error connecting to MongoDB: {e}")
        raise


def get_transcript_collection(db, date: Optional[datetime] = None) -> Collection:
    """Get the appropriate transcript collection for the given date."""
    if date is None:
        date = datetime.now()
    
    collection_name = f"{MONGO_COLLECTION}_{date.strftime('%Y%m')}"
    return db[collection_name]


def get_transcript_by_id(collection: Collection, transcript_id: str) -> Dict[str, Any]:
    """Retrieve a transcript by its ID."""
    transcript = collection.find_one({"call_identifier": transcript_id})
    if not transcript:
        raise ValueError(f"Transcript with ID {transcript_id} not found")
    return transcript


def get_all_transcripts(collection: Collection, limit: int = 100) -> List[Dict[str, Any]]:
    """Retrieve all transcripts from the collection, up to the specified limit."""
    return list(collection.find().limit(limit))


def extract_transcript_content(transcript: Dict[str, Any]) -> str:
    """Extract the conversation content from a transcript document."""
    try:
        # Access the full conversation transcript
        if "full_conversation_transcript" in transcript:
            return transcript["full_conversation_transcript"]
        
        # If structured_conversation exists, reconstruct the transcript
        if "structured_conversation" in transcript and transcript["structured_conversation"]:
            conversation = []
            for item in transcript["structured_conversation"]:
                role = item.get("role", "unknown")
                content = item.get("content", "")
                conversation.append(f"{role.upper()}: {content}")
            return "\n".join(conversation)
        
        # Fallback to user and assistant transcriptions
        if "user_transcriptions" in transcript and "assistant_transcriptions" in transcript:
            combined = []
            user_transcriptions = transcript.get("user_transcriptions", [])
            assistant_transcriptions = transcript.get("assistant_transcriptions", [])
            
            # Interleave the transcriptions as best we can
            max_len = max(len(user_transcriptions), len(assistant_transcriptions))
            for i in range(max_len):
                if i < len(assistant_transcriptions):
                    combined.append(f"ASSISTANT: {assistant_transcriptions[i]}")
                if i < len(user_transcriptions):
                    combined.append(f"USER: {user_transcriptions[i]}")
            
            return "\n".join(combined)
        
        raise ValueError("Could not find transcript content in the document")
    except Exception as e:
        logger.error(f"Error extracting transcript content: {e}")
        raise


def process_transcript_with_openai(transcript_content: str) -> Dict[str, Any]:
    """
    Process the transcript content using OpenAI function calling to extract structured data.
    """
    try:
        messages = FUNCTION_CALLING_MESSAGES.copy()
        messages[1]["content"] = f"Extract vehicle inspection details from the following transcript: {transcript_content}"
        
        tools = [{"type": "function", "function": INSPECTION_FUNCTION_SCHEMA}]
        
        response = client.chat.completions.create(
            model="gpt-4o",  # Use an appropriate model that supports function calling
            messages=messages,
            tools=tools,
            tool_choice={"type": "function", "function": {"name": "extract_vehicle_inspection"}}
        )
        
        tool_call = response.choices[0].message.tool_calls[0]
        if tool_call.function.name == "extract_vehicle_inspection":
            inspection_data = json.loads(tool_call.function.arguments)
            return inspection_data
        
        raise ValueError("Function call did not return inspection data")
    except Exception as e:
        logger.error(f"Error processing transcript with OpenAI: {e}")
        raise


def extract_inspection_data(transcript_id: Optional[str] = None, date: Optional[datetime] = None) -> Dict[str, Any]:
    """
    Extract inspection data from a transcript in MongoDB.
    
    Args:
        transcript_id: The ID of the transcript to process. If None, processes the most recent.
        date: The date to use for the collection name. If None, uses current date.
        
    Returns:
        Dict containing structured inspection data.
    """
    try:
        mongo_client, db = connect_to_mongodb()
        collection = get_transcript_collection(db, date)
        
        if transcript_id:
            transcript = get_transcript_by_id(collection, transcript_id)
        else:
            # Get the most recent transcript if ID not provided
            transcripts = get_all_transcripts(collection, limit=1)
            if not transcripts:
                raise ValueError(f"No transcripts found in collection {collection.name}")
            transcript = transcripts[0]
        
        transcript_content = extract_transcript_content(transcript)
        inspection_data = process_transcript_with_openai(transcript_content)
        
        # Add metadata
        inspection_data["metadata"] = {
            "transcript_id": transcript.get("call_identifier", ""),
            "call_duration": transcript.get("call_duration", 0),
            "processed_at": datetime.now().isoformat(),
            "source_collection": collection.name
        }
        
        mongo_client.close()
        return inspection_data
    
    except Exception as e:
        logger.error(f"Error extracting inspection data: {e}")
        raise


def map_condition_to_status(condition: str) -> tuple[str, str]:
    """
    Map condition descriptions to status classes and text for the HTML template.
    
    Returns:
        Tuple of (status_class, status_text)
    """
    condition = condition.lower()
    
    if any(term in condition for term in ["excellent", "good", "ok", "working", "functional"]):
        return "status-ok", "OK"
    elif any(term in condition for term in ["replaced", "changed", "new"]):
        return "status-changed", "Changed"
    elif any(term in condition for term in ["repair", "bad", "worn", "damaged", "broken", "issue"]):
        return "status-repair", "Needs Repair"
    elif any(term in condition for term in ["na", "n/a", "not applicable"]):
        return "status-na", "N/A"
    else:
        return "status-ok", "OK"


def format_inspection_data_for_template(inspection_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format the inspection data into a structure suitable for the HTML template.
    """
    template_data = {
        "owner": inspection_data.get("customer_info", {}).get("name", "Not provided"),
        "odometer": inspection_data.get("vehicle_info", {}).get("odometer", "Not provided"),
        "vin": inspection_data.get("vehicle_info", {}).get("vin", ""),
        "engine_hours": "",  # Not in our schema, but in the template
        "make_model_year": inspection_data.get("vehicle_info", {}).get("make_model_year", "Not provided"),
        "inspection_date": inspection_data.get("vehicle_info", {}).get("inspection_date", datetime.now().strftime("%Y-%m-%d")),
        "license_plate": inspection_data.get("vehicle_info", {}).get("license_plate", ""),
        "service_level": "Standard",  # Default value
        "inspector_name": inspection_data.get("technician_info", {}).get("name", "Not provided"),
        "inspection_location": "",  # Not in our schema
        "additional_notes": inspection_data.get("final_assessment", {}).get("additional_notes", ""),
        "checklist_items": []
    }
    
    # Build checklist items
    checklist_map = {
        "Brake Pads": inspection_data.get("brakes_and_suspension", {}).get("brake_pads", {}),
        "Brake Rotors": inspection_data.get("brakes_and_suspension", {}).get("brake_rotors", {}),
        "Engine Oil": inspection_data.get("under_the_hood", {}).get("engine_oil", {}),
        "Coolant Level": inspection_data.get("under_the_hood", {}).get("coolant_level", {}),
        "Lights": inspection_data.get("exterior", {}).get("lights", {}),
        "Seat Belts": inspection_data.get("interior", {}).get("seatbelts", {}),
        "Suspension": inspection_data.get("brakes_and_suspension", {}).get("suspension_system", {}),
        "Battery": inspection_data.get("under_the_hood", {}).get("battery", {}),
        "Visible Leaks": inspection_data.get("under_the_hood", {}).get("visible_leaks", {}),
        "Tires": inspection_data.get("exterior", {}).get("tires", {}),
        "Windshield": inspection_data.get("exterior", {}).get("windshield", {}),
        "Front Bumper": inspection_data.get("exterior", {}).get("front_bumper", {}),
        "Body & Paint": inspection_data.get("exterior", {}).get("body_panels_paint", {}),
        "Belts & Hoses": inspection_data.get("under_the_hood", {}).get("belts_hoses", {}),
        "Dashboard Lights": inspection_data.get("interior", {}).get("dashboard_lights", {}),
        "Horn": inspection_data.get("interior", {}).get("horn", {}),
        "Windows & Locks": inspection_data.get("interior", {}).get("windows_locks", {}),
        "Acceleration": inspection_data.get("road_test", {}).get("acceleration", {}),
        "Braking": inspection_data.get("road_test", {}).get("braking", {}),
        "Unusual Sounds": inspection_data.get("road_test", {}).get("unusual_sounds", {})
    }
    
    for name, item_data in checklist_map.items():
        if not item_data:
            continue
            
        condition = item_data.get("condition", "")
        notes = item_data.get("notes", "")
        
        status_class, status_text = map_condition_to_status(condition)
        
        template_data["checklist_items"].append({
            "name": name,
            "status_class": status_class,
            "status_text": status_text,
            "notes": notes or condition
        })
    
    # Add recommended repairs as a checklist item
    recommended_repairs = inspection_data.get("final_assessment", {}).get("recommended_repairs", "")
    if recommended_repairs:
        template_data["checklist_items"].append({
            "name": "Recommended Repairs",
            "status_class": "status-repair",
            "status_text": "Action Required",
            "notes": recommended_repairs
        })
    
    return template_data


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Extract inspection data from a transcript")
    parser.add_argument("--transcript-id", help="ID of the transcript to process")
    parser.add_argument("--output", help="Output file for the extracted data (JSON)")
    args = parser.parse_args()
    
    try:
        inspection_data = extract_inspection_data(args.transcript_id)
        template_data = format_inspection_data_for_template(inspection_data)
        
        if args.output:
            with open(args.output, "w") as f:
                json.dump(template_data, f, indent=2)
            print(f"Inspection data written to {args.output}")
        else:
            print(json.dumps(template_data, indent=2))
    except Exception as e:
        logger.error(f"Error running transcript processor: {e}")
        raise