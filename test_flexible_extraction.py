#!/usr/bin/env python3
"""
Test script for the flexible extraction system.
"""

import asyncio
import json
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from flexible_extraction import FlexibleExtractor

# Test cases with different conversation patterns
TEST_CASES = [
    {
        "name": "Formal Inspection Conversation",
        "transcript": """
Assistant: Workforce AI Assistant.
Assistant: English or Spanish?
User: English
Assistant: What is the unit number?
User: UNIT-12345
Assistant: What is the maker and model?
User: Ford F-150
Assistant: What is the client's name?
User: <PERSON>
Assistant: What is the client's email address?
User: <EMAIL>
Assistant: What does the odometer reading say?
User: 85,000 miles
Assistant: We will be doing a Light-Duty Vehicle inspection
Assistant: Beginning inspection protocol. Door locks?
User: Door locks are working fine
Assistant: Door latches?
User: Good condition
Assistant: Windshield condition?
User: No cracks, clear visibility
Assistant: Brakes?
User: Brakes are in excellent condition
Assistant: Engine oil?
User: Oil level is good, clean oil
Assistant: Thank you, we are done here.
        """,
        "structured_conversation": [
            {"role": "assistant", "content": "Workforce AI Assistant."},
            {"role": "assistant", "content": "English or Spanish?"},
            {"role": "user", "content": "English"},
            {"role": "assistant", "content": "What is the unit number?"},
            {"role": "user", "content": "UNIT-12345"},
            {"role": "assistant", "content": "What is the maker and model?"},
            {"role": "user", "content": "Ford F-150"},
            {"role": "assistant", "content": "What is the client's name?"},
            {"role": "user", "content": "John Smith"},
            {"role": "assistant", "content": "What is the client's email address?"},
            {"role": "user", "content": "<EMAIL>"},
            {"role": "assistant", "content": "What does the odometer reading say?"},
            {"role": "user", "content": "85,000 miles"},
            {"role": "assistant", "content": "We will be doing a Light-Duty Vehicle inspection"},
            {"role": "assistant", "content": "Beginning inspection protocol. Door locks?"},
            {"role": "user", "content": "Door locks are working fine"},
            {"role": "assistant", "content": "Door latches?"},
            {"role": "user", "content": "Good condition"},
            {"role": "assistant", "content": "Windshield condition?"},
            {"role": "user", "content": "No cracks, clear visibility"},
            {"role": "assistant", "content": "Brakes?"},
            {"role": "user", "content": "Brakes are in excellent condition"},
            {"role": "assistant", "content": "Engine oil?"},
            {"role": "user", "content": "Oil level is good, clean oil"},
            {"role": "assistant", "content": "Thank you, we are done here."}
        ]
    },
    {
        "name": "Informal Conversation",
        "transcript": """
Hey, I need to check out this truck. It's a 2018 Chevy Silverado, unit #ABC-789.
The customer is Mike Johnson, email <EMAIL>.
The odometer shows 120,000 miles.
I checked the brakes - they're in good shape.
The tires look worn but still have some life left.
Engine oil is dirty, needs changing soon.
All lights are working properly.
        """,
        "structured_conversation": []
    },
    {
        "name": "Partial Inspection",
        "transcript": """
Assistant: What's the unit number?
User: TRUCK-456
Assistant: Vehicle make and model?
User: Toyota Tacoma 2020
Assistant: How are the brakes?
User: Brakes need some work, pads are worn
Assistant: What about the engine?
User: Engine runs fine, no issues
        """,
        "structured_conversation": [
            {"role": "assistant", "content": "What's the unit number?"},
            {"role": "user", "content": "TRUCK-456"},
            {"role": "assistant", "content": "Vehicle make and model?"},
            {"role": "user", "content": "Toyota Tacoma 2020"},
            {"role": "assistant", "content": "How are the brakes?"},
            {"role": "user", "content": "Brakes need some work, pads are worn"},
            {"role": "assistant", "content": "What about the engine?"},
            {"role": "user", "content": "Engine runs fine, no issues"}
        ]
    },
    {
        "name": "Statement-Based Conversation",
        "transcript": """
This is for unit VAN-123, a 2019 Ford Transit.
The customer is Sarah Wilson, email <EMAIL>.
The brakes are in excellent condition.
The engine oil is clean and at proper level.
The tires are worn and need replacement.
The lights are all functioning correctly.
The doors open and close properly.
        """,
        "structured_conversation": []
    }
]

def test_flexible_extraction():
    """Test the flexible extraction system with various conversation patterns."""
    print("🧪 Testing Flexible Extraction System")
    print("="*80)
    
    extractor = FlexibleExtractor()
    
    for i, test_case in enumerate(TEST_CASES, 1):
        print(f"\n📋 Test Case {i}: {test_case['name']}")
        print("-" * 60)
        
        transcript = test_case['transcript'].strip()
        structured_conv = test_case['structured_conversation']
        
        print(f"📄 Transcript length: {len(transcript)} chars")
        print(f"💬 Structured conversation items: {len(structured_conv)}")
        
        # Extract vehicle information
        vehicle_info = extractor.extract_vehicle_info(transcript, structured_conv)
        print(f"\n🚗 Vehicle Information Extracted:")
        for key, value in vehicle_info.items():
            if value:
                print(f"   {key}: {value}")
        
        # Extract inspection items
        inspection_items = extractor.extract_inspection_items(transcript, structured_conv)
        print(f"\n📋 Inspection Items Extracted ({len(inspection_items)} items):")
        for j, item in enumerate(inspection_items, 1):
            print(f"   {j}. {item.get('item', 'N/A')}")
            print(f"      Status: {item.get('status', 'N/A')}")
            print(f"      Notes: {item.get('notes', 'N/A')[:100]}...")
        
        # Summary
        vehicle_fields_filled = len([v for v in vehicle_info.values() if v])
        print(f"\n📊 Summary:")
        print(f"   Vehicle fields filled: {vehicle_fields_filled}/9")
        print(f"   Inspection items found: {len(inspection_items)}")
        
        # Determine if this would use flexible extraction
        use_flexible = (
            len(inspection_items) > 0 or 
            vehicle_fields_filled >= 3
        )
        print(f"   Would use flexible extraction: {'✅ YES' if use_flexible else '❌ NO (fallback to OpenAI)'}")
        
        print("\n" + "="*80)

def test_with_real_transcripts():
    """Test with real transcript files."""
    print("\n🔍 Testing with Real Transcript Files")
    print("="*80)
    
    transcripts_dir = "transcripts"
    if not os.path.exists(transcripts_dir):
        print(f"❌ Transcripts directory not found: {transcripts_dir}")
        return
    
    extractor = FlexibleExtractor()
    
    # Get a few recent transcript files
    transcript_files = [f for f in os.listdir(transcripts_dir) if f.endswith('.json')]
    transcript_files.sort(reverse=True)  # Most recent first
    
    for filename in transcript_files[:3]:  # Test with 3 most recent
        filepath = os.path.join(transcripts_dir, filename)
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                transcript_data = json.load(f)
            
            print(f"\n📄 Testing with: {filename}")
            print("-" * 60)
            
            transcript = transcript_data.get('transcript', '')
            structured_conv = transcript_data.get('structured_conversation', [])
            
            print(f"📄 Transcript length: {len(transcript)} chars")
            print(f"💬 Structured conversation items: {len(structured_conv)}")
            
            # Extract data
            vehicle_info = extractor.extract_vehicle_info(transcript, structured_conv)
            inspection_items = extractor.extract_inspection_items(transcript, structured_conv)
            
            # Show results
            vehicle_fields_filled = len([v for v in vehicle_info.values() if v])
            print(f"🚗 Vehicle fields filled: {vehicle_fields_filled}/9")
            print(f"📋 Inspection items found: {len(inspection_items)}")
            
            if vehicle_fields_filled > 0:
                print("   Vehicle info found:")
                for key, value in vehicle_info.items():
                    if value:
                        print(f"      {key}: {value}")
            
            if inspection_items:
                print("   Inspection items found:")
                for j, item in enumerate(inspection_items[:3], 1):
                    print(f"      {j}. {item.get('item', 'N/A')}: {item.get('status', 'N/A')}")
            
            # Determine extraction method
            use_flexible = (
                len(inspection_items) > 0 or 
                vehicle_fields_filled >= 3
            )
            print(f"   Would use flexible extraction: {'✅ YES' if use_flexible else '❌ NO'}")
            
        except Exception as e:
            print(f"❌ Error processing {filename}: {e}")

if __name__ == "__main__":
    # Test with predefined test cases
    test_flexible_extraction()
    
    # Test with real transcript files
    test_with_real_transcripts()
    
    print("\n🎉 Flexible extraction testing completed!")
